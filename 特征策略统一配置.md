# 特征选择策略统一配置

## 概述
为确保模型训练、回测和推理的一致性，所有页面都使用相同的特征选择策略配置。

## 统一策略配置

### 最小特征集 (10个核心指标)
- **值**: `minimal`
- **特征数量**: 10
- **描述**: 用于快速训练和测试的最小特征集
- **包含**: 基础价格指标, 核心技术指标

### 推荐特征集 (26个核心特征)
- **值**: `recommended`
- **特征数量**: 26
- **描述**: 经过优化的核心技术指标组合
- **包含**: 布林带, ATR, 随机指标, RSI, MACD, 移动平均

### 增强特征集 (52个特征)
- **值**: `enhanced`
- **特征数量**: 52
- **描述**: 包含所有可用的技术指标和组合信号
- **包含**: 所有基础指标, 组合信号, 市场结构, 波动率指标

### 重要性前15个特征
- **值**: `top_importance`
- **特征数量**: 15
- **描述**: 基于特征重要性分析的前15个特征
- **包含**: 最重要的技术指标, 高预测能力特征

### 自定义特征选择
- **值**: `custom`
- **特征数量**: 可变
- **描述**: 手动选择特定的技术指标组合
- **包含**: 用户自定义选择

## 使用说明

### 模型训练
在模型训练页面选择特征策略时，系统会根据选择的策略生成对应数量和类型的特征。

### 模型回测
回测时必须选择与训练时相同的特征策略，否则会导致特征维度不匹配。

### 模型推理
推理时也必须使用与训练时相同的特征策略，确保输入数据的一致性。

## 技术实现

### HTML选项
```html
<select class="form-select" id="featureSelectionStrategy">
    <option value="minimal">最小特征集 (10个核心指标)</option>
    <option value="recommended">推荐特征集 (26个核心特征)</option>
    <option value="enhanced">增强特征集 (52个特征)</option>
    <option value="top_importance">重要性前15个特征</option>
    <option value="custom">自定义特征选择</option>
</select>
```

### JavaScript映射
```javascript
const strategyNames = {
    'minimal': '最小特征集 (10个核心指标)',
    'recommended': '推荐特征集 (26个核心特征)',
    'enhanced': '增强特征集 (52个特征)',
    'top_importance': '重要性前15个特征',
    'custom': '自定义特征选择',
};
```

## 注意事项

1. **一致性要求**: 训练、回测、推理必须使用相同的特征策略
2. **特征数量**: 不同策略的特征数量不同，模型输入维度会相应变化
3. **兼容性**: 旧模型可能使用旧的策略配置，需要注意兼容性
4. **性能影响**: 特征数量越多，训练时间越长，但可能提高预测精度

## 更新历史

- 2025-08-03: 统一所有页面的特征策略配置
- 修复了不同页面策略不一致的问题
- 确保模型训练和推理的特征匹配
