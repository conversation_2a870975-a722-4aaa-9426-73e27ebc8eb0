#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试增强特征功能
"""

import requests
import time
import json

def test_enhanced_features_debug():
    """调试增强特征功能"""
    print("🧪 调试增强特征功能")
    print("=" * 50)
    
    # 等待应用启动
    time.sleep(3)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        models_data = models_response.json()
        
        test_model = None
        for model in models_data.get('models', []):
            if model.get('status') == 'completed':
                test_model = model
                break
        
        if not test_model:
            print(f"❌ 没有可用模型")
            return False
        
        print(f"✅ 选择测试模型: {test_model['name']}")
        
        # 测试增强特征回测请求（短时间范围）
        print(f"\n🚀 发送增强特征回测请求（短时间范围）...")
        
        backtest_data = {
            'model_id': test_model['id'],
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'start_date': '2025-08-01',  # 测试两天
            'end_date': '2025-08-02',
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 1000,
            'take_profit_pips': 2000,
            'min_confidence': 0.1,  # 降低置信度阈值
            'cliff_brake_enabled': False,
            'trailing_stop_enabled': False,
            # 增强特征配置
            'use_enhanced_features': True,
            'feature_selection_strategy': 'recommended',
            'analyze_feature_importance': True,
            'selected_features': None
        }
        
        print(f"📋 请求数据:")
        print(f"   时间范围: {backtest_data['start_date']} 到 {backtest_data['end_date']}")
        print(f"   使用增强特征: {backtest_data['use_enhanced_features']}")
        print(f"   特征策略: {backtest_data['feature_selection_strategy']}")
        print(f"   最小置信度: {backtest_data['min_confidence']}")
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', 
                               json=backtest_data)
        
        print(f"\n📡 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应数据:")
            print(f"   成功: {result.get('success')}")
            
            if result.get('success'):
                trades = result.get('trades', [])
                stats = result.get('statistics', {})
                
                print(f"   交易数量: {len(trades)}")
                print(f"   统计数据: {len(stats)} 个指标")
                
                # 检查所有交易的增强特征标记
                enhanced_count = 0
                for i, trade in enumerate(trades):
                    if trade.get('enhanced_features_used'):
                        enhanced_count += 1
                        if i < 3:  # 只显示前3笔
                            print(f"   交易{i+1}: 增强特征={trade.get('enhanced_features_used')}, 策略={trade.get('feature_strategy')}")
                
                print(f"   使用增强特征的交易: {enhanced_count}/{len(trades)}")
                
                if enhanced_count == len(trades) and len(trades) > 0:
                    print(f"   ✅ 所有交易都使用了增强特征")
                elif enhanced_count > 0:
                    print(f"   ⚠️ 部分交易使用了增强特征")
                else:
                    print(f"   ❌ 没有交易使用增强特征")
                
                return True
            else:
                print(f"   错误: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误详情: {error_data}")
            except:
                print(f"   响应内容: {response.text[:200]}...")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_enhanced_features_debug()
    
    if success:
        print("\n🎉 增强特征调试测试完成！")
        print("💡 请检查应用服务器日志，查看增强特征推理的详细信息")
    else:
        print("\n❌ 增强特征调试测试失败")
