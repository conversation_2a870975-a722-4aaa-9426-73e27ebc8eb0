#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清理后的页面
"""

import requests
import time

def test_cleanup():
    """测试清理后的页面"""
    print("🧪 测试清理后的AI推理交易页面")
    print("=" * 40)
    
    # 等待应用启动
    time.sleep(3)
    
    try:
        # 测试应用响应
        response = requests.get('http://127.0.0.1:5000', timeout=5)
        print(f"应用状态: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ 应用未正常运行")
            return False
        
        # 登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 获取AI推理交易页面
        page_response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if page_response.status_code != 200:
            print(f"❌ 页面访问失败: {page_response.status_code}")
            return False
        
        content = page_response.text
        
        print(f"✅ 页面访问成功，大小: {len(content):,} 字符")
        
        # 检查推理配置相关元素是否已删除
        print(f"\n🔍 检查推理配置元素删除情况:")
        
        removed_elements = [
            ('模型选择', 'id="modelSelect"'),
            ('交易品种选择', 'id="symbol"'),
            ('时间框架选择', 'id="timeframe"'),
            ('数据点数', 'id="dataPoints"'),
            ('推理模式', 'id="inferenceMode"'),
            ('开始日期', 'id="startDate"'),
            ('结束日期', 'id="endDate"'),
            ('GPU选项', 'id="useGPU"'),
            ('置信度选项', 'id="showConfidence"'),
            ('开始推理按钮', 'id="startInferenceBtn"'),
            ('推理配置按钮', 'id="toggleInferenceBtn"')
        ]
        
        deleted_count = 0
        for name, element in removed_elements:
            if element not in content:
                print(f"✅ {name}: 已删除")
                deleted_count += 1
            else:
                print(f"⚠️ {name}: 仍存在")
        
        print(f"📊 删除进度: {deleted_count}/{len(removed_elements)}")
        
        # 检查实盘交易功能是否保留
        print(f"\n🔍 检查实盘交易功能保留情况:")
        
        trading_elements = [
            ('AI推理交易标题', 'AI推理交易'),
            ('交易手数', 'id="tradingLotSize"'),
            ('最低置信度', 'id="minConfidence"'),
            ('止损点数', 'id="stopLossPips"'),
            ('止盈点数', 'id="takeProfitPips"'),
            ('开始交易按钮', 'id="startTradingBtn"'),
            ('自动交易开关', 'id="enableAutoTrading"'),
            ('动态止损', 'id="enableDynamicSL"'),
            ('追踪止损', 'id="enableTrailingStop"'),
            ('当前持仓', '当前持仓')
        ]
        
        preserved_count = 0
        for name, element in trading_elements:
            if element in content:
                print(f"✅ {name}: 已保留")
                preserved_count += 1
            else:
                print(f"❌ {name}: 缺失")
        
        print(f"📊 保留进度: {preserved_count}/{len(trading_elements)}")
        
        # 检查页面结构
        print(f"\n📊 页面结构:")
        div_count = content.count('<div')
        div_close_count = content.count('</div>')
        card_count = content.count('class="card')
        button_count = content.count('<button')
        
        print(f"   • div标签: {div_count} 开始, {div_close_count} 结束")
        print(f"   • 卡片数量: {card_count}")
        print(f"   • 按钮数量: {button_count}")
        
        if div_count == div_close_count:
            print("✅ div标签匹配")
        else:
            print(f"⚠️ div标签不匹配，差异: {div_count - div_close_count}")
        
        # 保存页面内容用于检查
        with open('cleaned_page_content.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"💾 页面内容已保存到: cleaned_page_content.html")
        
        # 总结
        success = (deleted_count >= len(removed_elements) * 0.8 and 
                  preserved_count >= len(trading_elements) * 0.9 and
                  div_count == div_close_count)
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_cleanup()
    
    if success:
        print("\n🎉 推理配置清理测试通过！")
        print("📋 清理成果:")
        print("   • 推理配置UI已删除")
        print("   • 实盘交易功能完整保留")
        print("   • 页面结构正常")
        print("\n🔄 请刷新浏览器页面查看效果")
    else:
        print("\n⚠️ 清理测试未完全通过")
        print("🔧 可能需要进一步调整")
