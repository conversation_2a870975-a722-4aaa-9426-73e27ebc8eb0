#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复AI推理交易的MT5连接和模型选择问题
"""

def fix_javascript_errors():
    """修复JavaScript错误"""
    print("🔧 修复JavaScript错误")
    print("=" * 40)
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 原始文件大小: {len(content):,} 字符")
        
        # 修复1: 删除null.addEventListener错误
        old_code = '''    // 监听模型选择变化
    null.addEventListener('change', function() {
        const modelId = this.value;
        updateInferenceIntervalForModel(); // 更新推理间隔
        if (modelId) {
            loadModelInfo(modelId);
        } else {
            clearModelInfo();
        }
    });'''
        
        new_code = '''    // 推理配置区域已删除，不需要监听模型选择变化
    // 原来的模型选择监听器已移除'''
        
        if old_code in content:
            content = content.replace(old_code, new_code)
            print("✅ 修复了null.addEventListener错误")
        else:
            print("⚠️ 未找到null.addEventListener错误")
        
        # 修复2: 确保loadTradingModels函数正确实现
        # 检查loadTradingModels函数是否存在且正确
        if 'async function loadTradingModels()' in content:
            print("✅ loadTradingModels函数存在")
        else:
            print("❌ loadTradingModels函数缺失")
        
        # 修复3: 确保checkMT5Connection函数正确实现
        if 'async function checkMT5Connection()' in content:
            print("✅ checkMT5Connection函数存在")
        else:
            print("❌ checkMT5Connection函数缺失")
        
        # 写入修复后的内容
        with open('templates/model_inference.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"📄 修复后文件大小: {len(content):,} 字符")
        print("✅ JavaScript错误修复完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复JavaScript错误失败: {e}")
        return False

def add_model_select_to_trading_config():
    """在交易配置中添加模型选择功能"""
    print(f"\n🔧 在交易配置中添加模型选择功能")
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有tradingModelSelect
        if 'id="tradingModelSelect"' in content:
            print("✅ 交易模型选择已存在")
            return True
        
        # 如果没有，需要添加
        # 找到交易配置区域
        trading_config_start = content.find('<h6 class="text-primary mb-3">')
        if trading_config_start == -1:
            print("❌ 未找到交易配置区域")
            return False
        
        # 在交易配置开始处添加模型选择
        model_select_html = '''
                            <!-- 模型选择 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-brain me-1"></i>交易模型
                                    <small class="text-muted">(选择用于自动交易的深度学习模型)</small>
                                </label>
                                <select class="form-select" id="tradingModelSelect">
                                    <option value="">请选择交易模型...</option>
                                </select>
                                <div class="form-text" id="tradingModelInfo">
                                    <i class="fas fa-info-circle text-info"></i>
                                    选择一个训练完成的模型用于AI交易决策
                                </div>
                            </div>
'''
        
        # 在交易配置标题后插入模型选择
        insert_pos = content.find('</h6>', trading_config_start) + 5
        content = content[:insert_pos] + model_select_html + content[insert_pos:]
        
        # 写入修复后的内容
        with open('templates/model_inference.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 交易模型选择已添加")
        return True
        
    except Exception as e:
        print(f"❌ 添加模型选择失败: {e}")
        return False

def verify_api_endpoints():
    """验证API端点"""
    print(f"\n🔍 验证API端点")
    
    import requests
    import time
    
    # 等待应用启动
    time.sleep(2)
    
    try:
        # 测试模型列表API
        response = requests.get('http://127.0.0.1:5000/api/deep-learning/models', timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                models = data.get('models', [])
                completed_models = [m for m in models if m.get('status') == 'completed']
                print(f"✅ 模型列表API正常，找到 {len(completed_models)} 个完成的模型")
            else:
                print(f"⚠️ 模型列表API返回错误: {data.get('error')}")
        else:
            print(f"❌ 模型列表API访问失败: {response.status_code}")
        
        # 测试MT5连接状态API
        response = requests.get('http://127.0.0.1:5000/api/mt5/connection-status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ MT5连接状态API正常，连接状态: {data.get('connected', False)}")
        else:
            print(f"❌ MT5连接状态API访问失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ API端点验证失败: {e}")
        return False

def create_test_page():
    """创建测试页面"""
    print(f"\n🧪 创建测试页面")
    
    test_html = '''<!DOCTYPE html>
<html>
<head>
    <title>AI推理交易功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
    </style>
</head>
<body>
    <h1>AI推理交易功能测试</h1>
    
    <div class="test-section info">
        <h3>🔍 测试说明</h3>
        <p>此页面用于测试AI推理交易的关键功能是否正常工作。</p>
        <p>请访问: <a href="http://127.0.0.1:5000/deep-learning/inference" target="_blank">AI推理交易页面</a></p>
    </div>
    
    <div class="test-section">
        <h3>✅ 应该看到的功能</h3>
        <ul>
            <li><strong>MT5连接状态</strong>: 页面顶部应显示MT5连接状态和控制按钮</li>
            <li><strong>交易模型选择</strong>: 交易配置区域应有"交易模型"下拉框</li>
            <li><strong>模型列表</strong>: 下拉框中应显示已训练完成的模型</li>
            <li><strong>连接按钮</strong>: "检查连接"和"自动连接"按钮应该可以点击</li>
            <li><strong>增强特征</strong>: 交易配置中应有"增强特征"开关</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h3>🔧 故障排除</h3>
        <ol>
            <li><strong>如果模型列表为空</strong>:
                <ul>
                    <li>检查是否有训练完成的模型</li>
                    <li>打开浏览器控制台查看错误信息</li>
                    <li>检查API端点是否正常</li>
                </ul>
            </li>
            <li><strong>如果MT5连接失败</strong>:
                <ul>
                    <li>确保MT5软件已启动</li>
                    <li>检查MT5是否允许自动交易</li>
                    <li>检查网络连接</li>
                </ul>
            </li>
            <li><strong>如果页面功能异常</strong>:
                <ul>
                    <li>强制刷新页面 (Ctrl+F5)</li>
                    <li>清除浏览器缓存</li>
                    <li>检查浏览器控制台错误</li>
                </ul>
            </li>
        </ol>
    </div>
    
    <div class="test-section">
        <h3>📋 测试检查清单</h3>
        <p>请在AI推理交易页面上验证以下功能:</p>
        <ul>
            <li>□ 页面正常加载，无JavaScript错误</li>
            <li>□ MT5连接状态显示正常</li>
            <li>□ 交易模型下拉框有选项</li>
            <li>□ 点击"检查连接"按钮有响应</li>
            <li>□ 点击"自动连接"按钮有响应</li>
            <li>□ 选择模型后显示模型信息</li>
            <li>□ 增强特征开关存在且可操作</li>
            <li>□ 其他交易配置选项正常</li>
        </ul>
    </div>
    
    <script>
        // 简单的功能测试
        function testAPIs() {
            console.log('开始测试API端点...');
            
            // 测试模型列表API
            fetch('/api/deep-learning/models')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('✅ 模型列表API正常，模型数量:', data.models.length);
                    } else {
                        console.error('❌ 模型列表API错误:', data.error);
                    }
                })
                .catch(error => console.error('❌ 模型列表API异常:', error));
            
            // 测试MT5连接API
            fetch('/api/mt5/connection-status')
                .then(response => response.json())
                .then(data => {
                    console.log('✅ MT5连接状态API正常，连接状态:', data.connected);
                })
                .catch(error => console.error('❌ MT5连接API异常:', error));
        }
        
        // 页面加载后自动测试
        window.onload = function() {
            setTimeout(testAPIs, 1000);
        };
    </script>
</body>
</html>'''
    
    with open('ai_trading_test.html', 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print("✅ 测试页面已创建: ai_trading_test.html")
    return True

def main():
    """主函数"""
    print("🚀 开始修复AI推理交易的MT5连接和模型选择问题")
    print("=" * 60)
    
    success_count = 0
    total_tasks = 4
    
    # 1. 修复JavaScript错误
    if fix_javascript_errors():
        success_count += 1
    
    # 2. 添加模型选择功能
    if add_model_select_to_trading_config():
        success_count += 1
    
    # 3. 验证API端点
    if verify_api_endpoints():
        success_count += 1
    
    # 4. 创建测试页面
    if create_test_page():
        success_count += 1
    
    print(f"\n📊 修复进度: {success_count}/{total_tasks}")
    
    if success_count == total_tasks:
        print("\n🎉 所有问题修复完成！")
        print("📋 修复总结:")
        print("   1. ✅ 修复了JavaScript null.addEventListener错误")
        print("   2. ✅ 确保交易模型选择功能完整")
        print("   3. ✅ 验证了API端点正常工作")
        print("   4. ✅ 创建了测试页面")
        print("\n🔄 请重启应用并测试功能")
        print("🌐 测试页面: ai_trading_test.html")
    else:
        print(f"\n⚠️ 部分问题修复失败 ({success_count}/{total_tasks})")
        print("🔧 请检查上述错误信息")
    
    return success_count == total_tasks

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
