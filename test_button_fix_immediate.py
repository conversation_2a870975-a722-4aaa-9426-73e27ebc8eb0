#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
立即测试按钮修复效果
"""

print("🔧 手动交易按钮'处理中'问题修复指南")
print("=" * 80)

print("\n📋 问题现状:")
print("刷新页面后，手动交易按钮一直显示'处理中'状态")

print("\n✅ 修复方案:")
print("1. 已修改代码，区分'禁用'和'处理中'两种状态")
print("2. 提供立即修复的JavaScript代码")

print("\n🚀 立即修复步骤:")
print("1. 打开AI推理交易页面")
print("2. 按F12打开浏览器开发者工具")
print("3. 切换到'控制台(Console)'标签")
print("4. 复制并粘贴以下代码，然后按回车:")

print("\n" + "="*60)
print("// 立即修复代码 - 请复制到浏览器控制台运行")
print("="*60)

fix_code = '''
console.log('🔧 立即修复手动交易按钮状态...');

const buyBtn = document.getElementById('manualBuyBtn');
const sellBtn = document.getElementById('manualSellBtn');

if (buyBtn && sellBtn) {
    // 强制重置为正常状态
    buyBtn.disabled = true;
    buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
    buyBtn.className = 'btn btn-outline-success w-100';
    
    sellBtn.disabled = true;
    sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
    sellBtn.className = 'btn btn-outline-danger w-100';
    
    console.log('✅ 按钮状态已修复！不再显示"处理中"');
    
    // 重新定义函数避免再次出现问题
    window.setManualTradeButtonsEnabled = function(enabled, isProcessing = false) {
        if (enabled) {
            buyBtn.disabled = false;
            buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
            buyBtn.className = 'btn btn-success w-100';
            
            sellBtn.disabled = false;
            sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
            sellBtn.className = 'btn btn-danger w-100';
        } else if (isProcessing) {
            buyBtn.disabled = true;
            buyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
            buyBtn.className = 'btn btn-secondary w-100';
            
            sellBtn.disabled = true;
            sellBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
            sellBtn.className = 'btn btn-secondary w-100';
        } else {
            buyBtn.disabled = true;
            buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
            buyBtn.className = 'btn btn-outline-success w-100';
            
            sellBtn.disabled = true;
            sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
            sellBtn.className = 'btn btn-outline-danger w-100';
        }
    };
    
    // 检查条件并更新状态
    setTimeout(() => {
        const canTrade = window.mt5Connected && window.selectedTradingModel && window.currentMarketData;
        window.setManualTradeButtonsEnabled(canTrade, false);
        console.log('🔍 按钮状态已根据条件更新');
    }, 100);
    
} else {
    console.error('❌ 找不到按钮元素');
}
'''

print(fix_code)
print("="*60)

print("\n💡 预期效果:")
print("• 按钮立即显示正常文本（买入(BUY)、卖出(SELL)）")
print("• 不再显示'处理中'状态")
print("• 根据实际条件启用或禁用按钮")

print("\n🔄 永久修复:")
print("代码已修改，下次刷新页面时会自动应用修复")

print("\n📊 按钮状态说明:")
print("• 启用状态: 绿色'买入'，红色'卖出'")
print("• 禁用状态: 灰色边框，正常文本")
print("• 处理中: 灰色背景，'处理中...'文本")

print("\n🎉 修复完成！")
print("现在按钮应该显示正常状态，不再一直显示'处理中'")
