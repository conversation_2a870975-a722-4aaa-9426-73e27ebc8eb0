#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制停止卡住的训练任务
直接修改数据库状态
"""

import sqlite3
from datetime import datetime

def force_stop_training():
    """强制停止训练任务"""
    print('🛑 强制停止卡住的训练任务')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找running状态的任务
        cursor.execute('''
            SELECT id, model_id, current_epoch, total_epochs, updated_at
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("❌ 没有找到正在运行的任务")
            conn.close()
            return False
        
        print(f"📊 找到 {len(tasks)} 个正在运行的任务:")
        
        for task in tasks:
            task_id, model_id, current_epoch, total_epochs, updated_at = task
            print(f"   任务ID: {task_id}")
            print(f"   模型ID: {model_id}")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   最后更新: {updated_at}")
            
            # 更新任务状态为failed
            cursor.execute('''
                UPDATE training_tasks 
                SET status = 'failed',
                    completed_at = ?,
                    updated_at = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), datetime.now().isoformat(), task_id))
            
            print(f"   ✅ 任务状态已更新为failed")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n✅ 成功停止 {len(tasks)} 个卡住的训练任务")
        return True
        
    except Exception as e:
        print(f"❌ 强制停止失败: {e}")
        return False

def main():
    """主函数"""
    print('🔧 强制停止第8轮卡住的训练')
    print('=' * 80)
    
    success = force_stop_training()
    
    if success:
        print(f"\n🎉 卡住的训练任务已强制停止！")
        print(f"💡 现在可以:")
        print(f"   1. 重新开始训练")
        print(f"   2. 使用优化后的参数:")
        print(f"      - batch_size: 8")
        print(f"      - learning_rate: 0.0001")
        print(f"      - hidden_size: 32")
        print(f"      - num_layers: 1")
        print(f"      - use_gpu: False")
        print(f"   3. 监控前8轮的训练过程")
    else:
        print(f"\n❌ 强制停止失败")

if __name__ == "__main__":
    main()
