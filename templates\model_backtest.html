{% extends "base.html" %}

{% block title %}AI推理回测{% endblock %}

{% block extra_css %}
<style>
.backtest-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e3e6f0;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.backtest-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #5a5c69;
}

.profit-positive {
    color: #1cc88a;
    font-weight: bold;
}

.profit-negative {
    color: #e74a3b;
    font-weight: bold;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-running {
    background-color: #f6c23e;
    animation: pulse 2s infinite;
}

.status-completed {
    background-color: #1cc88a;
}

.status-error {
    background-color: #e74a3b;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.config-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.trade-row-profit {
    background-color: rgba(28, 200, 138, 0.1);
}

.trade-row-loss {
    background-color: rgba(231, 74, 59, 0.1);
}

.optimization-rank-1 {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    font-weight: bold;
}

.optimization-rank-2 {
    background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
    color: #333;
    font-weight: bold;
}

.optimization-rank-3 {
    background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
    color: white;
    font-weight: bold;
}

.parameter-highlight {
    background-color: #e3f2fd;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
}

.alert-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.btn-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-gradient:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    color: white;
}

.feature-badge {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.risk-badge-low {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.risk-badge-medium {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.risk-badge-high {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-chart-line text-success me-2"></i>
                    AI推理回测
                    <small class="text-muted ms-2">基于历史数据验证AI模型交易策略</small>
                </h1>
                <div class="d-flex align-items-center mt-2">
                    <span class="badge bg-secondary me-2" id="mt5ConnectionStatus">检查中...</span>
                    <button class="btn btn-sm btn-outline-primary" id="reconnectMT5Btn" onclick="reconnectMT5()" style="display: none;">
                        <i class="fas fa-sync-alt me-1"></i>重新连接MT5
                    </button>
                </div>
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回首页
                </a>
            </div>
        </div>
    </div>

    <!-- 功能提示区域 -->
    <div class="alert alert-gradient mt-3" id="backtestFeaturesHint">
        <div class="d-flex align-items-center">
            <i class="fas fa-chart-bar text-warning me-2 fs-4"></i>
            <div class="flex-grow-1">
                <h6 class="alert-heading mb-1">📊 AI推理回测功能</h6>
                <p class="mb-2">使用历史数据验证AI模型的交易策略，支持参数优化和风险分析，帮助您找到最佳交易参数！</p>
                <button type="button" class="btn btn-light btn-sm" onclick="showBacktestConfig()">
                    <i class="fas fa-cog me-1"></i>开始回测
                </button>
                <button type="button" class="btn btn-outline-light btn-sm ms-2" onclick="dismissBacktestHint()">
                    <i class="fas fa-times me-1"></i>知道了
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- 模型选择和基础配置 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-brain me-2"></i>模型选择与基础配置
                    </h6>
                </div>
                <div class="card-body">
                    <form id="backtestForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">选择AI模型</label>
                                    <select class="form-select" id="modelSelect" required>
                                        <option value="">请选择模型...</option>
                                    </select>
                                    <div class="form-text">选择用于回测的AI推理模型</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">交易品种</label>
                                    <select class="form-select" id="symbol" required>
                                        <option value="XAUUSD">XAUUSD (黄金/美元)</option>
                                        <option value="EURUSD">EURUSD (欧元/美元)</option>
                                        <option value="GBPUSD">GBPUSD (英镑/美元)</option>
                                        <option value="USDJPY">USDJPY (美元/日元)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">时间周期</label>
                                    <select class="form-select" id="timeframe" required>
                                        <option value="M15">M15 (15分钟)</option>
                                        <option value="M30">M30 (30分钟)</option>
                                        <option value="H1" selected>H1 (1小时)</option>
                                        <option value="H4">H4 (4小时)</option>
                                        <option value="D1">D1 (日线)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">回测开始日期</label>
                                    <input type="date" class="form-control" id="start_date" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">回测结束日期</label>
                                    <input type="date" class="form-control" id="end_date" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <div class="form-control-plaintext">
                                        <span class="status-indicator" id="statusIndicator"></span>
                                        <span id="statusText">等待开始</span>
                                    </div>
                                    <!-- 进度条 -->
                                    <div class="progress mt-2" id="progressContainer" style="display: none;">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                                             id="progressBar" role="progressbar" style="width: 0%">
                                            <span id="progressText">0%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 模型信息显示 -->
                        <div id="modelInfo" style="display: none;">
                            <!-- 模型信息将在这里显示 -->
                        </div>
                    </form>
                </div>
            </div>

            <!-- 增强特征配置 -->
            <div class="card shadow mb-4 border-primary" id="enhancedFeaturesCard">
                <div class="card-header py-3 bg-primary bg-opacity-10">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-brain me-2"></i>增强特征配置
                        <small class="text-muted ms-2">提升回测预测准确性</small>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="useEnhancedFeatures" onchange="toggleEnhancedFeaturesConfig()">
                                <label class="form-check-label" for="useEnhancedFeatures">
                                    <strong>使用增强特征</strong>
                                    <small class="text-muted d-block">启用52个高级技术指标特征</small>
                                </label>
                            </div>

                            <div id="enhancedFeaturesConfig" style="display: none;">
                                <div class="mb-3">
                                    <label class="form-label">特征选择策略</label>
                                    <select class="form-select" id="featureSelectionStrategy" onchange="toggleCustomFeaturesConfig()">
                                        <option value="minimal">最小特征集 (10个核心指标)</option>
                                        <option value="recommended">推荐特征集 (26个核心特征)</option>
                                        <option value="enhanced">增强特征集 (52个特征)</option>
                                        <option value="top_importance">重要性前15个特征</option>
                                        <option value="custom">自定义特征选择</option>
                                    </select>
                                    <div class="form-text">选择用于回测的特征集合</div>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="analyzeFeatureImportance">
                                    <label class="form-check-label" for="analyzeFeatureImportance">
                                        分析特征重要性
                                        <small class="text-muted d-block">显示各特征对预测结果的贡献度</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- 自定义特征选择区域 -->
                            <div id="customFeaturesConfig" style="display: none;">
                                <h6 class="text-primary mb-3">自定义特征选择</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-muted">布林带特征</h6>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_bb_percent_b" checked>
                                            <label class="form-check-label" for="feature_bb_percent_b">%B指标</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_bb_squeeze" checked>
                                            <label class="form-check-label" for="feature_bb_squeeze">挤压信号</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_bb_breakout" checked>
                                            <label class="form-check-label" for="feature_bb_breakout">突破信号</label>
                                        </div>

                                        <h6 class="text-muted mt-3">ATR特征</h6>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_atr_atr" checked>
                                            <label class="form-check-label" for="feature_atr_atr">ATR值</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_atr_ratio" checked>
                                            <label class="form-check-label" for="feature_atr_ratio">ATR比率</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_atr_low_volatility" checked>
                                            <label class="form-check-label" for="feature_atr_low_volatility">低波动性</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-muted">随机指标特征</h6>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_stoch_stoch_k" checked>
                                            <label class="form-check-label" for="feature_stoch_stoch_k">%K线</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_stoch_overbought" checked>
                                            <label class="form-check-label" for="feature_stoch_overbought">超买信号</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_stoch_oversold" checked>
                                            <label class="form-check-label" for="feature_stoch_oversold">超卖信号</label>
                                        </div>

                                        <h6 class="text-muted mt-3">组合信号</h6>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_combined_squeeze_low_vol" checked>
                                            <label class="form-check-label" for="feature_combined_squeeze_low_vol">挤压+低波动</label>
                                        </div>
                                        <div class="form-check form-check-sm">
                                            <input class="form-check-input" type="checkbox" id="feature_combined_breakout_confirmed" checked>
                                            <label class="form-check-label" for="feature_combined_breakout_confirmed">确认突破</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 回测配置 -->
            <div class="card shadow mb-4" id="backtestConfig">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-cog me-2"></i>回测配置
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">初始资金 ($)</label>
                                    <input type="number" class="form-control" id="backtestInitialBalance"
                                           value="10000" min="1000" max="1000000" step="1000">
                                    <div class="form-text">回测起始资金</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">交易手数</label>
                                    <input type="number" class="form-control" id="backtestLotSize"
                                           value="0.01" min="0.01" max="10" step="0.01">
                                    <div class="form-text">每笔交易手数</div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">止损 (pips)</label>
                                    <input type="number" class="form-control" id="backtestStopLoss"
                                           value="1000" min="10" max="3000" step="10">
                                    <div class="form-text">止损点数 (黄金推荐1000+ pips)</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">止盈 (pips)</label>
                                    <input type="number" class="form-control" id="backtestTakeProfit"
                                           value="2000" min="10" max="5000" step="10">
                                    <div class="form-text">止盈点数 (黄金推荐2000+ pips)</div>
                                </div>
                            </div>

                            <!-- 移动止损配置 -->
                            <div id="trailingStopConfig" style="display: none;">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">触发距离 (pips)</label>
                                        <input type="number" class="form-control" id="trailingStopDistance"
                                               value="20" min="5" max="100" step="5">
                                        <div class="form-text">价格有利多少pips后开始跟踪</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">跟踪步长 (pips)</label>
                                        <input type="number" class="form-control" id="trailingStopStep"
                                               value="10" min="1" max="50" step="1">
                                        <div class="form-text">每次移动止损的步长</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">最低置信度</label>
                                <input type="number" class="form-control" id="backtestMinConfidence"
                                       value="0.3" min="0.3" max="0.99" step="0.05">
                                <div class="form-text">
                                    执行交易的最低置信度
                                    <span class="badge bg-info ms-1">推荐: 0.3-0.6</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">风险管理</label>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="backtestDynamicSL" checked>
                                    <label class="form-check-label" for="backtestDynamicSL">
                                        动态止盈止损 (基于市场波动性)
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableTrailingStop" onchange="toggleTrailingStopConfig()">
                                    <label class="form-check-label" for="enableTrailingStop">
                                        <strong>移动止损</strong>
                                        <small class="text-muted d-block">价格有利时自动跟随移动止损位，锁定利润</small>
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="backtestCliffBrake">
                                    <label class="form-check-label" for="backtestCliffBrake">
                                        <strong>悬崖勒马</strong>
                                        <small class="text-muted d-block">连续2单亏损时，根据价格趋势反转交易方向</small>
                                    </label>
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-shield-alt text-success me-1"></i>
                                    高级风险控制功能，帮助保护利润和减少亏损
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">配置预设</label>
                                <select class="form-select" id="backtestPreset" onchange="applyBacktestPreset()">
                                    <option value="custom">自定义</option>
                                    <option value="conservative">保守型 (置信度30%)</option>
                                    <option value="balanced">平衡型 (置信度40%)</option>
                                    <option value="aggressive">激进型 (置信度50%)</option>
                                </select>
                                <div class="form-text">快速应用推荐配置</div>
                            </div>

                            <!-- 参数优化配置 -->
                            <div class="mb-3 mt-4">
                                <h6 class="text-primary">
                                    <i class="fas fa-cogs me-2"></i>参数优化设置
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">优化周期</label>
                                        <select class="form-select" id="optimizationPeriod">
                                            <option value="week">一周数据</option>
                                            <option value="month">一个月数据</option>
                                        </select>
                                        <div class="form-text">选择用于参数优化的历史数据时间范围</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">风险偏好</label>
                                        <select class="form-select" id="riskPreference" onchange="loadSavedOptimizationResults()">
                                            <option value="balanced">平衡模式</option>
                                            <option value="high_return_high_risk">高收益高风险</option>
                                            <option value="medium_return_low_risk">中等收益低风险</option>
                                            <option value="low_return_ultra_low_risk">低收益超低风险</option>
                                        </select>
                                        <div class="form-text">选择参数优化的风险偏好策略</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2 flex-wrap justify-content-between">
                                <div class="d-flex gap-2 flex-wrap">
                                    <button type="button" class="btn btn-success" id="startBacktestBtn" onclick="startBacktest()">
                                        <i class="fas fa-chart-line me-1"></i>开始回测
                                    </button>
                                    <button type="button" class="btn btn-warning" id="parameterOptimizationBtn" onclick="startParameterOptimization()">
                                        <i class="fas fa-cogs me-1"></i>参数优化
                                    </button>
                                    <button type="button" class="btn btn-info" id="loadSavedResultsBtn" onclick="loadSavedOptimizationResults()">
                                        <i class="fas fa-history me-1"></i>加载保存结果
                                    </button>
                                </div>
                                <div class="d-flex gap-2 flex-wrap">
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetBacktestConfig()">
                                        <i class="fas fa-undo me-1"></i>重置配置
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="saveBacktestConfig()">
                                        <i class="fas fa-save me-1"></i>保存配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 回测结果 -->
            <div class="card shadow mb-4" id="backtestCard" style="display: none;">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-chart-bar me-2"></i>交易回测结果
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 回测统计 -->
                    <div class="row mb-4" id="backtestStats">
                        <!-- 回测统计将在这里显示 -->
                    </div>

                    <!-- 回测详细结果 -->
                    <div id="backtestResults">
                        <!-- 回测详细结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/backtest_functions.js') }}"></script>
<script>
// 全局变量
let selectedModel = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 AI推理回测页面加载完成');

    // 设置默认日期
    setDefaultDates();

    // 加载可用模型
    loadAvailableModels();

    // 检查MT5连接状态
    checkMT5Connection();
    
    // 监听模型选择变化
    document.getElementById('modelSelect').addEventListener('change', function() {
        const modelId = this.value;
        if (modelId) {
            loadModelInfo(modelId);
        } else {
            clearModelInfo();
        }
    });
    
    // 检查是否需要显示提示
    setTimeout(function() {
        const hintDismissed = localStorage.getItem('backtestHintDismissed');
        const hint = document.getElementById('backtestFeaturesHint');

        if (hintDismissed === 'true' && hint) {
            hint.style.display = 'none';
        }

        // 检查是否有保存的配置
        const savedConfig = localStorage.getItem('saved_backtest_config');
        if (savedConfig) {
            const loadConfigBtn = document.createElement('button');
            loadConfigBtn.className = 'btn btn-outline-info btn-sm ms-2';
            loadConfigBtn.innerHTML = '<i class="fas fa-upload me-1"></i>加载保存的配置';
            loadConfigBtn.onclick = loadSavedBacktestConfig;

            const hintButtons = hint.querySelector('.d-flex .flex-grow-1');
            if (hintButtons) {
                hintButtons.appendChild(loadConfigBtn);
            }
        }
    }, 1000);
});

// 设置默认日期
function setDefaultDates() {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1); // 默认回测一个月
    
    document.getElementById('end_date').value = endDate.toISOString().split('T')[0];
    document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
}

// 加载可用模型
async function loadAvailableModels() {
    try {
        const response = await fetch('/api/deep-learning/models');
        const data = await response.json();
        
        if (data.success) {
            const modelSelect = document.getElementById('modelSelect');
            modelSelect.innerHTML = '<option value="">请选择模型...</option>';
            
            // 只显示训练完成的模型
            const completedModels = data.models.filter(model => model.status === 'completed');
            
            completedModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = `${model.name} (${model.symbol}-${model.timeframe})`;
                modelSelect.appendChild(option);
            });
            
            if (completedModels.length === 0) {
                showError('没有可用的训练完成模型，请先训练AI模型');
            }
        } else {
            showError('加载模型列表失败: ' + data.error);
        }
    } catch (error) {
        console.error('加载模型列表失败:', error);
        showError('加载模型列表失败: ' + error.message);
    }
}

// 加载模型信息
async function loadModelInfo(modelId) {
    try {
        const response = await fetch(`/api/deep-learning/models/${modelId}`);
        const data = await response.json();
        
        if (data.success) {
            selectedModel = data.model;
            displayModelInfo(selectedModel);
        } else {
            showError('加载模型信息失败: ' + data.error);
        }
    } catch (error) {
        console.error('加载模型信息失败:', error);
        showError('加载模型信息失败: ' + error.message);
    }
}

// 显示模型信息
function displayModelInfo(model) {
    const modelInfoDiv = document.getElementById('modelInfo');
    
    const accuracy = model.performance?.accuracy ? (model.performance.accuracy * 100).toFixed(1) + '%' : 'N/A';
    const precision = model.performance?.precision ? (model.performance.precision * 100).toFixed(1) + '%' : 'N/A';
    
    modelInfoDiv.innerHTML = `
        <div class="alert alert-info">
            <div class="row">
                <div class="col-md-8">
                    <h6 class="text-primary">${model.name}</h6>
                    <small class="text-muted">${model.id}</small>
                    <div class="mt-2">
                        <span class="badge bg-primary me-2">${model.symbol}</span>
                        <span class="badge bg-info me-2">${model.timeframe}</span>
                        <span class="badge bg-success">${model.status}</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-end">
                        <div><strong>准确率:</strong> ${accuracy}</div>
                        <div><strong>精确率:</strong> ${precision}</div>
                        <div><small class="text-muted">训练时间: ${new Date(model.created_at).toLocaleString()}</small></div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    modelInfoDiv.style.display = 'block';
}

// 清除模型信息
function clearModelInfo() {
    selectedModel = null;
    document.getElementById('modelInfo').style.display = 'none';
}

// 显示回测配置
function showBacktestConfig() {
    const configPanel = document.getElementById('backtestConfig');
    if (configPanel) {
        configPanel.scrollIntoView({ behavior: 'smooth' });
    }
}

// 关闭回测提示
function dismissBacktestHint() {
    const hint = document.getElementById('backtestFeaturesHint');
    if (hint) {
        hint.style.display = 'none';
        localStorage.setItem('backtestHintDismissed', 'true');
    }
}

// 显示错误消息
function showError(message) {
    alert('❌ ' + message);
}

// 显示成功消息
function showSuccess(message) {
    alert('✅ ' + message);
}

// 显示信息消息
function showInfo(message) {
    alert('ℹ️ ' + message);
}

// 切换移动止损配置显示
function toggleTrailingStopConfig() {
    const checkbox = document.getElementById('enableTrailingStop');
    const config = document.getElementById('trailingStopConfig');

    if (checkbox && config) {
        config.style.display = checkbox.checked ? 'block' : 'none';
    }
}

// 应用回测预设配置
function applyBacktestPreset() {
    const preset = document.getElementById('backtestPreset').value;

    switch (preset) {
        case 'conservative':
            document.getElementById('backtestInitialBalance').value = 10000;
            document.getElementById('backtestLotSize').value = 0.01;
            document.getElementById('backtestStopLoss').value = 800;
            document.getElementById('backtestTakeProfit').value = 1600;
            document.getElementById('backtestMinConfidence').value = 0.3;
            document.getElementById('backtestDynamicSL').checked = true;
            document.getElementById('enableTrailingStop').checked = false;
            document.getElementById('backtestCliffBrake').checked = false;
            toggleTrailingStopConfig();
            showInfo('已应用保守型配置：低风险，稳健收益');
            break;
        case 'balanced':
            document.getElementById('backtestInitialBalance').value = 10000;
            document.getElementById('backtestLotSize').value = 0.01;
            document.getElementById('backtestStopLoss').value = 1000;
            document.getElementById('backtestTakeProfit').value = 2000;
            document.getElementById('backtestMinConfidence').value = 0.4;
            document.getElementById('backtestDynamicSL').checked = true;
            document.getElementById('enableTrailingStop').checked = true;
            document.getElementById('backtestCliffBrake').checked = false;
            document.getElementById('trailingStopDistance').value = 20;
            document.getElementById('trailingStopStep').value = 10;
            toggleTrailingStopConfig();
            showInfo('已应用平衡型配置：适中风险，平衡收益');
            break;
        case 'aggressive':
            document.getElementById('backtestInitialBalance').value = 10000;
            document.getElementById('backtestLotSize').value = 0.02;
            document.getElementById('backtestStopLoss').value = 1200;
            document.getElementById('backtestTakeProfit').value = 2400;
            document.getElementById('backtestMinConfidence').value = 0.5;
            document.getElementById('backtestDynamicSL').checked = true;
            document.getElementById('enableTrailingStop').checked = true;
            document.getElementById('backtestCliffBrake').checked = true;
            document.getElementById('trailingStopDistance').value = 30;
            document.getElementById('trailingStopStep').value = 15;
            toggleTrailingStopConfig();
            showInfo('已应用激进型配置：高风险，高收益潜力');
            break;
        default:
            showInfo('已切换到自定义配置模式，请手动调整各项参数');
            break;
    }
}

// 获取回测配置
function getBacktestConfig() {
    const trailingStopEnabled = document.getElementById('enableTrailingStop').checked;

    return {
        initial_balance: parseFloat(document.getElementById('backtestInitialBalance').value),
        lot_size: parseFloat(document.getElementById('backtestLotSize').value),
        stop_loss_pips: parseInt(document.getElementById('backtestStopLoss').value),
        take_profit_pips: parseInt(document.getElementById('backtestTakeProfit').value),
        min_confidence: parseFloat(document.getElementById('backtestMinConfidence').value),
        dynamic_sl: document.getElementById('backtestDynamicSL').checked,
        trailing_stop_enabled: trailingStopEnabled,
        trailing_stop_distance: trailingStopEnabled ? parseInt(document.getElementById('trailingStopDistance').value) : 0,
        trailing_stop_step: trailingStopEnabled ? parseInt(document.getElementById('trailingStopStep').value) : 0,
        cliff_brake_enabled: document.getElementById('backtestCliffBrake').checked
    };
}

// 验证回测配置
function validateBacktestConfig(config) {
    if (config.initial_balance < 1000 || config.initial_balance > 1000000) {
        showError('初始资金必须在1,000-1,000,000之间');
        return false;
    }

    if (config.lot_size < 0.01 || config.lot_size > 10) {
        showError('交易手数必须在0.01-10之间');
        return false;
    }

    if (config.min_confidence < 0.3 || config.min_confidence > 0.99) {
        showError('最低置信度必须在0.3-0.99之间');
        return false;
    }

    if (config.take_profit_pips < 10 || config.take_profit_pips > 5000) {
        showError('止盈必须在10-5000 pips之间');
        return false;
    }

    // 验证移动止损配置
    if (config.trailing_stop_enabled) {
        if (config.trailing_stop_distance < 5 || config.trailing_stop_distance > 100) {
            showError('移动止损触发距离必须在5-100 pips之间');
            return false;
        }

        if (config.trailing_stop_step < 1 || config.trailing_stop_step > 50) {
            showError('移动止损跟踪步长必须在1-50 pips之间');
            return false;
        }

        if (config.trailing_stop_distance <= config.trailing_stop_step) {
            showError('移动止损触发距离必须大于跟踪步长');
            return false;
        }
    }

    return true;
}

// 获取表单数据
function getFormData() {
    return {
        symbol: document.getElementById('symbol').value,
        timeframe: document.getElementById('timeframe').value,
        start_date: document.getElementById('start_date').value,
        end_date: document.getElementById('end_date').value
    };
}

// 开始交易回测
async function startBacktest() {
    console.log('🔄 startBacktest 函数被调用');

    if (!selectedModel) {
        console.error('❌ 没有选择模型');
        showError('请先选择一个模型');
        return;
    }

    const formData = getFormData();
    console.log('表单数据:', formData);

    if (!formData.symbol || !formData.timeframe || !formData.start_date || !formData.end_date) {
        console.error('❌ 表单数据不完整');
        showError('请填写完整的回测参数');
        return;
    }

    // 获取回测配置
    const backtestConfig = getBacktestConfig();
    console.log('回测配置:', backtestConfig);

    // 获取增强特征配置
    const enhancedFeaturesConfig = getEnhancedFeaturesConfig();
    console.log('增强特征配置:', enhancedFeaturesConfig);

    // 验证回测配置
    if (!validateBacktestConfig(backtestConfig)) {
        return;
    }

    try {
        // 在开始回测前检查MT5连接
        console.log('🔍 回测前检查MT5连接状态...');
        const mt5CheckResponse = await fetch('/api/mt5/connection-status');
        const mt5Status = await mt5CheckResponse.json();

        if (!mt5Status.success || !mt5Status.connected) {
            console.log('❌ MT5未连接，尝试自动重连...');
            updateStatus('running', '检测到MT5未连接，正在尝试重连...');

            // 尝试自动重连
            const reconnectResponse = await fetch('/api/mt5/auto-connect', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            const reconnectResult = await reconnectResponse.json();

            if (!reconnectResult.success) {
                throw new Error(`MT5连接失败: ${reconnectResult.error}`);
            }

            console.log('✅ MT5重连成功，继续回测...');
            // 更新连接状态显示
            checkMT5Connection();
        }

        updateStatus('running', '正在执行回测...');
        document.getElementById('startBacktestBtn').disabled = true;

        console.log('🔄 开始交易回测...');

        const backtestData = {
            model_id: selectedModel.id,
            symbol: formData.symbol,
            timeframe: formData.timeframe,
            start_date: formData.start_date,
            end_date: formData.end_date,
            initial_balance: backtestConfig.initial_balance,
            lot_size: backtestConfig.lot_size,
            stop_loss_pips: backtestConfig.stop_loss_pips,
            take_profit_pips: backtestConfig.take_profit_pips,
            min_confidence: backtestConfig.min_confidence,
            cliff_brake_enabled: backtestConfig.cliff_brake_enabled,
            trailing_stop_enabled: backtestConfig.trailing_stop_enabled,
            trailing_stop_distance: backtestConfig.trailing_stop_distance,
            trailing_stop_step: backtestConfig.trailing_stop_step,
            // 增强特征配置
            ...enhancedFeaturesConfig
        };

        const response = await fetch('/api/deep-learning/inference-backtest', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(backtestData)
        });

        const result = await response.json();

        console.log('回测API响应:', result);

        if (result.success) {
            console.log('回测统计:', result.statistics);
            console.log('交易记录数量:', result.trades ? result.trades.length : 0);

            displayBacktestResults(result);
            updateStatus('completed', '回测完成');
            showSuccess('回测完成！');
        } else {
            console.error('回测失败:', result.error);
            showError(`回测失败: ${result.error}`);
            updateStatus('error', '回测失败');
        }

    } catch (error) {
        console.error('回测失败:', error);
        showError(`回测失败: ${error.message}`);
        updateStatus('error', '回测失败');
    } finally {
        document.getElementById('startBacktestBtn').disabled = false;
    }
}

// 更新状态显示
function updateStatus(status, text) {
    const indicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');
    const progressContainer = document.getElementById('progressContainer');

    if (indicator && statusText) {
        // 清除所有状态类
        indicator.className = 'status-indicator';

        // 添加对应的状态类
        switch (status) {
            case 'running':
                indicator.classList.add('status-running');
                if (progressContainer) {
                    progressContainer.style.display = 'block';
                    updateProgress(0, '开始处理...');
                }
                break;
            case 'completed':
                indicator.classList.add('status-completed');
                if (progressContainer) {
                    updateProgress(100, '完成');
                    setTimeout(() => {
                        progressContainer.style.display = 'none';
                    }, 3000);
                }
                break;
            case 'error':
                indicator.classList.add('status-error');
                if (progressContainer) {
                    progressContainer.style.display = 'none';
                }
                break;
        }

        statusText.textContent = text;
    }
}

// 切换增强特征配置显示
function toggleEnhancedFeaturesConfig() {
    const useEnhanced = document.getElementById('useEnhancedFeatures').checked;
    const configDiv = document.getElementById('enhancedFeaturesConfig');

    if (configDiv) {
        configDiv.style.display = useEnhanced ? 'block' : 'none';
    }

    // 如果禁用增强特征，也隐藏自定义特征配置
    if (!useEnhanced) {
        const customDiv = document.getElementById('customFeaturesConfig');
        if (customDiv) {
            customDiv.style.display = 'none';
        }
    }
}

// 切换自定义特征配置显示
function toggleCustomFeaturesConfig() {
    const strategy = document.getElementById('featureSelectionStrategy').value;
    const customDiv = document.getElementById('customFeaturesConfig');

    if (customDiv) {
        customDiv.style.display = strategy === 'custom' ? 'block' : 'none';
    }
}

// 获取增强特征配置
function getEnhancedFeaturesConfig() {
    const useEnhanced = document.getElementById('useEnhancedFeatures').checked;

    if (!useEnhanced) {
        return {
            use_enhanced_features: false
        };
    }

    const strategy = document.getElementById('featureSelectionStrategy').value;
    const analyzeImportance = document.getElementById('analyzeFeatureImportance').checked;

    let selectedFeatures = null;

    if (strategy === 'custom') {
        // 收集自定义选择的特征
        selectedFeatures = [];
        const featureCheckboxes = document.querySelectorAll('[id^="feature_"]');
        featureCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const featureName = checkbox.id.replace('feature_', '');
                selectedFeatures.push(featureName);
            }
        });
    }

    return {
        use_enhanced_features: true,
        feature_selection_strategy: strategy,
        analyze_feature_importance: analyzeImportance,
        selected_features: selectedFeatures
    };
}

// 检查MT5连接状态
async function checkMT5Connection() {
    try {
        console.log('🔍 检查MT5连接状态...');
        const response = await fetch('/api/mt5/connection-status');
        const data = await response.json();

        const statusElement = document.getElementById('mt5ConnectionStatus');
        const reconnectBtn = document.getElementById('reconnectMT5Btn');

        if (data.success && data.connected) {
            statusElement.textContent = 'MT5已连接';
            statusElement.className = 'badge bg-success me-2';
            reconnectBtn.style.display = 'none';
            console.log('✅ MT5连接正常');
        } else {
            statusElement.textContent = 'MT5未连接';
            statusElement.className = 'badge bg-danger me-2';
            reconnectBtn.style.display = 'inline-block';
            console.log('❌ MT5未连接:', data.error);

            // 自动尝试重连
            setTimeout(() => {
                autoReconnectMT5();
            }, 2000);
        }
    } catch (error) {
        console.error('❌ 检查MT5连接状态失败:', error);
        const statusElement = document.getElementById('mt5ConnectionStatus');
        const reconnectBtn = document.getElementById('reconnectMT5Btn');

        statusElement.textContent = '连接检查失败';
        statusElement.className = 'badge bg-warning me-2';
        reconnectBtn.style.display = 'inline-block';
    }
}

// 自动重连MT5
async function autoReconnectMT5() {
    try {
        console.log('🔄 自动尝试重连MT5...');
        const response = await fetch('/api/mt5/auto-connect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            console.log('✅ MT5自动重连成功');
            // 重新检查连接状态
            setTimeout(() => {
                checkMT5Connection();
            }, 1000);
        } else {
            console.log('❌ MT5自动重连失败:', data.error);
        }
    } catch (error) {
        console.error('❌ MT5自动重连异常:', error);
    }
}

// 手动重连MT5
async function reconnectMT5() {
    const reconnectBtn = document.getElementById('reconnectMT5Btn');
    const originalText = reconnectBtn.innerHTML;

    try {
        reconnectBtn.disabled = true;
        reconnectBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>重连中...';

        console.log('🔄 手动重连MT5...');
        const response = await fetch('/api/mt5/auto-connect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('MT5重连成功');
            // 重新检查连接状态
            setTimeout(() => {
                checkMT5Connection();
            }, 1000);
        } else {
            showError(`MT5重连失败: ${data.error}`);
        }
    } catch (error) {
        console.error('❌ MT5重连异常:', error);
        showError(`MT5重连异常: ${error.message}`);
    } finally {
        reconnectBtn.disabled = false;
        reconnectBtn.innerHTML = originalText;
    }
}

// 定期检查MT5连接状态
let mt5ConnectionCheckInterval = null;

function startMT5ConnectionMonitoring() {
    // 每30秒检查一次连接状态
    mt5ConnectionCheckInterval = setInterval(() => {
        checkMT5Connection();
    }, 30000);
    console.log('🔄 MT5连接监控已启动');
}

function stopMT5ConnectionMonitoring() {
    if (mt5ConnectionCheckInterval) {
        clearInterval(mt5ConnectionCheckInterval);
        mt5ConnectionCheckInterval = null;
        console.log('⏹️ MT5连接监控已停止');
    }
}

// 页面卸载时停止监控
window.addEventListener('beforeunload', function() {
    stopMT5ConnectionMonitoring();
});

// 启动连接监控
setTimeout(() => {
    startMT5ConnectionMonitoring();
}, 5000);

// 更新进度条
function updateProgress(percentage, text) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');

    if (progressBar && progressText) {
        progressBar.style.width = percentage + '%';
        progressText.textContent = text || (percentage + '%');

        // 根据进度改变颜色
        progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
        if (percentage >= 100) {
            progressBar.classList.add('bg-success');
        } else if (percentage >= 50) {
            progressBar.classList.add('bg-info');
        } else {
            progressBar.classList.add('bg-warning');
        }
    }
}

// 获取选中的模型
function getSelectedModel() {
    const modelSelect = document.getElementById('modelSelect');
    if (!modelSelect || !modelSelect.value) {
        return null;
    }

    return selectedModel; // 使用全局变量
}

// 重置回测配置
function resetBacktestConfig() {
    if (confirm('确定要重置所有回测配置吗？')) {
        // 重置基础配置
        document.getElementById('backtestInitialBalance').value = 10000;
        document.getElementById('backtestLotSize').value = 0.01;
        document.getElementById('backtestStopLoss').value = 1000;
        document.getElementById('backtestTakeProfit').value = 2000;
        document.getElementById('backtestMinConfidence').value = 0.3;

        // 重置风险管理
        document.getElementById('backtestDynamicSL').checked = true;
        document.getElementById('enableTrailingStop').checked = false;
        document.getElementById('backtestCliffBrake').checked = false;

        // 重置预设
        document.getElementById('backtestPreset').value = 'custom';

        // 重置优化配置
        document.getElementById('optimizationPeriod').value = 'week';
        document.getElementById('riskPreference').value = 'balanced';

        // 更新移动止损配置显示
        toggleTrailingStopConfig();

        showSuccess('回测配置已重置为默认值');
    }
}

// 保存回测配置
function saveBacktestConfig() {
    const config = getBacktestConfig();
    const formData = getFormData();

    const configToSave = {
        ...config,
        ...formData,
        model_id: selectedModel ? selectedModel.id : null,
        saved_at: new Date().toISOString()
    };

    // 保存到localStorage
    localStorage.setItem('saved_backtest_config', JSON.stringify(configToSave));

    showSuccess('回测配置已保存到本地');
}

// 加载保存的回测配置
function loadSavedBacktestConfig() {
    const savedConfig = localStorage.getItem('saved_backtest_config');

    if (!savedConfig) {
        showInfo('没有找到保存的配置');
        return;
    }

    try {
        const config = JSON.parse(savedConfig);

        // 应用基础配置
        if (config.symbol) document.getElementById('symbol').value = config.symbol;
        if (config.timeframe) document.getElementById('timeframe').value = config.timeframe;
        if (config.start_date) document.getElementById('start_date').value = config.start_date;
        if (config.end_date) document.getElementById('end_date').value = config.end_date;

        // 应用回测配置
        if (config.initial_balance) document.getElementById('backtestInitialBalance').value = config.initial_balance;
        if (config.lot_size) document.getElementById('backtestLotSize').value = config.lot_size;
        if (config.stop_loss_pips) document.getElementById('backtestStopLoss').value = config.stop_loss_pips;
        if (config.take_profit_pips) document.getElementById('backtestTakeProfit').value = config.take_profit_pips;
        if (config.min_confidence) document.getElementById('backtestMinConfidence').value = config.min_confidence;

        // 应用风险管理配置
        if (config.dynamic_sl !== undefined) document.getElementById('backtestDynamicSL').checked = config.dynamic_sl;
        if (config.trailing_stop_enabled !== undefined) document.getElementById('enableTrailingStop').checked = config.trailing_stop_enabled;
        if (config.cliff_brake_enabled !== undefined) document.getElementById('backtestCliffBrake').checked = config.cliff_brake_enabled;

        // 更新移动止损配置显示
        toggleTrailingStopConfig();

        showSuccess(`已加载保存的配置 (${new Date(config.saved_at).toLocaleString()})`);

    } catch (error) {
        showError('加载配置失败：配置文件格式错误');
    }
}
</script>
{% endblock %}
