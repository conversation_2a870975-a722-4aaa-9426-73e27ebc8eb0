#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强特征在回测中的效果
"""

import requests
import time
import json

def test_enhanced_features_backtest():
    """测试增强特征回测功能"""
    print("🧪 测试增强特征回测功能")
    print("=" * 50)
    
    # 等待应用启动
    time.sleep(3)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 1. 获取可用模型
        print(f"\n🔍 获取可用模型...")
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if models_response.status_code != 200:
            print(f"❌ 获取模型失败: {models_response.status_code}")
            return False
        
        models_data = models_response.json()
        if not models_data.get('success') or not models_data.get('models'):
            print(f"❌ 没有可用模型")
            return False
        
        # 选择第一个训练完成的模型
        test_model = None
        for model in models_data['models']:
            if model.get('status') == 'completed':
                test_model = model
                break
        
        if not test_model:
            print(f"❌ 没有训练完成的模型")
            return False
        
        print(f"✅ 选择测试模型: {test_model['name']}")
        
        # 2. 执行基础回测（不使用增强特征）
        print(f"\n🔄 执行基础回测（不使用增强特征）...")
        basic_backtest_data = {
            'model_id': test_model['id'],
            'symbol': test_model.get('symbol', 'XAUUSD'),
            'timeframe': test_model.get('timeframe', 'H1'),
            'start_date': '2025-07-28',
            'end_date': '2025-08-02',
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 1000,
            'take_profit_pips': 2000,
            'min_confidence': 0.3,
            'cliff_brake_enabled': False,
            'trailing_stop_enabled': False,
            # 不使用增强特征
            'use_enhanced_features': False
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', 
                               json=basic_backtest_data)
        
        if response.status_code != 200:
            print(f"❌ 基础回测请求失败: {response.status_code}")
            return False
        
        basic_result = response.json()
        if not basic_result.get('success'):
            print(f"❌ 基础回测失败: {basic_result.get('error')}")
            return False
        
        basic_trades = basic_result.get('trades', [])
        basic_stats = basic_result.get('statistics', {})
        
        print(f"✅ 基础回测成功: {len(basic_trades)} 笔交易")
        print(f"📊 基础统计: 胜率{basic_stats.get('win_rate', 0):.1f}%, 总收益{basic_stats.get('total_return', 0):.2f}%, 总盈亏${basic_stats.get('total_profit', 0):.2f}")
        
        # 3. 执行增强特征回测
        print(f"\n🚀 执行增强特征回测...")
        enhanced_backtest_data = {
            'model_id': test_model['id'],
            'symbol': test_model.get('symbol', 'XAUUSD'),
            'timeframe': test_model.get('timeframe', 'H1'),
            'start_date': '2025-07-28',
            'end_date': '2025-08-02',
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 1000,
            'take_profit_pips': 2000,
            'min_confidence': 0.3,
            'cliff_brake_enabled': False,
            'trailing_stop_enabled': False,
            # 使用增强特征
            'use_enhanced_features': True,
            'feature_selection_strategy': 'recommended',
            'analyze_feature_importance': True,
            'selected_features': None
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', 
                               json=enhanced_backtest_data)
        
        if response.status_code != 200:
            print(f"❌ 增强特征回测请求失败: {response.status_code}")
            return False
        
        enhanced_result = response.json()
        if not enhanced_result.get('success'):
            print(f"❌ 增强特征回测失败: {enhanced_result.get('error')}")
            return False
        
        enhanced_trades = enhanced_result.get('trades', [])
        enhanced_stats = enhanced_result.get('statistics', {})
        
        print(f"✅ 增强特征回测成功: {len(enhanced_trades)} 笔交易")
        print(f"📊 增强统计: 胜率{enhanced_stats.get('win_rate', 0):.1f}%, 总收益{enhanced_stats.get('total_return', 0):.2f}%, 总盈亏${enhanced_stats.get('total_profit', 0):.2f}")
        
        # 4. 对比分析
        print(f"\n📈 对比分析:")
        print(f"{'指标':<15} {'基础回测':<15} {'增强特征':<15} {'差异':<15}")
        print("-" * 60)
        
        # 交易数量对比
        trade_diff = len(enhanced_trades) - len(basic_trades)
        print(f"{'交易数量':<15} {len(basic_trades):<15} {len(enhanced_trades):<15} {trade_diff:+d}")
        
        # 胜率对比
        basic_win_rate = basic_stats.get('win_rate', 0)
        enhanced_win_rate = enhanced_stats.get('win_rate', 0)
        win_rate_diff = enhanced_win_rate - basic_win_rate
        print(f"{'胜率(%)':<15} {basic_win_rate:<15.1f} {enhanced_win_rate:<15.1f} {win_rate_diff:+.1f}")
        
        # 总收益对比
        basic_return = basic_stats.get('total_return', 0)
        enhanced_return = enhanced_stats.get('total_return', 0)
        return_diff = enhanced_return - basic_return
        print(f"{'总收益(%)':<15} {basic_return:<15.2f} {enhanced_return:<15.2f} {return_diff:+.2f}")
        
        # 总盈亏对比
        basic_profit = basic_stats.get('total_profit', 0)
        enhanced_profit = enhanced_stats.get('total_profit', 0)
        profit_diff = enhanced_profit - basic_profit
        print(f"{'总盈亏($)':<15} {basic_profit:<15.2f} {enhanced_profit:<15.2f} {profit_diff:+.2f}")
        
        # 5. 检查是否有实际差异
        print(f"\n🔍 差异分析:")
        has_difference = False
        
        if abs(trade_diff) > 0:
            print(f"   ✅ 交易数量有差异: {trade_diff:+d} 笔")
            has_difference = True
        else:
            print(f"   ⚠️ 交易数量无差异")
        
        if abs(win_rate_diff) > 0.1:
            print(f"   ✅ 胜率有差异: {win_rate_diff:+.1f}%")
            has_difference = True
        else:
            print(f"   ⚠️ 胜率差异很小: {win_rate_diff:+.1f}%")
        
        if abs(profit_diff) > 0.01:
            print(f"   ✅ 盈亏有差异: ${profit_diff:+.2f}")
            has_difference = True
        else:
            print(f"   ⚠️ 盈亏差异很小: ${profit_diff:+.2f}")
        
        # 6. 检查交易详情中是否有增强特征标记
        print(f"\n🔍 检查增强特征标记:")
        enhanced_feature_used = False
        if enhanced_trades:
            for trade in enhanced_trades[:3]:  # 检查前3笔交易
                if trade.get('enhanced_features_used'):
                    enhanced_feature_used = True
                    print(f"   ✅ 交易中发现增强特征标记: {trade.get('feature_strategy', 'unknown')}")
                    break
        
        if not enhanced_feature_used:
            print(f"   ⚠️ 交易中未发现增强特征标记")
        
        # 7. 结论
        print(f"\n📋 测试结论:")
        if has_difference or enhanced_feature_used:
            print(f"   ✅ 增强特征功能正常工作")
            if has_difference:
                print(f"   ✅ 增强特征对回测结果产生了影响")
            if enhanced_feature_used:
                print(f"   ✅ 增强特征标记正确添加到交易记录中")
        else:
            print(f"   ❌ 增强特征功能可能未生效")
            print(f"   💡 建议检查:")
            print(f"      - 后端是否正确接收增强特征配置")
            print(f"      - AI推理是否使用了增强特征")
            print(f"      - 增强特征计算是否成功")
        
        return has_difference or enhanced_feature_used
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_enhanced_features_backtest()
    
    if success:
        print("\n🎉 增强特征回测功能测试完成！")
        print("📋 测试结果: 增强特征功能正常工作")
    else:
        print("\n❌ 增强特征回测功能测试失败")
        print("🔧 需要进一步检查增强特征实现")
