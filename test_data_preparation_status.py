#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据准备状态显示和模型训练启动问题修复
验证状态一致性和用户体验改进
"""

import sqlite3
import json
from datetime import datetime

def check_training_tasks_status():
    """检查训练任务状态"""
    print("🔍 检查训练任务状态")
    print("=" * 80)
    
    try:
        conn = sqlite3.connect('data/deep_learning.db')
        cursor = conn.cursor()
        
        # 获取最近的训练任务
        cursor.execute('''
            SELECT id, model_id, status, progress, logs, created_at, updated_at
            FROM training_tasks
            ORDER BY created_at DESC
            LIMIT 10
        ''')
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("❌ 没有找到训练任务")
            return False
        
        print(f"📊 找到 {len(tasks)} 个训练任务:")
        print()
        
        status_issues = []
        
        for task in tasks:
            task_id, model_id, status, progress, logs, created_at, updated_at = task
            
            print(f"🔸 任务 {task_id[:8]}...")
            print(f"   模型ID: {model_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            
            # 分析日志
            if logs:
                try:
                    log_data = json.loads(logs)
                    stage = log_data.get('stage', 'unknown')
                    message = log_data.get('message', '')
                    
                    print(f"   日志阶段: {stage}")
                    print(f"   日志消息: {message}")
                    
                    # 检查状态一致性
                    if stage == 'data_ready' and status != 'data_ready':
                        issue = f"状态不一致: 日志显示{stage}，但任务状态是{status}"
                        status_issues.append((task_id, issue))
                        print(f"   ⚠️ {issue}")
                    elif status == 'data_ready':
                        print(f"   ✅ 状态一致: 数据准备完成")
                    
                    # 显示数据信息
                    if 'train_samples' in log_data:
                        print(f"   📊 训练样本: {log_data.get('train_samples', 'N/A')}")
                        print(f"   📊 验证样本: {log_data.get('val_samples', 'N/A')}")
                        print(f"   📊 特征数: {log_data.get('feature_count', 'N/A')}")
                    
                except json.JSONDecodeError:
                    print(f"   ❌ 日志解析失败")
            else:
                print(f"   ⚠️ 无日志信息")
            
            print()
        
        conn.close()
        
        # 报告状态问题
        if status_issues:
            print(f"⚠️ 发现 {len(status_issues)} 个状态不一致问题:")
            for task_id, issue in status_issues:
                print(f"   • {task_id[:8]}...: {issue}")
            
            # 询问是否修复
            choice = input("\n是否要修复这些状态不一致问题? (y/n): ").lower().strip()
            if choice == 'y':
                fix_status_issues(status_issues)
        else:
            print("✅ 所有任务状态一致")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def fix_status_issues(status_issues):
    """修复状态不一致问题"""
    print("\n🔧 修复状态不一致问题...")
    
    try:
        conn = sqlite3.connect('data/deep_learning.db')
        cursor = conn.cursor()
        
        fixed_count = 0
        
        for task_id, issue in status_issues:
            # 获取任务详情
            cursor.execute('''
                SELECT logs, model_id FROM training_tasks WHERE id = ?
            ''', (task_id,))
            
            result = cursor.fetchone()
            if not result:
                continue
                
            logs, model_id = result
            
            try:
                log_data = json.loads(logs)
                if log_data.get('stage') == 'data_ready':
                    # 更新任务状态为data_ready
                    cursor.execute('''
                        UPDATE training_tasks
                        SET status = 'data_ready',
                            progress = 100,
                            updated_at = ?
                        WHERE id = ?
                    ''', (datetime.now().isoformat(), task_id))
                    
                    # 更新模型状态
                    cursor.execute('''
                        UPDATE deep_learning_models
                        SET status = 'data_ready'
                        WHERE id = ?
                    ''', (model_id,))
                    
                    print(f"✅ 修复任务 {task_id[:8]}... 状态为 data_ready")
                    fixed_count += 1
                    
            except json.JSONDecodeError:
                print(f"❌ 跳过任务 {task_id[:8]}... (日志解析失败)")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 成功修复 {fixed_count} 个任务的状态")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

def test_frontend_improvements():
    """测试前端改进"""
    print("\n🎨 前端改进测试")
    print("=" * 50)
    
    improvements = [
        {
            'name': '任务状态检查函数',
            'description': '在启动训练前检查任务状态',
            'file': 'templates/model_training.html',
            'function': 'checkTaskStatusBeforeTraining'
        },
        {
            'name': '状态详情显示',
            'description': '显示详细的任务状态信息',
            'file': 'templates/model_training.html',
            'function': 'showTaskStatusDetails'
        },
        {
            'name': '手动状态检查',
            'description': '用户可以手动检查任务状态',
            'file': 'templates/model_training.html',
            'function': 'checkCurrentTaskStatus'
        },
        {
            'name': '改进的数据准备显示',
            'description': '显示更详细的数据准备信息',
            'file': 'templates/model_training.html',
            'section': 'data_ready case'
        }
    ]
    
    print("📋 已实现的改进:")
    for improvement in improvements:
        print(f"✅ {improvement['name']}")
        print(f"   {improvement['description']}")
        print()

def provide_testing_guide():
    """提供测试指南"""
    print("💡 测试指南")
    print("=" * 50)
    
    print("🔍 1. 验证状态一致性:")
    print("   • 运行此脚本检查任务状态")
    print("   • 修复发现的状态不一致问题")
    
    print("\n🔍 2. 测试前端改进:")
    print("   • 重启应用程序")
    print("   • 进入模型训练页面")
    print("   • 点击'检查状态'按钮")
    print("   • 观察状态信息显示")
    
    print("\n🔍 3. 测试数据准备流程:")
    print("   • 启动新的数据准备任务")
    print("   • 观察进度显示和状态更新")
    print("   • 确认数据准备完成后按钮状态")
    
    print("\n🔍 4. 测试训练启动:")
    print("   • 在数据准备完成后点击'开始模型训练'")
    print("   • 观察是否还有'任务状态不正确'错误")
    
    print("\n🎯 预期效果:")
    print("✅ 数据准备完成后状态正确显示为data_ready")
    print("✅ 页面显示详细的数据信息（样本数、特征数等）")
    print("✅ 用户可以清楚了解当前任务状态")
    print("✅ 不再出现'任务状态不正确'的错误")
    print("✅ 训练启动前会自动检查状态")

def main():
    """主函数"""
    print("🔧 数据准备状态显示修复验证")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• 数据准备完成后状态显示不准确")
    print("• 点击开始训练提示'任务状态不正确: running'")
    print("• 用户无法确定数据准备是否真正完成")
    
    print(f"\n🔧 修复内容:")
    print("• 修复后端状态更新逻辑（running → data_ready）")
    print("• 添加前端状态检查机制")
    print("• 改进数据准备完成信息显示")
    print("• 添加手动状态检查功能")
    print("• 增强用户体验和错误提示")
    
    # 检查任务状态
    success = check_training_tasks_status()
    
    # 测试前端改进
    test_frontend_improvements()
    
    # 提供测试指南
    provide_testing_guide()
    
    if success:
        print(f"\n🎉 修复完成！")
        print("现在重启应用程序，数据准备状态将正确显示")
    else:
        print(f"\n⚠️ 请检查数据库连接和任务状态")
    
    return 0

if __name__ == "__main__":
    main()
