#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI推理回测页面功能完整性
验证所有回测功能是否正常工作
"""

import requests
from bs4 import BeautifulSoup
import re

def test_backtest_functionality():
    """测试回测功能完整性"""
    print("🧪 测试AI推理回测页面功能完整性")
    print("=" * 70)
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 2. 访问AI推理回测页面
        print(f"\n📋 访问AI推理回测页面...")
        response = session.get('http://127.0.0.1:5000/deep-learning/backtest')
        
        if response.status_code != 200:
            print(f"❌ 访问页面失败: {response.status_code}")
            return False
        
        print("✅ 页面访问成功")
        
        # 3. 解析页面内容
        soup = BeautifulSoup(response.text, 'html.parser')
        page_content = response.text
        
        # 4. 检查基础配置元素
        print(f"\n🔍 检查基础配置元素...")
        basic_elements = {
            'modelSelect': '模型选择下拉框',
            'symbol': '交易品种选择',
            'timeframe': '时间周期选择',
            'start_date': '开始日期',
            'end_date': '结束日期'
        }
        
        missing_basic = []
        for element_id, description in basic_elements.items():
            if f'id="{element_id}"' not in page_content:
                missing_basic.append(f"{element_id} ({description})")
        
        if missing_basic:
            print("❌ 缺少基础配置元素:")
            for element in missing_basic:
                print(f"   • {element}")
            return False
        else:
            print("✅ 基础配置元素完整")
        
        # 5. 检查回测配置元素
        print(f"\n🔍 检查回测配置元素...")
        backtest_elements = {
            'backtestInitialBalance': '初始资金',
            'backtestLotSize': '交易手数',
            'backtestStopLoss': '止损设置',
            'backtestTakeProfit': '止盈设置',
            'backtestMinConfidence': '最低置信度',
            'backtestDynamicSL': '动态止盈止损',
            'enableTrailingStop': '移动止损',
            'backtestCliffBrake': '悬崖勒马',
            'backtestPreset': '配置预设'
        }
        
        missing_backtest = []
        for element_id, description in backtest_elements.items():
            if f'id="{element_id}"' not in page_content:
                missing_backtest.append(f"{element_id} ({description})")
        
        if missing_backtest:
            print("❌ 缺少回测配置元素:")
            for element in missing_backtest:
                print(f"   • {element}")
            return False
        else:
            print("✅ 回测配置元素完整")
        
        # 6. 检查参数优化元素
        print(f"\n🔍 检查参数优化元素...")
        optimization_elements = {
            'optimizationPeriod': '优化周期',
            'riskPreference': '风险偏好'
        }
        
        missing_optimization = []
        for element_id, description in optimization_elements.items():
            if f'id="{element_id}"' not in page_content:
                missing_optimization.append(f"{element_id} ({description})")
        
        if missing_optimization:
            print("❌ 缺少参数优化元素:")
            for element in missing_optimization:
                print(f"   • {element}")
            return False
        else:
            print("✅ 参数优化元素完整")
        
        # 7. 检查操作按钮
        print(f"\n🔍 检查操作按钮...")
        button_elements = {
            'startBacktestBtn': '开始回测按钮',
            'parameterOptimizationBtn': '参数优化按钮',
            'loadSavedResultsBtn': '加载保存结果按钮'
        }
        
        missing_buttons = []
        for button_id, description in button_elements.items():
            if f'id="{button_id}"' not in page_content:
                missing_buttons.append(f"{button_id} ({description})")
        
        if missing_buttons:
            print("❌ 缺少操作按钮:")
            for button in missing_buttons:
                print(f"   • {button}")
            return False
        else:
            print("✅ 操作按钮完整")
        
        # 8. 检查结果显示区域
        print(f"\n🔍 检查结果显示区域...")
        result_elements = {
            'backtestCard': '回测结果卡片',
            'backtestStats': '回测统计区域',
            'backtestResults': '回测详细结果区域'
        }
        
        missing_results = []
        for element_id, description in result_elements.items():
            if f'id="{element_id}"' not in page_content:
                missing_results.append(f"{element_id} ({description})")
        
        if missing_results:
            print("❌ 缺少结果显示元素:")
            for element in missing_results:
                print(f"   • {element}")
            return False
        else:
            print("✅ 结果显示区域完整")
        
        # 9. 检查JavaScript函数
        print(f"\n🔍 检查JavaScript函数...")
        required_functions = [
            'startBacktest',
            'startParameterOptimization', 
            'loadSavedOptimizationResults',
            'applyBacktestPreset',
            'toggleTrailingStopConfig',
            'validateBacktestConfig',
            'getBacktestConfig',
            'updateStatus'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if f'function {func_name}' not in page_content and f'{func_name}(' not in page_content:
                missing_functions.append(func_name)
        
        if missing_functions:
            print("❌ 缺少JavaScript函数:")
            for func in missing_functions:
                print(f"   • {func}")
            return False
        else:
            print("✅ JavaScript函数完整")
        
        # 10. 检查外部JavaScript文件
        print(f"\n🔍 检查外部JavaScript文件...")
        if 'backtest_functions.js' in page_content:
            print("✅ 外部JavaScript文件正确引用")
        else:
            print("⚠️ 外部JavaScript文件可能未正确引用")
        
        # 11. 检查回测配置是否默认显示
        print(f"\n🔍 检查回测配置显示状态...")
        if 'id="backtestConfig" style="display: none;"' in page_content:
            print("❌ 回测配置默认隐藏，用户看不到完整功能")
            return False
        elif 'id="backtestConfig"' in page_content:
            print("✅ 回测配置默认显示，用户可以看到完整功能")
        else:
            print("❌ 未找到回测配置区域")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🔧 AI推理回测页面功能完整性测试工具")
    print("=" * 80)
    
    print("📋 测试内容:")
    print("• 基础配置元素（模型选择、日期范围等）")
    print("• 回测配置元素（资金、手数、止损止盈等）")
    print("• 参数优化元素（优化周期、风险偏好等）")
    print("• 操作按钮（开始回测、参数优化等）")
    print("• 结果显示区域（统计、详细结果等）")
    print("• JavaScript函数完整性")
    print("• 配置区域显示状态")
    print()
    
    success = test_backtest_functionality()
    
    if success:
        print("\n🎉 AI推理回测页面功能完整性测试通过！")
        print("✅ 所有必要的功能元素都已正确实现:")
        print("   • 完整的回测配置界面")
        print("   • 参数优化功能")
        print("   • 操作按钮和结果显示")
        print("   • JavaScript函数支持")
        print("   • 配置区域默认显示")
        print()
        print("💡 用户现在可以:")
        print("   • 选择模型和配置回测参数")
        print("   • 执行历史数据回测")
        print("   • 进行参数优化")
        print("   • 查看详细的回测结果")
    else:
        print("\n❌ AI推理回测页面功能不完整")
        print("🔧 请检查缺失的功能元素")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
