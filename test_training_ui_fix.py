#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试训练界面修复效果
验证"开始模型训练"按钮是否正常显示和工作
"""

import requests
import time
import json

def test_training_ui_fix():
    """测试训练界面修复效果"""
    print('🧪 测试训练界面修复效果')
    print('=' * 50)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 创建一个新的训练任务来测试
    print("\n📊 创建测试训练任务...")
    
    test_config = {
        'model_name': f'UI修复测试_{int(time.time())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'data_config': {'mode': 'days', 'training_days': 30},
        'sequence_length': 20,
        'hidden_size': 64,
        'num_layers': 2,
        'dropout': 0.2,
        'batch_size': 16,
        'learning_rate': 0.001,
        'epochs': 5,
        'patience': 3,
        'early_stopping': True,
        'min_epochs': 2,
        'use_gpu': True,
        'save_checkpoints': True,
        'use_enhanced_features': True,
        'feature_selection_strategy': 'recommended',
        'include_basic_features': True,
        'analyze_feature_importance': True,
        'auto_start_training': False  # 关闭自动启动，测试手动启动
    }
    
    try:
        response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                               json=test_config,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 测试任务创建成功: {task_id}")
                
                # 等待数据准备完成
                print("⏳ 等待数据准备完成...")
                
                max_wait_time = 120  # 最多等待2分钟
                start_time = time.time()
                
                while time.time() - start_time < max_wait_time:
                    # 检查任务状态
                    progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                    
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        if progress_data.get('success'):
                            progress = progress_data.get('progress', {})
                            status = progress.get('status', 'unknown')
                            progress_percent = progress.get('progress', 0)
                            
                            print(f"   状态: {status}, 进度: {progress_percent}%")
                            
                            if status == 'data_ready' and progress_percent >= 100:
                                print("✅ 数据准备完成！")
                                
                                # 测试手动启动模型训练
                                print("🧪 测试手动启动模型训练...")
                                
                                train_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/start-model-training/{task_id}',
                                                            headers={'Content-Type': 'application/json'})
                                
                                if train_response.status_code == 200:
                                    train_result = train_response.json()
                                    if train_result.get('success'):
                                        print("✅ 手动启动模型训练成功！")
                                        
                                        # 等待一下，检查训练是否真的开始了
                                        time.sleep(3)
                                        
                                        final_check = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                                        if final_check.status_code == 200:
                                            final_data = final_check.json()
                                            if final_data.get('success'):
                                                final_progress = final_data.get('progress', {})
                                                final_status = final_progress.get('status', 'unknown')
                                                
                                                if final_status == 'running':
                                                    print("✅ 训练已成功启动并运行中！")
                                                    return True
                                                else:
                                                    print(f"⚠️ 训练状态异常: {final_status}")
                                                    return False
                                    else:
                                        print(f"❌ 手动启动失败: {train_result.get('error')}")
                                        return False
                                else:
                                    print(f"❌ 启动请求失败: {train_response.status_code}")
                                    return False
                                
                            elif status == 'failed':
                                print(f"❌ 数据准备失败")
                                return False
                            
                            # 继续等待
                            time.sleep(5)
                        else:
                            print(f"❌ 获取进度失败: {progress_data.get('error')}")
                            return False
                    else:
                        print(f"❌ 进度请求失败: {progress_response.status_code}")
                        return False
                
                print(f"⏰ 等待超时，数据准备可能需要更长时间")
                return False
                
            else:
                print(f"❌ 任务创建失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def check_existing_data_ready_tasks():
    """检查现有的data_ready任务"""
    print('\n🔍 检查现有的data_ready任务')
    print('=' * 50)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 从数据库查询data_ready任务
    import sqlite3
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, model_id, status, progress, created_at, updated_at
            FROM training_tasks 
            WHERE status = 'data_ready'
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if tasks:
            print(f"📋 找到 {len(tasks)} 个data_ready状态的任务:")
            
            for i, task in enumerate(tasks, 1):
                task_id, model_id, status, progress, created_at, updated_at = task
                print(f"\n📊 任务 {i}: {task_id}")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   创建时间: {created_at}")
                
                # 测试手动启动
                print(f"🧪 测试手动启动: {task_id}")
                
                try:
                    train_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/start-model-training/{task_id}',
                                                headers={'Content-Type': 'application/json'})
                    
                    if train_response.status_code == 200:
                        train_result = train_response.json()
                        if train_result.get('success'):
                            print(f"   ✅ 手动启动成功")
                        else:
                            print(f"   ❌ 启动失败: {train_result.get('error')}")
                    else:
                        print(f"   ❌ 请求失败: {train_response.status_code}")
                        
                except Exception as e:
                    print(f"   ❌ 启动异常: {e}")
                
                # 只测试第一个任务
                if i == 1:
                    break
                    
        else:
            print("❌ 没有找到data_ready状态的任务")
            
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print('🔧 训练界面修复效果测试')
    print('=' * 80)
    
    # 检查现有任务
    existing_success = check_existing_data_ready_tasks()
    
    # 创建新任务测试
    new_task_success = test_training_ui_fix()
    
    print(f"\n🎯 测试结果总结:")
    print(f"   现有任务测试: {'✅ 成功' if existing_success else '❌ 失败'}")
    print(f"   新任务测试: {'✅ 成功' if new_task_success else '❌ 失败'}")
    
    if existing_success and new_task_success:
        print(f"\n🎉 训练界面修复测试成功！")
        print(f"💡 修复效果:")
        print(f"   ✅ data_ready状态的任务可以手动启动")
        print(f"   ✅ 前端按钮功能正常")
        print(f"   ✅ API调用成功")
        print(f"   ✅ 训练流程正常")
    else:
        print(f"\n❌ 训练界面修复测试失败")
        print(f"🔧 需要进一步检查:")
        if not existing_success:
            print(f"   - 现有任务启动问题")
        if not new_task_success:
            print(f"   - 新任务创建或启动问题")

if __name__ == "__main__":
    main()
