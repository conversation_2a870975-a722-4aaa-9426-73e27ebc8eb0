#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断卡住的训练任务
分析第8轮训练卡住的原因
"""

import sqlite3
import json
import psutil
import time
from datetime import datetime, timedelta
import threading
import os

def get_stuck_task_details():
    """获取卡住任务的详细信息"""
    print('🔍 获取卡住任务的详细信息')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询running状态的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs, config
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("❌ 没有找到正在运行的任务")
            return None
        
        # 分析第一个任务（最新的）
        task = tasks[0]
        task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs, config = task
        
        print(f"📊 任务详情:")
        print(f"   任务ID: {task_id}")
        print(f"   模型ID: {model_id}")
        print(f"   状态: {status}")
        print(f"   进度: {progress}%")
        print(f"   当前轮次: {current_epoch}/{total_epochs}")
        print(f"   创建时间: {created_at}")
        print(f"   最后更新: {updated_at}")
        
        # 计算卡住时间
        if updated_at:
            try:
                last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                now = datetime.now()
                stuck_time = now - last_update.replace(tzinfo=None)
                print(f"   卡住时间: {stuck_time}")
                
                if stuck_time.total_seconds() > 300:  # 5分钟
                    print(f"   ⚠️ 任务确实卡住了 (超过5分钟无更新)")
                else:
                    print(f"   ✅ 任务可能正常运行")
            except Exception as e:
                print(f"   ❌ 时间解析失败: {e}")
        
        # 解析日志
        if logs:
            try:
                if isinstance(logs, str):
                    log_data = json.loads(logs)
                else:
                    log_data = logs
                
                print(f"\n📋 最新日志信息:")
                print(f"   阶段: {log_data.get('stage', 'unknown')}")
                print(f"   消息: {log_data.get('message', 'N/A')}")
                
                if 'epoch' in log_data:
                    print(f"   轮次: {log_data.get('epoch', 'N/A')}")
                if 'train_loss' in log_data:
                    print(f"   训练损失: {log_data.get('train_loss', 'N/A')}")
                if 'val_loss' in log_data:
                    print(f"   验证损失: {log_data.get('val_loss', 'N/A')}")
                if 'train_acc' in log_data:
                    print(f"   训练准确率: {log_data.get('train_acc', 'N/A')}")
                if 'val_acc' in log_data:
                    print(f"   验证准确率: {log_data.get('val_acc', 'N/A')}")
                if 'patience_counter' in log_data:
                    print(f"   早停计数: {log_data.get('patience_counter', 'N/A')}")
                    
            except Exception as e:
                print(f"   ❌ 日志解析失败: {e}")
                print(f"   原始日志: {logs}")
        
        # 解析配置
        if config:
            try:
                if isinstance(config, str):
                    config_data = json.loads(config)
                else:
                    config_data = config
                
                print(f"\n⚙️ 训练配置:")
                print(f"   模型类型: {config_data.get('model_type', 'N/A')}")
                print(f"   批次大小: {config_data.get('batch_size', 'N/A')}")
                print(f"   学习率: {config_data.get('learning_rate', 'N/A')}")
                print(f"   序列长度: {config_data.get('sequence_length', 'N/A')}")
                print(f"   隐藏层大小: {config_data.get('hidden_size', 'N/A')}")
                print(f"   层数: {config_data.get('num_layers', 'N/A')}")
                print(f"   Dropout: {config_data.get('dropout', 'N/A')}")
                print(f"   早停耐心: {config_data.get('patience', 'N/A')}")
                print(f"   使用GPU: {config_data.get('use_gpu', 'N/A')}")
                
            except Exception as e:
                print(f"   ❌ 配置解析失败: {e}")
        
        return {
            'task_id': task_id,
            'model_id': model_id,
            'current_epoch': current_epoch,
            'total_epochs': total_epochs,
            'progress': progress,
            'logs': log_data if logs else {},
            'config': config_data if config else {}
        }
        
    except Exception as e:
        print(f"❌ 获取任务详情失败: {e}")
        return None

def check_system_resources():
    """检查系统资源使用情况"""
    print('\n🔍 检查系统资源使用情况')
    print('=' * 50)
    
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"💻 CPU使用率: {cpu_percent}%")
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        print(f"🧠 内存使用率: {memory.percent}%")
        print(f"   总内存: {memory.total / (1024**3):.1f} GB")
        print(f"   已用内存: {memory.used / (1024**3):.1f} GB")
        print(f"   可用内存: {memory.available / (1024**3):.1f} GB")
        
        # GPU使用情况 (简化检查)
        try:
            import subprocess
            result = subprocess.run(['nvidia-smi', '--query-gpu=name,utilization.gpu,memory.used,memory.total,temperature.gpu', '--format=csv,noheader,nounits'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                gpu_lines = result.stdout.strip().split('\n')
                print(f"🎮 GPU信息:")
                for i, line in enumerate(gpu_lines):
                    if line.strip():
                        parts = line.split(', ')
                        if len(parts) >= 5:
                            name, util, mem_used, mem_total, temp = parts[:5]
                            print(f"   GPU {i}: {name}")
                            print(f"     使用率: {util}%")
                            print(f"     内存使用: {mem_used}/{mem_total} MB")
                            print(f"     温度: {temp}°C")
                gpu_available = True
            else:
                print(f"🎮 GPU: 未检测到NVIDIA GPU或nvidia-smi不可用")
                gpu_available = False
        except Exception as e:
            print(f"🎮 GPU检查失败: {e}")
            gpu_available = False
        
        # 磁盘使用情况
        disk = psutil.disk_usage('.')
        print(f"💾 磁盘使用率: {disk.used / disk.total * 100:.1f}%")
        print(f"   总空间: {disk.total / (1024**3):.1f} GB")
        print(f"   已用空间: {disk.used / (1024**3):.1f} GB")
        print(f"   可用空间: {disk.free / (1024**3):.1f} GB")
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_available_gb': memory.available / (1024**3),
            'gpu_available': gpu_available,
            'disk_free_gb': disk.free / (1024**3)
        }
        
    except Exception as e:
        print(f"❌ 系统资源检查失败: {e}")
        return {}

def check_python_processes():
    """检查Python进程"""
    print('\n🔍 检查Python进程')
    print('=' * 50)
    
    try:
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'deep_learning_service' in cmdline or 'training' in cmdline.lower():
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline,
                            'cpu_percent': proc.info['cpu_percent'],
                            'memory_percent': proc.info['memory_percent']
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if python_processes:
            print(f"🐍 找到 {len(python_processes)} 个相关Python进程:")
            for proc in python_processes:
                print(f"   PID {proc['pid']}: {proc['name']}")
                print(f"     CPU: {proc['cpu_percent']}%")
                print(f"     内存: {proc['memory_percent']:.1f}%")
                print(f"     命令: {proc['cmdline'][:100]}...")
        else:
            print(f"🐍 没有找到相关的Python训练进程")
            
        return python_processes
        
    except Exception as e:
        print(f"❌ 检查Python进程失败: {e}")
        return []

def analyze_possible_causes(task_info, system_info, processes):
    """分析可能的卡住原因"""
    print('\n🔍 分析可能的卡住原因')
    print('=' * 50)
    
    possible_causes = []
    
    # 1. 检查系统资源
    if system_info.get('memory_percent', 0) > 90:
        possible_causes.append({
            'cause': '内存不足',
            'description': f"内存使用率 {system_info['memory_percent']}% 过高",
            'severity': 'high',
            'solution': '关闭其他程序释放内存，或减小batch_size'
        })
    
    if system_info.get('memory_available_gb', 0) < 1:
        possible_causes.append({
            'cause': '可用内存过低',
            'description': f"可用内存仅 {system_info['memory_available_gb']:.1f} GB",
            'severity': 'high',
            'solution': '释放内存或重启系统'
        })
    
    if system_info.get('disk_free_gb', 0) < 1:
        possible_causes.append({
            'cause': '磁盘空间不足',
            'description': f"可用磁盘空间仅 {system_info['disk_free_gb']:.1f} GB",
            'severity': 'medium',
            'solution': '清理磁盘空间'
        })
    
    # 2. 检查GPU相关
    if task_info.get('config', {}).get('use_gpu', False) and not system_info.get('gpu_available', False):
        possible_causes.append({
            'cause': 'GPU不可用',
            'description': '配置使用GPU但GPU不可用',
            'severity': 'high',
            'solution': '检查GPU驱动或改为CPU训练'
        })
    
    # 3. 检查训练参数
    config = task_info.get('config', {})
    if config.get('batch_size', 0) > 64:
        possible_causes.append({
            'cause': '批次大小过大',
            'description': f"batch_size = {config['batch_size']} 可能导致内存不足",
            'severity': 'medium',
            'solution': '减小batch_size到32或16'
        })
    
    if config.get('sequence_length', 0) > 100:
        possible_causes.append({
            'cause': '序列长度过长',
            'description': f"sequence_length = {config['sequence_length']} 可能导致计算复杂度过高",
            'severity': 'medium',
            'solution': '减小sequence_length'
        })
    
    # 4. 检查进程状态
    if not processes:
        possible_causes.append({
            'cause': '训练进程丢失',
            'description': '没有找到活跃的训练进程',
            'severity': 'high',
            'solution': '重启训练任务'
        })
    
    # 5. 检查训练进度
    current_epoch = task_info.get('current_epoch', 0)
    if current_epoch == 8:
        possible_causes.append({
            'cause': '第8轮特定问题',
            'description': '可能在第8轮遇到梯度爆炸或数值不稳定',
            'severity': 'medium',
            'solution': '降低学习率或添加梯度裁剪'
        })
    
    # 输出分析结果
    if possible_causes:
        print(f"🚨 发现 {len(possible_causes)} 个可能原因:")
        for i, cause in enumerate(possible_causes, 1):
            severity_icon = '🔴' if cause['severity'] == 'high' else '🟡' if cause['severity'] == 'medium' else '🟢'
            print(f"\n{i}. {severity_icon} {cause['cause']} ({cause['severity'].upper()})")
            print(f"   描述: {cause['description']}")
            print(f"   解决方案: {cause['solution']}")
    else:
        print("✅ 没有发现明显的问题原因")
    
    return possible_causes

def provide_solutions(task_info, causes):
    """提供解决方案"""
    print('\n🔧 解决方案建议')
    print('=' * 50)
    
    task_id = task_info.get('task_id', 'unknown')
    
    print("立即解决方案:")
    print(f"1. 🛑 停止卡住的任务:")
    print(f"   python stop_training.py {task_id}")
    
    print(f"\n2. 🔄 重启训练 (优化参数):")
    print(f"   - 减小batch_size: 从 {task_info.get('config', {}).get('batch_size', 'N/A')} 改为 16")
    print(f"   - 降低学习率: 从 {task_info.get('config', {}).get('learning_rate', 'N/A')} 改为 0.0001")
    print(f"   - 添加梯度裁剪: max_grad_norm = 1.0")
    
    print(f"\n3. 🧹 清理系统资源:")
    print(f"   - 关闭不必要的程序")
    print(f"   - 清理临时文件")
    print(f"   - 重启Python进程")
    
    # 根据具体原因提供针对性建议
    high_priority_causes = [c for c in causes if c['severity'] == 'high']
    if high_priority_causes:
        print(f"\n🚨 高优先级问题解决:")
        for cause in high_priority_causes:
            print(f"   - {cause['cause']}: {cause['solution']}")

def main():
    """主函数"""
    print('🔧 训练卡住问题诊断')
    print('=' * 80)
    
    # 获取卡住任务详情
    task_info = get_stuck_task_details()
    
    if not task_info:
        print("❌ 没有找到卡住的训练任务")
        return
    
    # 检查系统资源
    system_info = check_system_resources()
    
    # 检查Python进程
    processes = check_python_processes()
    
    # 分析可能原因
    causes = analyze_possible_causes(task_info, system_info, processes)
    
    # 提供解决方案
    provide_solutions(task_info, causes)
    
    print(f"\n✅ 诊断完成！")
    print(f"💡 建议优先处理高优先级问题，然后重启训练")

if __name__ == "__main__":
    main()
