#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动高标准训练配置
"""

import requests
import time
import json

def login_session():
    """登录并返回会话"""
    session = requests.Session()
    
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def start_high_standard_training():
    """启动高标准训练配置"""
    print('🚀 启动高标准训练配置')
    print('=' * 50)
    
    session = login_session()
    if not session:
        return
    
    # 恢复到原始的高标准配置
    high_standard_configs = [
        {
            'model_name': f'高标准基础模型_{int(time.time())}',
            'model_type': 'lstm',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'mode': 'days',
                'training_days': 365  # 1年数据
            },
            'sequence_length': 60,      # 长序列
            'hidden_size': 128,         # 大隐藏层
            'num_layers': 3,            # 多层LSTM
            'dropout': 0.3,             # 标准dropout
            'batch_size': 32,           # 标准批次大小
            'learning_rate': 0.001,
            'epochs': 200,              # 充分训练轮数
            'patience': 15,             # 大耐心值
            'early_stopping': True,
            'min_epochs': 20,           # 更多最小轮数
            'use_gpu': True,
            'save_checkpoints': True,
            # 使用基础特征
            'use_enhanced_features': False,
            'features': {
                'price': True,
                'volume': True,
                'technical': True,
                'time': True
            }
        },
        {
            'model_name': f'高标准推荐增强特征模型_{int(time.time())}',
            'model_type': 'lstm',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'mode': 'days',
                'training_days': 365
            },
            'sequence_length': 60,
            'hidden_size': 128,
            'num_layers': 3,
            'dropout': 0.3,
            'batch_size': 32,
            'learning_rate': 0.001,
            'epochs': 200,
            'patience': 15,
            'early_stopping': True,
            'min_epochs': 20,
            'use_gpu': True,
            'save_checkpoints': True,
            # 使用推荐增强特征（31个特征）
            'use_enhanced_features': True,
            'feature_selection_strategy': 'recommended',
            'analyze_feature_importance': True,
            'selected_features': None
        },
        {
            'model_name': f'高标准全部增强特征模型_{int(time.time())}',
            'model_type': 'lstm',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'mode': 'days',
                'training_days': 365
            },
            'sequence_length': 60,
            'hidden_size': 128,
            'num_layers': 3,
            'dropout': 0.3,
            'batch_size': 32,
            'learning_rate': 0.001,
            'epochs': 200,
            'patience': 15,
            'early_stopping': True,
            'min_epochs': 20,
            'use_gpu': True,
            'save_checkpoints': True,
            # 使用全部增强特征（52个特征）
            'use_enhanced_features': True,
            'feature_selection_strategy': 'all',
            'analyze_feature_importance': True,
            'selected_features': None
        }
    ]
    
    started_tasks = []
    
    for i, config in enumerate(high_standard_configs):
        print(f"\n📊 启动配置 {i+1}: {config['model_name']}")
        print(f"   特征类型: {'增强特征' if config['use_enhanced_features'] else '基础特征'}")
        if config['use_enhanced_features']:
            strategy = config['feature_selection_strategy']
            feature_count = '31个' if strategy == 'recommended' else '52个' if strategy == 'all' else '自定义'
            print(f"   特征策略: {strategy} ({feature_count}特征)")
        print(f"   模型复杂度: 高标准 (hidden_size={config['hidden_size']}, layers={config['num_layers']})")
        print(f"   训练数据: {config['data_config']['training_days']}天")
        print(f"   训练轮数: {config['epochs']}")
        print(f"   序列长度: {config['sequence_length']}")
        print(f"   批次大小: {config['batch_size']}")
        
        try:
            response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                                   json=config,
                                   headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    task_id = result.get('task_id')
                    print(f"   ✅ 启动成功: {task_id}")
                    started_tasks.append({
                        'task_id': task_id,
                        'name': config['model_name'],
                        'type': '增强特征' if config['use_enhanced_features'] else '基础特征',
                        'strategy': config.get('feature_selection_strategy', 'basic')
                    })
                else:
                    print(f"   ❌ 启动失败: {result.get('error')}")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 启动异常: {e}")
        
        # 等待一下再启动下一个
        time.sleep(3)
    
    # 总结
    print(f"\n🎯 高标准训练启动总结:")
    print(f"   成功启动: {len(started_tasks)} 个任务")
    
    for task in started_tasks:
        print(f"   - {task['name']} ({task['type']}): {task['task_id']}")
    
    if started_tasks:
        print(f"\n💡 高标准配置说明:")
        print(f"   ✅ 训练数据: 365天（1年完整数据）")
        print(f"   ✅ 模型复杂度: hidden_size=128, layers=3")
        print(f"   ✅ 序列长度: 60（更长的时间序列）")
        print(f"   ✅ 批次大小: 32（标准大小）")
        print(f"   ✅ 训练轮数: 200（充分训练）")
        print(f"   ✅ 早停耐心: 15（避免过早停止）")
        print(f"   ✅ 增强特征: 推荐31个 + 全部52个特征")
        
        print(f"\n📊 预期效果:")
        print(f"   🎯 更高的预测准确性")
        print(f"   🎯 更好的泛化能力")
        print(f"   🎯 充分利用增强特征优势")
        print(f"   🎯 专业级的训练质量")
        
        print(f"\n⚠️ 注意事项:")
        print(f"   ⏰ 训练时间较长（可能数小时）")
        print(f"   💾 需要充足的GPU内存和计算资源")
        print(f"   📊 建议监控训练进度和系统资源")
        
        print(f"\n🔄 下一步操作:")
        print(f"   1. 等待数据准备完成（可能需要10-20分钟）")
        print(f"   2. 运行: python start_pending_training.py")
        print(f"   3. 监控: python check_training_status.py")
    else:
        print(f"\n❌ 没有成功启动任何高标准训练任务")

if __name__ == "__main__":
    start_high_standard_training()
