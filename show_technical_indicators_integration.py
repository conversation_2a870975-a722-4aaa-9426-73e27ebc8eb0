#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
展示技术指标在深度学习模型训练中的完整集成过程
基于实际的deep_learning_service.py代码
"""

import numpy as np
import pandas as pd
import sys
import os

# 添加服务路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'services'))

try:
    from services.technical_indicators import TechnicalIndicators
except ImportError:
    print("⚠️ 无法导入TechnicalIndicators，使用简化实现")
    
    class TechnicalIndicators:
        def bollinger_bands(self, data, period=20, std_dev=2):
            df = pd.DataFrame({'close': data})
            sma = df['close'].rolling(window=period).mean()
            std = df['close'].rolling(window=period).std()
            return {
                'upper': sma + (std * std_dev),
                'middle': sma,
                'lower': sma - (std * std_dev)
            }

def simulate_mt5_price_data(days=100):
    """模拟MT5价格数据格式 [open, high, low, close, volume]"""
    print("📊 模拟MT5价格数据...")
    
    np.random.seed(42)
    base_price = 2000.0
    
    data = []
    current_price = base_price
    
    for i in range(days):
        # 生成价格变化
        change = np.random.normal(0, 10)
        current_price = max(current_price + change, 1800)
        
        # 生成OHLC
        volatility = np.random.uniform(5, 15)
        high = current_price + np.random.uniform(0, volatility)
        low = current_price - np.random.uniform(0, volatility)
        open_price = low + np.random.uniform(0, high - low)
        close = current_price
        volume = np.random.uniform(1000, 5000)
        
        data.append([open_price, high, low, close, volume])
    
    return np.array(data)

def calculate_enhanced_technical_indicators(price_data):
    """
    计算增强的技术指标特征
    这是deep_learning_service.py中实际使用的增强版本
    """
    print("\n🔧 计算增强技术指标特征...")
    print("=" * 50)
    
    # 提取价格数据
    open_prices = price_data[:, 0]
    high_prices = price_data[:, 1]
    low_prices = price_data[:, 2]
    close_prices = price_data[:, 3]
    volumes = price_data[:, 4]
    
    # 转换为pandas Series
    close_series = pd.Series(close_prices)
    high_series = pd.Series(high_prices)
    low_series = pd.Series(low_prices)
    volume_series = pd.Series(volumes)
    
    features = {}
    
    # 1. 布林带特征 (最重要的技术指标)
    print("📈 计算布林带特征...")
    ti = TechnicalIndicators()
    bb = ti.bollinger_bands(close_series, period=20, std_dev=2)
    
    features['bb_upper'] = bb['upper'].fillna(close_series)
    features['bb_middle'] = bb['middle'].fillna(close_series)
    features['bb_lower'] = bb['lower'].fillna(close_series)
    
    # 布林带衍生特征
    bb_width = bb['upper'] - bb['lower']
    bb_position = (close_series - bb['lower']) / (bb_width + 1e-10)
    
    features['bb_width'] = bb_width.fillna(0)
    features['bb_position'] = bb_position.fillna(0.5)
    
    print(f"   布林带上轨: {features['bb_upper'].min():.2f} - {features['bb_upper'].max():.2f}")
    print(f"   布林带位置: {features['bb_position'].min():.3f} - {features['bb_position'].max():.3f}")
    
    # 2. 移动平均线特征
    print("📊 计算移动平均线特征...")
    features['sma_5'] = close_series.rolling(5).mean().fillna(close_series)
    features['sma_10'] = close_series.rolling(10).mean().fillna(close_series)
    features['sma_20'] = close_series.rolling(20).mean().fillna(close_series)
    features['sma_50'] = close_series.rolling(50).mean().fillna(close_series)
    
    features['ema_12'] = close_series.ewm(span=12).mean()
    features['ema_26'] = close_series.ewm(span=26).mean()
    
    # 3. MACD特征
    print("⚡ 计算MACD特征...")
    macd_line = features['ema_12'] - features['ema_26']
    macd_signal = macd_line.ewm(span=9).mean()
    macd_histogram = macd_line - macd_signal
    
    features['macd_line'] = macd_line
    features['macd_signal'] = macd_signal
    features['macd_histogram'] = macd_histogram
    
    # 4. RSI特征
    print("📉 计算RSI特征...")
    price_changes = close_series.diff()
    gains = price_changes.where(price_changes > 0, 0)
    losses = -price_changes.where(price_changes < 0, 0)
    
    avg_gains = gains.rolling(14).mean()
    avg_losses = losses.rolling(14).mean()
    
    rs = avg_gains / (avg_losses + 1e-10)
    rsi = 100 - (100 / (1 + rs))
    features['rsi'] = rsi.fillna(50)
    
    # 5. 随机指标
    print("🎯 计算随机指标...")
    lowest_low = low_series.rolling(14).min()
    highest_high = high_series.rolling(14).max()
    
    k_percent = 100 * (close_series - lowest_low) / (highest_high - lowest_low + 1e-10)
    d_percent = k_percent.rolling(3).mean()
    
    features['stoch_k'] = k_percent.fillna(50)
    features['stoch_d'] = d_percent.fillna(50)
    
    # 6. ATR (平均真实波幅)
    print("📏 计算ATR...")
    high_low = high_series - low_series
    high_close = np.abs(high_series - close_series.shift())
    low_close = np.abs(low_series - close_series.shift())
    
    true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    atr = true_range.rolling(14).mean()
    features['atr'] = atr.fillna(true_range.mean())
    
    # 7. 威廉指标
    print("⚡ 计算威廉指标...")
    williams_r = -100 * (highest_high - close_series) / (highest_high - lowest_low + 1e-10)
    features['williams_r'] = williams_r.fillna(-50)
    
    # 8. 价格动量特征
    print("🚀 计算价格动量特征...")
    features['price_change_1'] = close_series.pct_change(1).fillna(0)
    features['price_change_5'] = close_series.pct_change(5).fillna(0)
    features['price_momentum'] = close_series / close_series.shift(10) - 1
    features['price_momentum'] = features['price_momentum'].fillna(0)
    
    # 9. 成交量特征
    print("📦 计算成交量特征...")
    features['volume_sma'] = volume_series.rolling(20).mean().fillna(volume_series.mean())
    features['volume_ratio'] = volume_series / features['volume_sma']
    
    print(f"✅ 计算完成，共生成 {len(features)} 个技术指标特征")
    
    return features

def prepare_features_for_lstm_model(price_data, technical_features):
    """
    为LSTM模型准备特征矩阵
    这是deep_learning_service.py中_prepare_training_data的核心部分
    """
    print(f"\n🤖 为LSTM模型准备特征矩阵")
    print("-" * 40)
    
    all_features = []
    feature_names = []
    
    # 1. 基础价格特征 (标准化)
    print("📊 处理基础价格特征...")
    price_features = price_data[:, [0, 1, 2, 3]]  # OHLC
    
    # 标准化价格特征
    price_normalized = (price_features - price_features.mean(axis=0)) / (price_features.std(axis=0) + 1e-10)
    all_features.append(price_normalized)
    feature_names.extend(['open_norm', 'high_norm', 'low_norm', 'close_norm'])
    
    # 2. 技术指标特征 (标准化)
    print("📈 处理技术指标特征...")
    
    # 重要的技术指标特征
    important_indicators = [
        'bb_position',      # 布林带位置 (最重要)
        'bb_width',         # 布林带宽度
        'rsi',              # RSI
        'macd_line',        # MACD线
        'macd_histogram',   # MACD柱状图
        'stoch_k',          # 随机指标K
        'atr',              # ATR
        'williams_r',       # 威廉指标
        'price_change_1',   # 1期价格变化
        'price_momentum',   # 价格动量
        'volume_ratio'      # 成交量比率
    ]
    
    for indicator in important_indicators:
        if indicator in technical_features:
            feature_data = technical_features[indicator].values
            
            # 标准化
            feature_normalized = (feature_data - feature_data.mean()) / (feature_data.std() + 1e-10)
            all_features.append(feature_normalized.reshape(-1, 1))
            feature_names.append(indicator)
            
            print(f"   {indicator}: 范围 {feature_normalized.min():.3f} - {feature_normalized.max():.3f}")
    
    # 3. 合并所有特征
    feature_matrix = np.concatenate(all_features, axis=1)
    
    print(f"\n✅ 特征矩阵准备完成:")
    print(f"   形状: {feature_matrix.shape}")
    print(f"   特征数量: {len(feature_names)}")
    print(f"   特征列表: {feature_names}")
    
    return feature_matrix, feature_names

def create_lstm_sequences(feature_matrix, sequence_length=60):
    """
    创建LSTM训练序列
    这是deep_learning_service.py中_create_sequences的实现
    """
    print(f"\n🔄 创建LSTM训练序列 (序列长度: {sequence_length})")
    print("-" * 50)
    
    X, y = [], []
    
    for i in range(sequence_length, len(feature_matrix)):
        # 输入序列：过去sequence_length个时间步的所有特征
        sequence = feature_matrix[i-sequence_length:i]
        X.append(sequence)
        
        # 目标：下一个时间步的价格变化方向
        # 使用标准化后的收盘价特征 (索引3)
        current_close = feature_matrix[i-1, 3]
        next_close = feature_matrix[i, 3]
        
        # 二分类：1=上涨，0=下跌
        target = 1 if next_close > current_close else 0
        y.append(target)
    
    X = np.array(X)
    y = np.array(y)
    
    print(f"✅ LSTM序列创建完成:")
    print(f"   输入形状: {X.shape} (样本数, 序列长度, 特征数)")
    print(f"   目标形状: {y.shape}")
    print(f"   上涨样本: {np.sum(y)} ({np.mean(y)*100:.1f}%)")
    print(f"   下跌样本: {len(y) - np.sum(y)} ({(1-np.mean(y))*100:.1f}%)")
    
    return X, y

def demonstrate_model_training_pipeline():
    """演示完整的模型训练管道"""
    print("🚀 深度学习模型训练管道演示")
    print("=" * 60)
    
    # 1. 获取价格数据 (模拟MT5数据)
    price_data = simulate_mt5_price_data(days=200)
    print(f"📊 价格数据: {price_data.shape}")
    
    # 2. 计算技术指标
    technical_features = calculate_enhanced_technical_indicators(price_data)
    
    # 3. 准备模型特征
    feature_matrix, feature_names = prepare_features_for_lstm_model(price_data, technical_features)
    
    # 4. 创建LSTM序列
    X, y = create_lstm_sequences(feature_matrix)
    
    # 5. 展示关键技术指标的作用
    print(f"\n🎯 关键技术指标在模型中的作用:")
    print("-" * 40)
    
    # 布林带位置的重要性
    bb_position = technical_features['bb_position'].values
    print(f"📈 布林带位置特征:")
    print(f"   超卖区域 (<0.2): {np.mean(bb_position < 0.2)*100:.1f}%")
    print(f"   中性区域 (0.2-0.8): {np.mean((bb_position >= 0.2) & (bb_position <= 0.8))*100:.1f}%")
    print(f"   超买区域 (>0.8): {np.mean(bb_position > 0.8)*100:.1f}%")
    
    # RSI的分布
    rsi = technical_features['rsi'].values
    print(f"📉 RSI特征:")
    print(f"   超卖区域 (<30): {np.mean(rsi < 30)*100:.1f}%")
    print(f"   中性区域 (30-70): {np.mean((rsi >= 30) & (rsi <= 70))*100:.1f}%")
    print(f"   超买区域 (>70): {np.mean(rsi > 70)*100:.1f}%")
    
    print(f"\n🎉 演示完成！")
    print(f"📋 总结:")
    print(f"   • 原始价格数据: {price_data.shape}")
    print(f"   • 技术指标数量: {len(technical_features)}")
    print(f"   • 模型特征矩阵: {feature_matrix.shape}")
    print(f"   • LSTM训练数据: {X.shape} -> {y.shape}")
    
    return X, y, feature_matrix, technical_features

if __name__ == "__main__":
    demonstrate_model_training_pipeline()
