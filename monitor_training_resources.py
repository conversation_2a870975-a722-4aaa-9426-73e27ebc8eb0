
import psutil
import torch
import time
import threading
from datetime import datetime

def monitor_training_resources():
    """监控训练资源使用"""
    print("🔍 开始监控训练资源...")
    
    while True:
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available / (1024**3)  # GB
            
            # GPU使用（如果可用）
            gpu_info = "N/A"
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.memory_allocated() / (1024**3)  # GB
                gpu_memory_max = torch.cuda.max_memory_allocated() / (1024**3)  # GB
                gpu_info = f"{gpu_memory:.2f}GB / {gpu_memory_max:.2f}GB"
            
            # 磁盘使用
            disk = psutil.disk_usage('.')
            disk_free = disk.free / (1024**3)  # GB
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] "
                  f"CPU: {cpu_percent:5.1f}% | "
                  f"内存: {memory_percent:5.1f}% ({memory_available:.1f}GB可用) | "
                  f"GPU: {gpu_info} | "
                  f"磁盘: {disk_free:.1f}GB可用")
            
            # 检查异常情况
            if cpu_percent > 95:
                print("⚠️ CPU使用率过高!")
            if memory_percent > 90:
                print("⚠️ 内存使用率过高!")
            if disk_free < 1:
                print("⚠️ 磁盘空间不足!")
            
            time.sleep(5)  # 每5秒监控一次
            
        except KeyboardInterrupt:
            print("监控停止")
            break
        except Exception as e:
            print(f"监控异常: {e}")
            time.sleep(5)

if __name__ == "__main__":
    monitor_training_resources()
