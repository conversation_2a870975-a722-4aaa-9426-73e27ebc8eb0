#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试AI推理回测页面的所有改进
验证功能完整性和用户体验
"""

import requests
from bs4 import BeautifulSoup
import re

def test_final_backtest_improvements():
    """测试最终的回测页面改进"""
    print("🧪 最终测试AI推理回测页面改进")
    print("=" * 70)
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 2. 访问AI推理回测页面
        print(f"\n📋 访问AI推理回测页面...")
        response = session.get('http://127.0.0.1:5000/deep-learning/backtest')
        
        if response.status_code != 200:
            print(f"❌ 访问页面失败: {response.status_code}")
            return False
        
        print("✅ 页面访问成功")
        
        # 3. 解析页面内容
        soup = BeautifulSoup(response.text, 'html.parser')
        page_content = response.text
        
        success = True
        
        # 4. 检查页面标题和描述
        print(f"\n🔍 检查页面标题和描述...")
        page_title = soup.find('title')
        if page_title and 'AI推理回测' in page_title.get_text():
            print("✅ 页面标题正确")
        else:
            print("❌ 页面标题不正确")
            print(f"   实际页面标题: {page_title.get_text() if page_title else 'None'}")
            success = False

        # 查找所有h1标签，找到包含AI推理回测的那个
        main_titles = soup.find_all('h1')
        backtest_title_found = False
        for title in main_titles:
            title_text = title.get_text()
            if 'AI推理回测' in title_text:
                backtest_title_found = True
                if '历史数据' in title_text or '验证' in title_text:
                    print("✅ 主标题和描述正确")
                else:
                    print("✅ 主标题正确（描述可能在其他位置）")
                break

        if not backtest_title_found:
            print("❌ 未找到AI推理回测主标题")
            success = False
        
        # 5. 检查功能提示区域
        print(f"\n🔍 检查功能提示区域...")
        hint_area = soup.find('div', id='backtestFeaturesHint')
        if hint_area:
            print("✅ 找到功能提示区域")
            if '参数优化' in hint_area.get_text() and '风险分析' in hint_area.get_text():
                print("✅ 提示内容完整")
            else:
                print("⚠️ 提示内容可能不完整")
        else:
            print("❌ 未找到功能提示区域")
            success = False
        
        # 6. 检查回测配置是否默认显示
        print(f"\n🔍 检查回测配置显示状态...")
        if 'id="backtestConfig" style="display: none;"' in page_content:
            print("❌ 回测配置仍然默认隐藏")
            success = False
        elif 'id="backtestConfig"' in page_content:
            print("✅ 回测配置默认显示")
        else:
            print("❌ 未找到回测配置区域")
            success = False
        
        # 7. 检查新增的操作按钮
        print(f"\n🔍 检查新增的操作按钮...")
        new_buttons = {
            'resetBacktestConfig': '重置配置按钮',
            'saveBacktestConfig': '保存配置按钮'
        }
        
        for button_func, description in new_buttons.items():
            if button_func in page_content:
                print(f"✅ 找到{description}")
            else:
                print(f"❌ 未找到{description}")
                success = False
        
        # 8. 检查进度条功能
        print(f"\n🔍 检查进度条功能...")
        progress_elements = [
            'progressContainer',
            'progressBar',
            'progressText',
            'updateProgress'
        ]
        
        missing_progress = []
        for element in progress_elements:
            if element not in page_content:
                missing_progress.append(element)
        
        if missing_progress:
            print("❌ 缺少进度条相关元素:")
            for element in missing_progress:
                print(f"   • {element}")
            success = False
        else:
            print("✅ 进度条功能完整")
        
        # 9. 检查配置管理功能
        print(f"\n🔍 检查配置管理功能...")
        config_functions = [
            'resetBacktestConfig',
            'saveBacktestConfig',
            'loadSavedBacktestConfig'
        ]
        
        missing_config_funcs = []
        for func in config_functions:
            if f'function {func}' not in page_content:
                missing_config_funcs.append(func)
        
        if missing_config_funcs:
            print("❌ 缺少配置管理函数:")
            for func in missing_config_funcs:
                print(f"   • {func}")
            success = False
        else:
            print("✅ 配置管理功能完整")
        
        # 10. 检查外部JavaScript文件
        print(f"\n🔍 检查外部JavaScript文件...")
        if 'backtest_functions.js' in page_content:
            print("✅ 外部JavaScript文件正确引用")
        else:
            print("❌ 外部JavaScript文件未正确引用")
            success = False
        
        # 11. 检查样式和UI改进
        print(f"\n🔍 检查样式和UI改进...")
        ui_elements = [
            'backtest-card',
            'metric-card',
            'status-indicator',
            'progress-bar',
            'btn-gradient'
        ]
        
        found_ui_elements = []
        for element in ui_elements:
            if element in page_content:
                found_ui_elements.append(element)
        
        if len(found_ui_elements) >= 3:
            print(f"✅ UI样式改进完整 (找到 {len(found_ui_elements)}/{len(ui_elements)} 个样式元素)")
        else:
            print(f"⚠️ UI样式可能不完整 (找到 {len(found_ui_elements)}/{len(ui_elements)} 个样式元素)")
        
        # 12. 检查核心回测功能
        print(f"\n🔍 检查核心回测功能...")
        core_functions = [
            'startBacktest',
            'startParameterOptimization',
            'displayBacktestResults',
            'validateBacktestConfig',
            'applyBacktestPreset'
        ]
        
        missing_core_funcs = []
        for func in core_functions:
            if func not in page_content:
                missing_core_funcs.append(func)
        
        if missing_core_funcs:
            print("❌ 缺少核心回测函数:")
            for func in missing_core_funcs:
                print(f"   • {func}")
            success = False
        else:
            print("✅ 核心回测功能完整")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🔧 AI推理回测页面最终改进测试工具")
    print("=" * 80)
    
    print("📋 测试内容:")
    print("• 页面标题和描述")
    print("• 功能提示区域")
    print("• 回测配置显示状态")
    print("• 新增操作按钮")
    print("• 进度条功能")
    print("• 配置管理功能")
    print("• 外部JavaScript文件")
    print("• UI样式改进")
    print("• 核心回测功能")
    print()
    
    success = test_final_backtest_improvements()
    
    if success:
        print("\n🎉 AI推理回测页面最终改进测试通过！")
        print("✅ 所有功能都已正确实现:")
        print("   • 完整的回测配置界面（默认显示）")
        print("   • 参数优化和结果分析功能")
        print("   • 配置保存和加载功能")
        print("   • 进度指示和状态更新")
        print("   • 现代化的UI设计")
        print("   • 完整的JavaScript功能支持")
        print()
        print("🎯 用户现在可以:")
        print("   • 立即看到完整的回测配置选项")
        print("   • 执行专业级的历史数据回测")
        print("   • 进行参数优化找到最佳策略")
        print("   • 保存和重用回测配置")
        print("   • 实时查看回测进度")
        print("   • 分析详细的回测结果")
        print()
        print("💡 页面特色:")
        print("   • 功能完整：媲美专业交易软件")
        print("   • 界面友好：现代化设计和交互")
        print("   • 操作简便：一键配置和执行")
        print("   • 结果详细：全面的统计和分析")
    else:
        print("\n❌ AI推理回测页面改进测试失败")
        print("🔧 请检查缺失的功能元素")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
