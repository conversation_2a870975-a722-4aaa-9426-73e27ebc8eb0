#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断AI推理交易页面实时市场数据显示问题
检查市场数据获取、更新机制、前端显示等环节
"""

import requests
import json
import time
from datetime import datetime

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post('http://127.0.0.1:5000/login', data=login_data)
    if response.status_code == 200:
        print("✅ 登录成功")
        return session
    else:
        print(f"❌ 登录失败: {response.status_code}")
        return None

def check_mt5_connection(session):
    """检查MT5连接状态"""
    print("\n🔗 检查MT5连接状态")
    print("=" * 60)
    
    try:
        response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        
        if response.status_code == 200:
            result = response.json()
            print(f"📊 MT5连接状态: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success') and result.get('connected'):
                print("✅ MT5连接正常")
                return True
            else:
                print("❌ MT5未连接")
                print("💡 可能原因:")
                print("   1. MT5终端未启动")
                print("   2. MT5账户未登录")
                print("   3. 专家顾问设置未正确配置")
                return False
        else:
            print(f"❌ 检查MT5连接失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查MT5连接异常: {e}")
        return False

def test_market_data_api(session, symbol='XAUUSD'):
    """测试市场数据API"""
    print(f"\n📊 测试市场数据API - {symbol}")
    print("=" * 60)
    
    try:
        # 测试市场数据API
        response = session.get(f'http://127.0.0.1:5000/api/mt5/market-data/{symbol}')
        
        if response.status_code == 200:
            result = response.json()
            print(f"📈 市场数据API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success') and result.get('market_data'):
                market_data = result['market_data']
                print(f"✅ 市场数据获取成功:")
                print(f"   品种: {market_data.get('symbol')}")
                print(f"   买价: {market_data.get('bid')}")
                print(f"   卖价: {market_data.get('ask')}")
                print(f"   时间: {market_data.get('time')}")
                
                # 计算点差
                if market_data.get('bid') and market_data.get('ask'):
                    spread = (market_data['ask'] - market_data['bid']) * 100000
                    print(f"   点差: {spread:.1f} 点")
                
                return True, market_data
            else:
                print(f"❌ 市场数据获取失败: {result.get('error')}")
                return False, None
        else:
            print(f"❌ 市场数据API请求失败: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ 测试市场数据API异常: {e}")
        return False, None

def check_model_selection(session):
    """检查模型选择状态"""
    print("\n🤖 检查模型选择状态")
    print("=" * 60)
    
    try:
        # 获取可用模型
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('models'):
                models = result['models']
                completed_models = [m for m in models if m.get('status') == 'completed']
                
                print(f"📊 模型统计:")
                print(f"   总模型数: {len(models)}")
                print(f"   完成训练: {len(completed_models)}")
                
                if completed_models:
                    print(f"✅ 可用模型:")
                    for i, model in enumerate(completed_models[:3]):  # 显示前3个
                        print(f"   {i+1}. {model['name']} ({model['symbol']})")
                    
                    # 检查自动交易状态
                    auto_trading_response = session.get('http://127.0.0.1:5000/api/deep-learning/auto-trading/status')
                    if auto_trading_response.status_code == 200:
                        auto_result = auto_trading_response.json()
                        if auto_result.get('success') and auto_result.get('active'):
                            model_info = auto_result.get('model_info', {})
                            print(f"🔄 当前选择的交易模型:")
                            print(f"   名称: {model_info.get('name')}")
                            print(f"   品种: {model_info.get('symbol')}")
                            return True, model_info.get('symbol', 'XAUUSD')
                        else:
                            print("⚠️ 自动交易未启动，无法确定选择的模型")
                            return True, completed_models[0]['symbol']
                    else:
                        print("⚠️ 无法获取自动交易状态")
                        return True, completed_models[0]['symbol']
                else:
                    print("❌ 没有完成训练的模型")
                    return False, None
            else:
                print(f"❌ 获取模型列表失败: {result.get('error')}")
                return False, None
        else:
            print(f"❌ 获取模型列表请求失败: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ 检查模型选择异常: {e}")
        return False, None

def analyze_market_data_flow():
    """分析市场数据流程"""
    print("\n🔍 分析市场数据显示流程")
    print("=" * 60)
    
    print("📋 实时市场数据显示流程:")
    print("1. 页面加载 → 检查MT5连接状态")
    print("2. MT5连接成功 → 启动市场数据更新 (startMarketDataUpdates)")
    print("3. 每5秒调用 updateMarketData() 函数")
    print("4. updateMarketData() → 调用 /api/mt5/market-data/{symbol}")
    print("5. API返回数据 → 更新页面显示 (currentBid, currentAsk, currentSpread)")
    print()
    
    print("🔍 可能的问题点:")
    print("• MT5连接状态检查失败")
    print("• 模型未选择或symbol为空")
    print("• 市场数据API返回错误")
    print("• 前端JavaScript错误")
    print("• 定时器未正确启动")
    print("• DOM元素未找到")

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 解决方案建议")
    print("=" * 60)
    
    print("📋 立即检查步骤:")
    print()
    
    print("1️⃣ 检查MT5连接")
    print("   • 确保MT5终端正在运行")
    print("   • 确认账户已登录")
    print("   • 检查专家顾问设置 (允许DLL导入)")
    print()
    
    print("2️⃣ 检查模型选择")
    print("   • 确认已选择交易模型")
    print("   • 确认模型的symbol字段正确")
    print("   • 重新启动自动交易")
    print()
    
    print("3️⃣ 检查浏览器控制台")
    print("   • 按F12打开开发者工具")
    print("   • 查看Console标签页")
    print("   • 寻找JavaScript错误或网络请求失败")
    print()
    
    print("4️⃣ 手动刷新")
    print("   • 刷新页面")
    print("   • 重新连接MT5")
    print("   • 重新启动自动交易")

def main():
    """主函数"""
    print("🔧 诊断AI推理交易实时市场数据显示问题")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• AI推理交易页面的'实时市场数据'长时间没有显示内容")
    print("• 买价、卖价、点差显示为 '--'")
    print("• 需要检查数据获取和显示链路")
    print()
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 检查MT5连接
    mt5_ok = check_mt5_connection(session)
    
    # 3. 检查模型选择
    model_ok, symbol = check_model_selection(session)
    
    # 4. 测试市场数据API
    if mt5_ok and model_ok and symbol:
        api_ok, market_data = test_market_data_api(session, symbol)
    else:
        api_ok = False
        market_data = None
    
    # 5. 分析问题
    analyze_market_data_flow()
    
    # 6. 提供解决方案
    provide_solutions()
    
    # 7. 总结
    print(f"\n📊 诊断结果总结")
    print("=" * 80)
    
    checks = [
        ("MT5连接", "✅" if mt5_ok else "❌"),
        ("模型选择", "✅" if model_ok else "❌"),
        ("市场数据API", "✅" if api_ok else "❌")
    ]
    
    for check_name, status in checks:
        print(f"   {check_name}: {status}")
    
    if mt5_ok and model_ok and api_ok:
        print("\n✅ 后端数据获取正常")
        print("💡 问题可能在前端JavaScript:")
        print("   1. 检查浏览器控制台错误")
        print("   2. 确认定时器正常运行")
        print("   3. 检查DOM元素是否存在")
        print("   4. 刷新页面重新初始化")
    else:
        print("\n❌ 发现后端问题，需要先解决:")
        if not mt5_ok:
            print("   • MT5连接问题")
        if not model_ok:
            print("   • 模型选择问题")
        if not api_ok:
            print("   • 市场数据API问题")
    
    return 0

if __name__ == "__main__":
    main()
