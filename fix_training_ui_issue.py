#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复训练界面问题
解决"开始模型训练"按钮不显示的问题
"""

def analyze_ui_issue():
    """分析UI问题的根本原因"""
    
    issues_found = {
        'data_ready_button_missing': {
            'description': 'data_ready状态的任务没有显示"开始模型训练"按钮',
            'root_cause': '前端JavaScript状态处理逻辑问题',
            'impact': '用户无法手动启动模型训练',
            'severity': 'high'
        },
        'training_stuck_at_25': {
            'description': '训练任务卡在25%不动',
            'root_cause': '数据准备完成后没有自动启动模型训练',
            'impact': '训练流程中断，用户体验差',
            'severity': 'medium'
        },
        'workflow_confusion': {
            'description': '用户不清楚训练流程的两个阶段',
            'root_cause': '界面缺少清晰的流程说明',
            'impact': '用户困惑，不知道如何操作',
            'severity': 'medium'
        }
    }
    
    return issues_found

def create_ui_fixes():
    """创建UI修复方案"""
    
    fixes = {
        'frontend_javascript_fix': {
            'file': 'templates/model_training.html',
            'description': '修复前端JavaScript中的状态处理逻辑',
            'changes': [
                '确保data_ready状态正确显示开始训练按钮',
                '优化状态轮询逻辑',
                '添加更清晰的状态提示'
            ]
        },
        'automatic_training_start': {
            'file': 'services/deep_learning_service.py',
            'description': '添加数据准备完成后自动启动训练的选项',
            'changes': [
                '在数据准备完成后自动调用模型训练',
                '添加配置选项控制是否自动启动',
                '改善训练流程的连续性'
            ]
        },
        'workflow_guidance': {
            'file': 'templates/model_training.html',
            'description': '添加训练流程指导',
            'changes': [
                '显示当前训练阶段',
                '添加流程进度条',
                '提供操作提示'
            ]
        }
    }
    
    return fixes

def implement_frontend_fix():
    """实施前端修复"""
    
    print("🔧 前端JavaScript修复方案:")
    print("=" * 50)
    
    js_fix = '''
    // 修复状态处理逻辑
    function updateTaskStatus(task) {
        const statusElement = document.getElementById(`status-${task.id}`);
        const actionElement = document.getElementById(`action-${task.id}`);
        
        if (task.status === 'data_ready' && task.progress >= 100) {
            // 显示开始模型训练按钮
            actionElement.innerHTML = `
                <button class="btn btn-success btn-sm" onclick="startModelTraining('${task.id}')">
                    <i class="fas fa-play"></i> 开始模型训练
                </button>
            `;
            statusElement.innerHTML = `
                <span class="badge bg-info">
                    <i class="fas fa-check-circle"></i> 数据准备完成
                </span>
            `;
        } else if (task.status === 'running') {
            // 显示训练中状态
            actionElement.innerHTML = `
                <button class="btn btn-danger btn-sm" onclick="stopTraining('${task.id}')">
                    <i class="fas fa-stop"></i> 停止训练
                </button>
            `;
            statusElement.innerHTML = `
                <span class="badge bg-primary">
                    <i class="fas fa-spinner fa-spin"></i> 训练中 (${task.progress}%)
                </span>
            `;
        }
    }
    
    // 启动模型训练函数
    function startModelTraining(taskId) {
        fetch(`/api/deep-learning/start-model-training/${taskId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', '模型训练启动成功！');
                loadTrainingTasks(); // 刷新任务列表
            } else {
                showAlert('error', '启动失败: ' + data.error);
            }
        })
        .catch(error => {
            showAlert('error', '启动失败: ' + error.message);
        });
    }
    '''
    
    print("JavaScript修复代码:")
    print(js_fix)
    
    return js_fix

def implement_backend_fix():
    """实施后端修复"""
    
    print("\n🔧 后端自动启动修复方案:")
    print("=" * 50)
    
    backend_fix = '''
    def _complete_data_preparation(self, task_id: str, config: Dict[str, Any]):
        """完成数据准备，可选择自动启动模型训练"""
        try:
            # 更新任务状态为data_ready
            self._update_task_status(task_id, 'data_ready', 100.0)
            
            # 检查是否启用自动启动
            auto_start_training = config.get('auto_start_training', True)
            
            if auto_start_training:
                logger.info(f"🚀 数据准备完成，自动启动模型训练: {task_id}")
                # 延迟1秒后启动模型训练，确保状态更新完成
                import threading
                timer = threading.Timer(1.0, self.start_model_training, args=[task_id])
                timer.start()
            else:
                logger.info(f"⏳ 数据准备完成，等待手动启动模型训练: {task_id}")
                
        except Exception as e:
            logger.error(f"❌ 完成数据准备失败: {e}")
    '''
    
    print("后端修复代码:")
    print(backend_fix)
    
    return backend_fix

def create_workflow_guidance():
    """创建工作流程指导"""
    
    print("\n🔧 工作流程指导方案:")
    print("=" * 50)
    
    workflow_html = '''
    <!-- 训练流程指导 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-route"></i> 训练流程指导
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="step-icon bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <i class="fas fa-cog"></i>
                        </div>
                        <h6 class="mt-2">1. 配置参数</h6>
                        <p class="text-muted small">设置模型参数和特征选项</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="step-icon bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <i class="fas fa-database"></i>
                        </div>
                        <h6 class="mt-2">2. 数据准备</h6>
                        <p class="text-muted small">处理历史数据和计算特征</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="step-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h6 class="mt-2">3. 模型训练</h6>
                        <p class="text-muted small">训练神经网络模型</p>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle"></i>
                <strong>提示：</strong>
                数据准备完成后，系统会自动启动模型训练。如果没有自动启动，请点击"开始模型训练"按钮。
            </div>
        </div>
    </div>
    '''
    
    print("工作流程指导HTML:")
    print(workflow_html)
    
    return workflow_html

def provide_immediate_solutions():
    """提供立即解决方案"""
    
    print("\n🚀 立即解决方案:")
    print("=" * 50)
    
    solutions = [
        {
            'problem': 'data_ready任务没有显示开始训练按钮',
            'solution': '手动启动训练',
            'command': 'python start_pending_training.py',
            'status': '✅ 已执行 - 10个任务已启动'
        },
        {
            'problem': '训练卡在25%不动',
            'solution': '检查训练进程和GPU使用',
            'command': 'python diagnose_training_stuck.py',
            'status': '✅ 已诊断 - 部分任务正常运行'
        },
        {
            'problem': '用户不知道如何操作',
            'solution': '添加操作指导和状态说明',
            'command': '更新前端界面',
            'status': '📋 待实施'
        }
    ]
    
    for solution in solutions:
        print(f"🔹 问题: {solution['problem']}")
        print(f"   解决方案: {solution['solution']}")
        print(f"   命令: {solution['command']}")
        print(f"   状态: {solution['status']}")
        print()

def main():
    """主函数"""
    print('🔧 训练界面问题修复方案')
    print('=' * 80)
    
    # 分析问题
    issues = analyze_ui_issue()
    print("🔍 发现的问题:")
    for issue_id, issue in issues.items():
        print(f"   {issue['severity'].upper()}: {issue['description']}")
        print(f"      原因: {issue['root_cause']}")
        print(f"      影响: {issue['impact']}")
        print()
    
    # 创建修复方案
    fixes = create_ui_fixes()
    print("🔧 修复方案:")
    for fix_id, fix in fixes.items():
        print(f"   {fix['description']}")
        print(f"      文件: {fix['file']}")
        for change in fix['changes']:
            print(f"      - {change}")
        print()
    
    # 实施修复
    implement_frontend_fix()
    implement_backend_fix()
    create_workflow_guidance()
    
    # 提供立即解决方案
    provide_immediate_solutions()
    
    print("✅ 修复方案制定完成！")
    print("\n💡 关键改进:")
    print("   1. 修复前端状态处理逻辑")
    print("   2. 添加自动启动训练选项")
    print("   3. 改善用户界面指导")
    print("   4. 提供手动解决方案")

if __name__ == "__main__":
    main()
