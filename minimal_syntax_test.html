<!DOCTYPE html>
<html>
<head>
    <title>最小化语法测试</title>
</head>
<body>
    <h1>最小化JavaScript语法测试</h1>
    
    <select id="tradingModelSelect">
        <option value="">请选择模型...</option>
    </select>
    
    <div id="tradingModelInfo">模型信息</div>
    
    <script>
    console.log("🧪 开始最小化测试...");
    
    // 测试基本的模型加载功能
    async function loadTradingModels() {
        console.log('🔄 开始加载交易模型...');
        
        try {
            const response = await fetch('/api/deep-learning/models');
            const data = await response.json();
            
            console.log('📊 模型API响应:', data);

            const tradingModelSelect = document.getElementById('tradingModelSelect');
            const tradingModelInfo = document.getElementById('tradingModelInfo');

            if (!tradingModelSelect) {
                console.error('❌ tradingModelSelect元素未找到');
                return;
            }
            
            if (!tradingModelInfo) {
                console.error('❌ tradingModelInfo元素未找到');
                return;
            }

            if (data.success && data.models) {
                console.log(`📋 找到 ${data.models.length} 个模型`);
                
                tradingModelSelect.innerHTML = '<option value="">请选择交易模型...</option>';

                const completedModels = data.models.filter(model => model.status === 'completed');
                console.log(`✅ 其中 ${completedModels.length} 个已完成训练`);

                if (completedModels.length > 0) {
                    completedModels.forEach((model, index) => {
                        const option = document.createElement('option');
                        option.value = model.id;
                        option.textContent = `${model.name} (${model.symbol}-${model.timeframe})`;
                        tradingModelSelect.appendChild(option);
                        
                        console.log(`   ${index + 1}. ${model.name} (${model.symbol}-${model.timeframe})`);
                    });

                    tradingModelInfo.innerHTML = `找到 ${completedModels.length} 个可用的交易模型`;
                    console.log('✅ 交易模型加载成功');
                } else {
                    tradingModelInfo.innerHTML = '没有找到训练完成的模型';
                    console.log('⚠️ 没有已完成的模型');
                }
            } else {
                const errorMsg = data.error || '未知错误';
                console.error('❌ 模型API返回错误:', errorMsg);
                tradingModelInfo.innerHTML = `加载模型失败: ${errorMsg}`;
            }

        } catch (error) {
            console.error('❌ 加载交易模型异常:', error);
            const tradingModelInfo = document.getElementById('tradingModelInfo');
            if (tradingModelInfo) {
                tradingModelInfo.innerHTML = `加载模型失败: ${error.message}`;
            }
        }
    }
    
    // 页面加载后自动测试
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 页面加载完成，开始测试...');
        setTimeout(loadTradingModels, 1000);
    });
    
    console.log("✅ 最小化测试脚本加载完成");
    </script>
</body>
</html>