// 立即修复止损止盈显示问题的JavaScript代码
// 请在浏览器控制台中运行此代码

console.log('🔧 开始修复止损止盈显示问题...');

// 1. 重新定义createPositionCard函数，修复止损止盈显示
window.createPositionCard = function(position) {
    // 适配MT5数据格式
    const openTime = new Date(position.time * 1000); // MT5时间戳是秒
    const now = new Date();
    const duration = Math.floor((now - openTime) / (1000 * 60)); // 分钟

    // 格式化持仓时长
    let durationText = '';
    if (duration < 60) {
        durationText = `${duration}分钟`;
    } else if (duration < 1440) {
        const hours = Math.floor(duration / 60);
        const minutes = duration % 60;
        durationText = `${hours}小时${minutes}分钟`;
    } else {
        const days = Math.floor(duration / 1440);
        const hours = Math.floor((duration % 1440) / 60);
        durationText = `${days}天${hours}小时`;
    }

    // 格式化北京时间
    const beijingTime = openTime.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });

    // 确定方向和颜色 (MT5: type=0是买入, type=1是卖出)
    const isBuy = position.type === 0;
    const directionText = isBuy ? '买入' : '卖出';
    const directionClass = isBuy ? 'position-direction-buy' : 'position-direction-sell';
    const directionIcon = isBuy ? 'fa-arrow-up' : 'fa-arrow-down';

    // 盈亏颜色 (使用MT5的profit字段)
    const currentProfit = position.profit || 0;
    const profitClass = currentProfit >= 0 ? 'position-profit-positive' : 'position-profit-negative';
    const profitIcon = currentProfit >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

    // 修复止损止盈显示逻辑
    const slDisplay = position.sl && position.sl > 0 ? parseFloat(position.sl).toFixed(5) : '未设置';
    const tpDisplay = position.tp && position.tp > 0 ? parseFloat(position.tp).toFixed(5) : '未设置';

    console.log(`🔍 持仓 ${position.ticket} 止损止盈数据:`, {
        sl_raw: position.sl,
        tp_raw: position.tp,
        sl_display: slDisplay,
        tp_display: tpDisplay
    });

    return `
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="position-card p-3">
                <div class="position-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 fw-bold">${position.symbol}</h6>
                            <span class="badge badge-direction ${directionClass}">
                                <i class="fas ${directionIcon} me-1"></i>${directionText}
                            </span>
                        </div>
                        <div class="text-end">
                            <div class="${profitClass}">
                                <i class="fas ${profitIcon} me-1"></i>
                                $${currentProfit.toFixed(2)}
                            </div>
                            <div class="small text-muted">
                                ${position.swap ? `掉期: $${parseFloat(position.swap).toFixed(2)}` : ''}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-6">
                        <div class="position-info-item">
                            <div class="position-info-label">数量</div>
                            <div class="position-info-value">${position.volume} 手</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="position-info-item">
                            <div class="position-info-label">开仓价</div>
                            <div class="position-info-value">${parseFloat(position.price_open).toFixed(5)}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="position-info-item">
                            <div class="position-info-label">当前价</div>
                            <div class="position-info-value">${parseFloat(position.price_current).toFixed(5)}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="position-info-item">
                            <div class="position-info-label">持仓时长</div>
                            <div class="position-info-value">${durationText}</div>
                        </div>
                    </div>
                </div>

                <div class="position-stats">
                    <div class="row">
                        <div class="col-6">
                            <div class="position-info-item">
                                <div class="position-info-label">止损</div>
                                <div class="position-info-value text-danger">
                                    ${slDisplay}
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="position-info-item">
                                <div class="position-info-label">止盈</div>
                                <div class="position-info-value text-success">
                                    ${tpDisplay}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="time-badge">
                                <i class="fas fa-clock me-1"></i>${beijingTime}
                            </span>
                            <button class="btn btn-sm btn-outline-danger" onclick="closeMT5Position('${position.ticket}')">
                                <i class="fas fa-times me-1"></i>平仓 #${position.ticket}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
};

console.log('✅ createPositionCard函数已重新定义，修复了止损止盈显示逻辑');

// 2. 立即刷新持仓显示
if (typeof refreshPositions === 'function') {
    console.log('🔄 立即刷新持仓显示...');
    refreshPositions();
} else {
    console.log('⚠️ refreshPositions函数不存在，请手动刷新页面');
}

// 3. 测试止损止盈显示逻辑
function testStopLossDisplay() {
    console.log('🧪 测试止损止盈显示逻辑...');
    
    // 模拟持仓数据
    const testPositions = [
        { sl: 0, tp: 0, ticket: 'test1' },           // 未设置
        { sl: null, tp: null, ticket: 'test2' },     // 空值
        { sl: 1.2345, tp: 1.2567, ticket: 'test3' }, // 已设置
        { sl: 3327.89, tp: 3278.22, ticket: 'test4' } // 实际数据
    ];
    
    testPositions.forEach((pos, i) => {
        const slDisplay = pos.sl && pos.sl > 0 ? parseFloat(pos.sl).toFixed(5) : '未设置';
        const tpDisplay = pos.tp && pos.tp > 0 ? parseFloat(pos.tp).toFixed(5) : '未设置';
        console.log(`测试 ${i+1} (${pos.ticket}): 止损=${slDisplay}, 止盈=${tpDisplay}`);
    });
}

// 运行测试
testStopLossDisplay();

console.log('🎉 止损止盈显示问题修复完成！');
console.log('💡 如果持仓卡片仍显示"未设置"，请检查:');
console.log('1. MT5中的止损止盈是否真的设置了');
console.log('2. 刷新页面让修复生效');
console.log('3. 检查浏览器控制台的调试信息');
