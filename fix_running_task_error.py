#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复"任务正在运行中"错误
清理卡住的训练任务状态
"""

import sqlite3
import json
import requests
from datetime import datetime, timed<PERSON>ta

def check_running_tasks():
    """检查所有running状态的任务"""
    print('🔍 检查running状态的任务')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询所有running状态的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("✅ 没有找到running状态的任务")
            conn.close()
            return []
        
        print(f"📊 找到 {len(tasks)} 个running状态的任务:")
        
        stuck_tasks = []
        for i, task in enumerate(tasks, 1):
            task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs = task
            
            print(f"\n任务 {i}:")
            print(f"   ID: {task_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            
            # 检查是否卡住
            if updated_at:
                try:
                    last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    now = datetime.now()
                    stuck_time = now - last_update.replace(tzinfo=None)
                    print(f"   距离上次更新: {stuck_time}")
                    
                    if stuck_time.total_seconds() > 300:  # 5分钟
                        print(f"   ⚠️ 任务卡住 (超过5分钟无更新)")
                        stuck_tasks.append(task_id)
                    else:
                        print(f"   ✅ 任务可能正常运行")
                except Exception as e:
                    print(f"   ❌ 时间解析失败: {e}")
                    stuck_tasks.append(task_id)  # 无法解析时间也认为是卡住
        
        conn.close()
        return stuck_tasks
        
    except Exception as e:
        print(f"❌ 检查任务失败: {e}")
        return []

def check_data_ready_tasks():
    """检查data_ready状态的任务"""
    print('\n🔍 检查data_ready状态的任务')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, model_id, status, progress, created_at, updated_at
            FROM training_tasks 
            WHERE status = 'data_ready'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("✅ 没有找到data_ready状态的任务")
            return []
        
        print(f"📊 找到 {len(tasks)} 个data_ready状态的任务:")
        
        for i, task in enumerate(tasks, 1):
            task_id, model_id, status, progress, created_at, updated_at = task
            print(f"   任务 {i}: {task_id} (进度: {progress}%)")
        
        return [task[0] for task in tasks]  # 返回任务ID列表
        
    except Exception as e:
        print(f"❌ 检查data_ready任务失败: {e}")
        return []

def clean_stuck_tasks(stuck_task_ids):
    """清理卡住的任务"""
    print(f'\n🧹 清理卡住的任务')
    print('=' * 50)
    
    if not stuck_task_ids:
        print("✅ 没有需要清理的卡住任务")
        return True
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        for task_id in stuck_task_ids:
            print(f"🛑 清理任务: {task_id}")
            
            # 更新任务状态为failed
            cursor.execute('''
                UPDATE training_tasks 
                SET status = 'failed',
                    completed_at = ?,
                    updated_at = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), datetime.now().isoformat(), task_id))
            
            print(f"   ✅ 任务状态已更新为failed")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n✅ 成功清理 {len(stuck_task_ids)} 个卡住的任务")
        return True
        
    except Exception as e:
        print(f"❌ 清理任务失败: {e}")
        return False

def test_start_training():
    """测试是否可以开始新的训练"""
    print(f'\n🧪 测试开始新训练')
    print('=' * 50)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 创建测试训练任务
    test_config = {
        'model_name': f'错误修复测试_{int(datetime.now().timestamp())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'data_config': {'mode': 'days', 'training_days': 30},
        'sequence_length': 20,
        'hidden_size': 32,  # 使用优化后的参数
        'num_layers': 1,
        'dropout': 0.3,
        'batch_size': 8,
        'learning_rate': 0.0001,
        'epochs': 10,  # 短期测试
        'patience': 3,
        'early_stopping': True,
        'min_epochs': 2,
        'use_gpu': False,  # 使用CPU避免GPU问题
        'save_checkpoints': True,
        'use_enhanced_features': True,
        'feature_selection_strategy': 'recommended',
        'include_basic_features': True,
        'analyze_feature_importance': False,
        'auto_start_training': False
    }
    
    try:
        print("📊 尝试创建测试训练任务...")
        response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                               json=test_config,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 测试任务创建成功: {task_id}")
                print(f"💡 这说明'任务正在运行中'错误已解决")
                return True
            else:
                error_msg = result.get('error', '未知错误')
                print(f"❌ 任务创建失败: {error_msg}")
                
                if 'running' in error_msg.lower():
                    print(f"⚠️ 仍然存在'任务正在运行中'错误")
                    return False
                else:
                    print(f"💡 '任务正在运行中'错误已解决，但有其他问题")
                    return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def check_training_control_state():
    """检查训练控制状态"""
    print(f'\n🔍 检查训练控制状态')
    print('=' * 50)
    
    try:
        # 检查是否有训练控制文件或状态
        import os
        
        # 检查可能的状态文件
        state_files = [
            'training_state.json',
            'training_control.json',
            'current_training.json'
        ]
        
        found_files = []
        for file in state_files:
            if os.path.exists(file):
                found_files.append(file)
                print(f"📄 找到状态文件: {file}")
                
                try:
                    with open(file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print(f"   内容: {content[:100]}...")
                except Exception as e:
                    print(f"   读取失败: {e}")
        
        if not found_files:
            print("✅ 没有找到训练状态文件")
        
        return found_files
        
    except Exception as e:
        print(f"❌ 检查训练控制状态失败: {e}")
        return []

def provide_manual_solutions():
    """提供手动解决方案"""
    print(f'\n🔧 手动解决方案')
    print('=' * 50)
    
    print("如果问题仍然存在，可以尝试以下方案:")
    
    print("\n1. 🔄 重启Flask应用:")
    print("   - 在终端按 Ctrl+C 停止应用")
    print("   - 重新运行: python app.py")
    
    print("\n2. 🧹 清理浏览器状态:")
    print("   - 刷新浏览器页面 (F5)")
    print("   - 清除浏览器缓存")
    print("   - 重新登录系统")
    
    print("\n3. 📊 检查数据库:")
    print("   - 确认所有running任务已清理")
    print("   - 检查是否有重复的任务记录")
    
    print("\n4. 🔧 强制重置:")
    print("   - 删除所有training_tasks记录")
    print("   - 重新开始训练")

def main():
    """主函数"""
    print('🔧 修复"任务正在运行中"错误')
    print('=' * 80)
    
    # 1. 检查running状态的任务
    stuck_tasks = check_running_tasks()
    
    # 2. 检查data_ready状态的任务
    data_ready_tasks = check_data_ready_tasks()
    
    # 3. 清理卡住的任务
    if stuck_tasks:
        print(f"\n🛑 发现 {len(stuck_tasks)} 个卡住的任务，开始清理...")
        clean_success = clean_stuck_tasks(stuck_tasks)
    else:
        print(f"\n✅ 没有发现卡住的任务")
        clean_success = True
    
    # 4. 检查训练控制状态
    state_files = check_training_control_state()
    
    # 5. 测试是否可以开始新训练
    if clean_success:
        test_success = test_start_training()
    else:
        test_success = False
    
    # 6. 总结结果
    print(f"\n🎯 修复结果总结:")
    print(f"   卡住任务清理: {'✅ 成功' if clean_success else '❌ 失败'}")
    print(f"   新训练测试: {'✅ 成功' if test_success else '❌ 失败'}")
    
    if test_success:
        print(f"\n🎉 '任务正在运行中'错误已修复！")
        print(f"💡 现在可以正常开始新的模型训练")
    else:
        print(f"\n❌ 错误仍然存在")
        provide_manual_solutions()

if __name__ == "__main__":
    main()
