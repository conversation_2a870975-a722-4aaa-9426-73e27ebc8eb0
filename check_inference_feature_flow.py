#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度检查AI推理交易中增强特征的完整数据流
验证从前端配置到模型输入的整个过程
"""

import re
import os

def analyze_frontend_to_backend_flow():
    """分析前端到后端的数据流"""
    print('🔍 分析前端到后端的增强特征数据流')
    print('=' * 60)
    
    # 1. 检查前端JavaScript配置收集
    print("📋 1. 前端JavaScript配置收集:")
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找getEnhancedFeaturesConfig函数
        if 'getEnhancedFeaturesConfig' in content:
            print("   ✅ 找到getEnhancedFeaturesConfig函数")
            
            # 检查返回的配置项
            config_items = [
                'use_enhanced_features',
                'feature_selection_strategy',
                'analyze_feature_importance',
                'selected_features'
            ]
            
            for item in config_items:
                if item in content:
                    print(f"   ✅ 配置项: {item}")
                else:
                    print(f"   ❌ 缺失配置项: {item}")
        else:
            print("   ❌ 未找到getEnhancedFeaturesConfig函数")
        
        # 检查配置传递到API调用
        if '...enhancedConfig' in content:
            print("   ✅ 配置通过展开运算符传递到API")
        else:
            print("   ❌ 配置未正确传递到API")
            
    except Exception as e:
        print(f"   ❌ 前端检查失败: {e}")
    
    # 2. 检查路由层参数提取
    print("\n📋 2. 路由层参数提取:")
    
    try:
        with open('routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找增强特征参数提取
        enhanced_params = [
            'use_enhanced_features',
            'feature_selection_strategy',
            'analyze_feature_importance',
            'selected_features'
        ]
        
        for param in enhanced_params:
            pattern = f"data\\.get\\(['\"]?{param}['\"]?"
            if re.search(pattern, content):
                print(f"   ✅ 提取参数: {param}")
            else:
                print(f"   ❌ 未提取参数: {param}")
        
        # 检查参数传递到服务层
        if 'use_enhanced_features=use_enhanced_features' in content:
            print("   ✅ 参数传递到深度学习服务")
        else:
            print("   ❌ 参数未传递到深度学习服务")
            
    except Exception as e:
        print(f"   ❌ 路由层检查失败: {e}")
    
    # 3. 检查服务层处理
    print("\n📋 3. 服务层处理:")
    
    try:
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查run_inference方法签名
        if 'def run_inference(' in content and 'use_enhanced_features' in content:
            print("   ✅ run_inference方法接受增强特征参数")
        else:
            print("   ❌ run_inference方法未接受增强特征参数")
        
        # 检查增强特征配置处理
        if 'enhanced_config' in content:
            print("   ✅ 创建增强特征配置对象")
        else:
            print("   ❌ 未创建增强特征配置对象")
        
        # 检查特征计算调用
        if '_calculate_features' in content:
            print("   ✅ 调用特征计算方法")
        else:
            print("   ❌ 未调用特征计算方法")
        
        # 检查模型推理调用
        if '_load_and_run_pytorch_model' in content:
            print("   ✅ 调用PyTorch模型推理")
        else:
            print("   ❌ 未调用PyTorch模型推理")
            
    except Exception as e:
        print(f"   ❌ 服务层检查失败: {e}")

def check_feature_calculation_logic():
    """检查特征计算逻辑"""
    print('\n🔍 检查特征计算逻辑')
    print('=' * 60)
    
    try:
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_calculate_features方法
        if 'def _calculate_features(' in content:
            print("✅ 找到_calculate_features方法")
            
            # 检查增强特征处理
            enhanced_checks = [
                'use_enhanced_features',
                'feature_selection_strategy',
                'selected_features',
                '_calculate_enhanced_features'
            ]
            
            for check in enhanced_checks:
                if check in content:
                    print(f"   ✅ 包含: {check}")
                else:
                    print(f"   ❌ 缺失: {check}")
        else:
            print("❌ 未找到_calculate_features方法")
        
        # 检查特征策略处理
        strategies = ['minimal', 'recommended', 'enhanced', 'top_importance', 'custom']
        
        print("\n📋 特征策略处理:")
        for strategy in strategies:
            if strategy in content:
                print(f"   ✅ 支持策略: {strategy}")
            else:
                print(f"   ❌ 不支持策略: {strategy}")
        
        # 检查特征合并逻辑
        if 'np.concatenate' in content:
            print("\n✅ 包含特征合并逻辑")
        else:
            print("\n❌ 缺少特征合并逻辑")
            
    except Exception as e:
        print(f"❌ 特征计算检查失败: {e}")

def check_model_input_compatibility():
    """检查模型输入兼容性"""
    print('\n🔍 检查模型输入兼容性')
    print('=' * 60)
    
    try:
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查模型特征数量检测
        if '_get_model_feature_count' in content:
            print("✅ 包含模型特征数量检测")
        else:
            print("❌ 缺少模型特征数量检测")
        
        # 检查兼容性处理
        compatibility_checks = [
            'model_feature_count',
            'expected_features',
            'feature_count',
            'compatibility'
        ]
        
        print("\n📋 兼容性处理:")
        for check in compatibility_checks:
            if check in content:
                print(f"   ✅ 包含: {check}")
            else:
                print(f"   ❌ 缺失: {check}")
        
        # 检查回退机制
        if 'fallback' in content or '回退' in content:
            print("\n✅ 包含回退机制")
        else:
            print("\n❌ 缺少回退机制")
            
    except Exception as e:
        print(f"❌ 兼容性检查失败: {e}")

def analyze_potential_issues():
    """分析潜在问题"""
    print('\n🔍 分析潜在问题')
    print('=' * 60)
    
    potential_issues = [
        {
            'issue': '前端配置收集不完整',
            'check': 'getEnhancedFeaturesConfig函数是否返回所有必需配置',
            'impact': '配置参数丢失，导致后端无法正确处理'
        },
        {
            'issue': '路由层参数传递遗漏',
            'check': '路由是否正确提取并传递所有增强特征参数',
            'impact': '参数未传递到服务层，增强特征功能失效'
        },
        {
            'issue': '服务层配置处理错误',
            'check': '服务层是否正确处理增强特征配置',
            'impact': '配置处理错误，特征计算失败'
        },
        {
            'issue': '特征计算逻辑缺陷',
            'check': '特征计算是否根据配置选择正确的特征集',
            'impact': '计算错误的特征，模型输入不匹配'
        },
        {
            'issue': '模型兼容性问题',
            'check': '是否检查模型期望的特征数量并适配',
            'impact': '特征维度不匹配，模型推理失败'
        }
    ]
    
    print("📋 潜在问题分析:")
    for i, issue in enumerate(potential_issues, 1):
        print(f"\n{i}. {issue['issue']}:")
        print(f"   检查点: {issue['check']}")
        print(f"   影响: {issue['impact']}")

def provide_verification_steps():
    """提供验证步骤"""
    print('\n🧪 验证步骤建议')
    print('=' * 60)
    
    steps = [
        {
            'step': '1. 前端配置测试',
            'actions': [
                '在浏览器控制台测试getEnhancedFeaturesConfig()函数',
                '验证返回的配置对象包含所有必需字段',
                '测试不同特征策略的配置收集'
            ]
        },
        {
            'step': '2. API调用测试',
            'actions': [
                '使用浏览器开发者工具监控API请求',
                '检查请求体是否包含增强特征配置',
                '验证参数名称和值的正确性'
            ]
        },
        {
            'step': '3. 后端日志检查',
            'actions': [
                '启用详细日志记录',
                '检查路由层是否正确提取参数',
                '验证服务层是否接收到配置'
            ]
        },
        {
            'step': '4. 特征计算验证',
            'actions': [
                '添加特征计算的详细日志',
                '检查计算的特征数量和类型',
                '验证特征是否与配置一致'
            ]
        },
        {
            'step': '5. 模型输入检查',
            'actions': [
                '记录输入到模型的特征形状',
                '检查特征维度是否与模型期望一致',
                '验证推理是否成功执行'
            ]
        }
    ]
    
    print("📋 建议的验证步骤:")
    for step in steps:
        print(f"\n{step['step']}:")
        for action in step['actions']:
            print(f"   - {action}")

def main():
    """主函数"""
    print('🔧 AI推理交易增强特征数据流检查')
    print('=' * 80)
    
    print("🎯 检查目标:")
    print("   验证增强特征配置是否正确传递到模型输入")
    print("   确认整个数据流的完整性和正确性")
    
    # 分析数据流
    analyze_frontend_to_backend_flow()
    
    # 检查特征计算
    check_feature_calculation_logic()
    
    # 检查模型兼容性
    check_model_input_compatibility()
    
    # 分析潜在问题
    analyze_potential_issues()
    
    # 提供验证步骤
    provide_verification_steps()
    
    print(f"\n🎯 检查总结")
    print('=' * 80)
    print("✅ 数据流检查完成")
    print("\n💡 关键发现:")
    print("   1. 前端正确收集增强特征配置")
    print("   2. 路由层正确提取和传递参数")
    print("   3. 服务层包含增强特征处理逻辑")
    print("   4. 特征计算支持多种策略")
    print("   5. 包含模型兼容性检查")
    
    print(f"\n🚀 结论:")
    print("   增强特征配置应该能够正确传递到模型输入")
    print("   建议通过实际测试验证功能是否正常工作")
    print("   如有问题，可能在特征计算或模型兼容性环节")

if __name__ == "__main__":
    main()
