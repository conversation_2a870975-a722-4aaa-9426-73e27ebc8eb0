#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制清理卡住的训练任务
"""

import sqlite3
import psutil
import time

def force_cleanup():
    """强制清理卡住的训练任务"""
    print('🔧 强制清理卡住的训练任务')
    print('=' * 50)
    
    # 1. 更新数据库中的任务状态
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE training_tasks 
            SET status = 'failed', 
                updated_at = datetime('now'),
                logs = '{"stage": "cleanup", "message": "训练任务被手动停止"}'
            WHERE id = 'd9997a16-c228-49c0-a508-8e6b662e9afd'
        """)
        
        conn.commit()
        conn.close()
        print('✅ 任务状态已更新为失败')
        
    except Exception as e:
        print(f'❌ 更新任务状态失败: {e}')
    
    # 2. 清理GPU内存
    try:
        import torch
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print('✅ GPU内存已清理')
        else:
            print('ℹ️ CUDA不可用')
    except ImportError:
        print('ℹ️ PyTorch未安装')
    except Exception as e:
        print(f'❌ 清理GPU失败: {e}')
    
    # 3. 终止相关Python进程
    try:
        killed = 0
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'train' in cmdline.lower() or 'deep_learning' in cmdline.lower():
                        print(f'🔹 终止进程 PID: {proc.info["pid"]}')
                        proc.kill()
                        killed += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if killed > 0:
            print(f'✅ 已终止 {killed} 个训练进程')
            time.sleep(2)  # 等待进程完全终止
        else:
            print('ℹ️ 没有找到训练进程')
            
    except Exception as e:
        print(f'❌ 清理进程失败: {e}')
    
    print('\n🎯 清理完成')
    
    # 4. 验证清理结果
    print('\n🔍 验证清理结果:')
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, status, updated_at 
            FROM training_tasks 
            WHERE id = 'd9997a16-c228-49c0-a508-8e6b662e9afd'
        """)
        
        result = cursor.fetchone()
        if result:
            task_id, status, updated_at = result
            print(f'   任务状态: {status}')
            print(f'   更新时间: {updated_at}')
        else:
            print('   ❌ 任务未找到')
        
        conn.close()
        
    except Exception as e:
        print(f'   ❌ 验证失败: {e}')

if __name__ == "__main__":
    force_cleanup()
