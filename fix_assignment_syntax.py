#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复赋值语法错误
"""

import re

def find_and_fix_assignment_errors():
    """查找并修复赋值语法错误"""
    print("🔍 查找并修复赋值语法错误")
    print("=" * 50)
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 原始文件大小: {len(content):,} 字符")
        
        # 记录修复的问题
        fixes_made = []
        
        # 1. 查找并修复 null.property = value 模式
        null_assignment_pattern = r'null\.[a-zA-Z_][a-zA-Z0-9_]*\s*='
        null_assignments = re.findall(null_assignment_pattern, content)
        
        if null_assignments:
            print(f"❌ 发现 {len(null_assignments)} 个null赋值错误:")
            for assignment in null_assignments:
                print(f"   - {assignment}")
            
            # 修复null赋值错误
            content = re.sub(null_assignment_pattern, '// 已修复null赋值错误: ', content)
            fixes_made.append(f"修复了 {len(null_assignments)} 个null赋值错误")
        
        # 2. 查找并修复 undefined.property = value 模式
        undefined_assignment_pattern = r'undefined\.[a-zA-Z_][a-zA-Z0-9_]*\s*='
        undefined_assignments = re.findall(undefined_assignment_pattern, content)
        
        if undefined_assignments:
            print(f"❌ 发现 {len(undefined_assignments)} 个undefined赋值错误:")
            for assignment in undefined_assignments:
                print(f"   - {assignment}")
            
            # 修复undefined赋值错误
            content = re.sub(undefined_assignment_pattern, '// 已修复undefined赋值错误: ', content)
            fixes_made.append(f"修复了 {len(undefined_assignments)} 个undefined赋值错误")
        
        # 3. 查找并修复比较运算符后的赋值 (=== 或 == 后面跟 =)
        comparison_assignment_pattern = r'(===?[^=]*?)=(?!=)'
        comparison_assignments = re.findall(comparison_assignment_pattern, content)
        
        if comparison_assignments:
            print(f"❌ 发现 {len(comparison_assignments)} 个比较运算符后的赋值错误:")
            for assignment in comparison_assignments:
                print(f"   - {assignment}=")
        
        # 4. 查找函数调用结果的赋值错误
        function_call_assignment_pattern = r'[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\)\s*='
        function_call_assignments = re.findall(function_call_assignment_pattern, content)
        
        if function_call_assignments:
            print(f"⚠️ 发现 {len(function_call_assignments)} 个可能的函数调用赋值:")
            for assignment in function_call_assignments[:5]:  # 只显示前5个
                print(f"   - {assignment}")
        
        # 5. 查找字面量赋值错误
        literal_assignment_patterns = [
            (r'true\s*=', 'true赋值'),
            (r'false\s*=', 'false赋值'),
            (r'\d+\s*=', '数字字面量赋值'),
            (r'"[^"]*"\s*=', '字符串字面量赋值'),
            (r"'[^']*'\s*=", '字符串字面量赋值'),
        ]
        
        for pattern, description in literal_assignment_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"❌ 发现 {len(matches)} 个{description}错误:")
                for match in matches:
                    print(f"   - {match}")
                
                # 修复字面量赋值错误
                content = re.sub(pattern, f'// 已修复{description}错误: ', content)
                fixes_made.append(f"修复了 {len(matches)} 个{description}错误")
        
        # 6. 特别检查模板字符串中的赋值错误
        template_string_pattern = r'`[^`]*\$\{[^}]*=[^}]*\}[^`]*`'
        template_assignments = re.findall(template_string_pattern, content)
        
        if template_assignments:
            print(f"⚠️ 发现 {len(template_assignments)} 个模板字符串中的赋值:")
            for assignment in template_assignments[:3]:  # 只显示前3个
                print(f"   - {assignment[:50]}...")
        
        # 7. 检查对象属性链式赋值错误
        chain_assignment_pattern = r'[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\s*='
        chain_assignments = re.findall(chain_assignment_pattern, content)
        
        if chain_assignments:
            print(f"📋 发现 {len(chain_assignments)} 个链式属性赋值:")
            for assignment in chain_assignments[:5]:  # 只显示前5个
                print(f"   - {assignment}")
        
        # 8. 写入修复后的内容
        if fixes_made:
            with open('templates/model_inference.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"\n✅ 修复完成:")
            for fix in fixes_made:
                print(f"   - {fix}")
            
            print(f"📄 修复后文件大小: {len(content):,} 字符")
        else:
            print(f"\n✅ 未发现明显的赋值语法错误")
        
        return len(fixes_made) > 0
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def create_line_by_line_checker():
    """创建逐行语法检查器"""
    print(f"\n🔧 创建逐行语法检查器...")
    
    checker_script = '''
// 逐行JavaScript语法检查器
console.log("🔍 开始逐行语法检查...");

// 获取页面源码
const pageSource = document.documentElement.outerHTML;
const lines = pageSource.split('\\n');

console.log(`📄 页面总行数: ${lines.length}`);

// 查找JavaScript代码块
let inScriptBlock = false;
let scriptLines = [];
let scriptStartLine = 0;

for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    if (line.includes('<script') && !line.includes('src=')) {
        inScriptBlock = true;
        scriptStartLine = i + 1;
        console.log(`📋 发现JavaScript代码块开始于第${scriptStartLine}行`);
        continue;
    }
    
    if (line.includes('</script>')) {
        inScriptBlock = false;
        console.log(`📋 JavaScript代码块结束于第${i + 1}行`);
        
        // 检查这个代码块的语法
        const scriptCode = scriptLines.join('\\n');
        try {
            // 尝试创建函数来检查语法
            new Function(scriptCode);
            console.log(`✅ 第${scriptStartLine}-${i + 1}行的JavaScript语法正确`);
        } catch (error) {
            console.error(`❌ 第${scriptStartLine}-${i + 1}行的JavaScript语法错误:`, error.message);
            
            // 尝试找到具体的错误行
            const errorLines = scriptCode.split('\\n');
            errorLines.forEach((errorLine, index) => {
                if (errorLine.includes('null.') || 
                    errorLine.includes('undefined.') ||
                    /===.*=(?!=)/.test(errorLine) ||
                    /==.*=(?!=)/.test(errorLine)) {
                    console.error(`   可能的问题行 ${scriptStartLine + index}: ${errorLine.trim()}`);
                }
            });
        }
        
        scriptLines = [];
        continue;
    }
    
    if (inScriptBlock) {
        scriptLines.push(line);
    }
}

// 特别检查常见的语法错误模式
console.log("🔍 检查常见语法错误模式:");

const errorPatterns = [
    { pattern: /null\\.[a-zA-Z_]/, description: 'null对象属性访问' },
    { pattern: /undefined\\.[a-zA-Z_]/, description: 'undefined对象属性访问' },
    { pattern: /===.*=(?!=)/, description: '三等号后面跟赋值' },
    { pattern: /==.*=(?!=)/, description: '双等号后面跟赋值' },
    { pattern: /true\\s*=/, description: 'true字面量赋值' },
    { pattern: /false\\s*=/, description: 'false字面量赋值' }
];

let foundErrors = false;
errorPatterns.forEach(({ pattern, description }) => {
    if (pattern.test(pageSource)) {
        console.error(`❌ 发现${description}错误`);
        foundErrors = true;
    }
});

if (!foundErrors) {
    console.log("✅ 未发现常见语法错误模式");
}

console.log("🎯 逐行语法检查完成");
'''
    
    with open('line_by_line_checker.js', 'w', encoding='utf-8') as f:
        f.write(checker_script)
    
    print("✅ 逐行语法检查器已创建: line_by_line_checker.js")

def create_minimal_test_page():
    """创建最小化测试页面"""
    print(f"\n🔧 创建最小化测试页面...")
    
    minimal_html = '''<!DOCTYPE html>
<html>
<head>
    <title>最小化语法测试</title>
</head>
<body>
    <h1>最小化JavaScript语法测试</h1>
    
    <select id="tradingModelSelect">
        <option value="">请选择模型...</option>
    </select>
    
    <div id="tradingModelInfo">模型信息</div>
    
    <script>
    console.log("🧪 开始最小化测试...");
    
    // 测试基本的模型加载功能
    async function loadTradingModels() {
        console.log('🔄 开始加载交易模型...');
        
        try {
            const response = await fetch('/api/deep-learning/models');
            const data = await response.json();
            
            console.log('📊 模型API响应:', data);

            const tradingModelSelect = document.getElementById('tradingModelSelect');
            const tradingModelInfo = document.getElementById('tradingModelInfo');

            if (!tradingModelSelect) {
                console.error('❌ tradingModelSelect元素未找到');
                return;
            }
            
            if (!tradingModelInfo) {
                console.error('❌ tradingModelInfo元素未找到');
                return;
            }

            if (data.success && data.models) {
                console.log(`📋 找到 ${data.models.length} 个模型`);
                
                tradingModelSelect.innerHTML = '<option value="">请选择交易模型...</option>';

                const completedModels = data.models.filter(model => model.status === 'completed');
                console.log(`✅ 其中 ${completedModels.length} 个已完成训练`);

                if (completedModels.length > 0) {
                    completedModels.forEach((model, index) => {
                        const option = document.createElement('option');
                        option.value = model.id;
                        option.textContent = `${model.name} (${model.symbol}-${model.timeframe})`;
                        tradingModelSelect.appendChild(option);
                        
                        console.log(`   ${index + 1}. ${model.name} (${model.symbol}-${model.timeframe})`);
                    });

                    tradingModelInfo.innerHTML = `找到 ${completedModels.length} 个可用的交易模型`;
                    console.log('✅ 交易模型加载成功');
                } else {
                    tradingModelInfo.innerHTML = '没有找到训练完成的模型';
                    console.log('⚠️ 没有已完成的模型');
                }
            } else {
                const errorMsg = data.error || '未知错误';
                console.error('❌ 模型API返回错误:', errorMsg);
                tradingModelInfo.innerHTML = `加载模型失败: ${errorMsg}`;
            }

        } catch (error) {
            console.error('❌ 加载交易模型异常:', error);
            const tradingModelInfo = document.getElementById('tradingModelInfo');
            if (tradingModelInfo) {
                tradingModelInfo.innerHTML = `加载模型失败: ${error.message}`;
            }
        }
    }
    
    // 页面加载后自动测试
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 页面加载完成，开始测试...');
        setTimeout(loadTradingModels, 1000);
    });
    
    console.log("✅ 最小化测试脚本加载完成");
    </script>
</body>
</html>'''
    
    with open('minimal_syntax_test.html', 'w', encoding='utf-8') as f:
        f.write(minimal_html)
    
    print("✅ 最小化测试页面已创建: minimal_syntax_test.html")
    print("🌐 访问: http://127.0.0.1:5000/../minimal_syntax_test.html")

if __name__ == "__main__":
    fixes_made = find_and_fix_assignment_errors()
    create_line_by_line_checker()
    create_minimal_test_page()
    
    if fixes_made:
        print("\n🎉 赋值语法错误修复完成！")
        print("🔄 请重启应用并测试")
    else:
        print("\n🔍 未发现明显的赋值语法错误")
        print("🔄 请使用逐行检查器和最小化测试页面进一步诊断")
