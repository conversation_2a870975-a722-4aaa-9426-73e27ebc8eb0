#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复running状态但数据准备已完成的任务
解决状态不一致问题
"""

import sqlite3
import json
from datetime import datetime
import os

def fix_running_task_status():
    """修复running状态的任务"""
    print("🔧 修复任务状态")
    print("=" * 60)
    
    # 检查数据库文件 - 深度学习服务使用trading_system.db
    possible_paths = [
        'trading_system.db',  # 深度学习服务的主数据库
        'instance/trading_system.db',
        'data/deep_learning.db',
        'instance/matetrade4.db',
        'matetrade4.db'
    ]

    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            print(f"✅ 找到数据库文件: {path}")
            break

    if not db_path:
        print(f"❌ 未找到数据库文件，尝试过的路径:")
        for path in possible_paths:
            print(f"   {path}")
        print("💡 请确保数据库文件存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print(f"✅ 连接数据库成功: {db_path}")

        # 首先检查数据库中有哪些表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📋 数据库中的表: {[table[0] for table in tables]}")

        # 检查是否有training_tasks表
        if 'training_tasks' not in [table[0] for table in tables]:
            print("❌ 数据库中没有training_tasks表")
            print("💡 这可能是因为还没有创建过训练任务")
            conn.close()
            return True

        # 查找running状态或stopped状态但进度较低的任务（可能是数据准备完成但状态错误）
        cursor.execute('''
            SELECT id, model_id, logs, progress, status, created_at, updated_at
            FROM training_tasks
            WHERE (status = 'running') OR (status = 'stopped' AND progress <= 30)
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("📋 没有找到需要修复的任务（running或stopped且进度<=30%）")
            return True
        
        print(f"📊 找到 {len(tasks)} 个可能需要修复的任务:")
        print()
        
        fixed_count = 0
        
        for task_id, model_id, logs, progress, status, created_at, updated_at in tasks:
            print(f"🔍 检查任务: {task_id}")
            print(f"   模型ID: {model_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            
            should_fix = False
            fix_reason = ""
            
            if logs:
                try:
                    log_data = json.loads(logs)
                    stage = log_data.get('stage', 'unknown')
                    message = log_data.get('message', '')
                    
                    print(f"   日志阶段: {stage}")
                    print(f"   日志消息: {message}")
                    
                    # 检查是否数据准备完成
                    if stage == 'data_ready':
                        should_fix = True
                        fix_reason = "日志显示数据准备完成"
                        print(f"   ✅ 发现数据准备完成，需要修复状态")
                        
                        # 显示数据信息
                        if 'train_samples' in log_data:
                            print(f"   📊 训练样本: {log_data.get('train_samples', 'N/A')}")
                            print(f"   📊 验证样本: {log_data.get('val_samples', 'N/A')}")
                            print(f"   📊 特征数: {log_data.get('feature_count', 'N/A')}")
                    
                    elif progress >= 25:
                        should_fix = True
                        fix_reason = f"进度达到{progress}%，可能数据准备完成"
                        print(f"   ⚠️ 进度较高，可能需要修复")
                    
                except json.JSONDecodeError as e:
                    print(f"   ❌ 日志解析失败: {e}")
                    if progress >= 25:
                        should_fix = True
                        fix_reason = f"日志解析失败但进度{progress}%"
            else:
                print(f"   ⚠️ 无日志信息")
                if progress >= 25:
                    should_fix = True
                    fix_reason = f"无日志但进度{progress}%"
            
            if should_fix:
                print(f"   🔧 修复原因: {fix_reason}")
                
                # 更新任务状态为data_ready
                cursor.execute('''
                    UPDATE training_tasks
                    SET status = 'data_ready',
                        progress = 100,
                        updated_at = ?
                    WHERE id = ?
                ''', (datetime.now().isoformat(), task_id))
                
                # 更新模型状态
                cursor.execute('''
                    UPDATE deep_learning_models
                    SET status = 'data_ready'
                    WHERE id = ?
                ''', (model_id,))
                
                print(f"   ✅ 任务状态已修复: running → data_ready")
                fixed_count += 1
            else:
                print(f"   ⏭️ 跳过修复（进度{progress}%，可能仍在处理中）")
            
            print()
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print("=" * 60)
        print(f"🎉 修复完成！")
        print(f"✅ 成功修复 {fixed_count} 个任务的状态")
        
        if fixed_count > 0:
            print(f"\n💡 下一步操作:")
            print(f"1. 刷新模型训练页面")
            print(f"2. 检查任务状态是否更新为 'data_ready'")
            print(f"3. 点击'开始模型训练'按钮")
        else:
            print(f"\n💡 没有需要修复的任务")
            print(f"如果页面仍显示running状态，可能需要等待数据准备完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def check_database_info():
    """检查数据库基本信息"""
    print("\n📊 数据库信息检查")
    print("=" * 40)

    # 尝试找到数据库文件 - 深度学习服务使用trading_system.db
    possible_paths = [
        'trading_system.db',  # 深度学习服务的主数据库
        'instance/trading_system.db',
        'data/deep_learning.db',
        'instance/matetrade4.db',
        'matetrade4.db'
    ]

    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        print(f"❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查训练任务表
        cursor.execute('SELECT COUNT(*) FROM training_tasks')
        task_count = cursor.fetchone()[0]
        print(f"📋 训练任务总数: {task_count}")
        
        # 检查各状态的任务数
        cursor.execute('''
            SELECT status, COUNT(*) 
            FROM training_tasks 
            GROUP BY status
        ''')
        status_counts = cursor.fetchall()
        
        print(f"📊 任务状态分布:")
        for status, count in status_counts:
            print(f"   {status}: {count}")
        
        # 检查最近的任务
        cursor.execute('''
            SELECT id, status, progress, updated_at
            FROM training_tasks
            ORDER BY updated_at DESC
            LIMIT 3
        ''')
        recent_tasks = cursor.fetchall()
        
        print(f"\n📅 最近的任务:")
        for task_id, status, progress, updated_at in recent_tasks:
            print(f"   {task_id[:8]}... | {status} | {progress}% | {updated_at}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🚀 任务状态修复工具")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• 任务显示running状态但数据准备已完成")
    print("• 进度25%通常表示数据准备完成")
    print("• 需要将状态更新为data_ready以便开始训练")
    
    # 检查数据库信息
    check_database_info()
    
    # 执行修复
    success = fix_running_task_status()
    
    if success:
        print(f"\n🎯 修复完成！请刷新页面查看效果")
    else:
        print(f"\n⚠️ 修复过程中遇到问题，请检查错误信息")
    
    return 0

if __name__ == "__main__":
    main()
