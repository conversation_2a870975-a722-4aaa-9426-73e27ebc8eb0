#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试验证死锁修复效果
验证单线程数据加载是否解决验证阶段卡住问题
"""

import requests
import time
import json

def test_validation_deadlock_fix():
    """测试验证死锁修复"""
    print('🧪 测试验证死锁修复效果')
    print('=' * 60)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 创建验证死锁修复测试任务
    print("\n📊 创建验证死锁修复测试任务...")
    
    # 使用您的内存充足优势，保持较大的batch_size
    validation_fix_config = {
        'model_name': f'验证死锁修复测试_{int(time.time())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'data_config': {'mode': 'days', 'training_days': 60},
        'sequence_length': 20,
        'hidden_size': 64,
        'num_layers': 2,
        'dropout': 0.3,
        'batch_size': 16,  # 保持较大batch_size，因为内存充足
        'learning_rate': 0.0001,
        'epochs': 20,  # 较少轮次快速测试
        'patience': 5,
        'early_stopping': True,
        'min_epochs': 3,
        'use_gpu': True,
        'save_checkpoints': True,
        'use_enhanced_features': True,
        'feature_selection_strategy': 'enhanced',
        'include_basic_features': True,
        'analyze_feature_importance': True,
        'auto_start_training': False,  # 手动启动
        'validation_split': 0.2,
        # 关键：强制单线程数据加载
        'num_workers': 0,
        'pin_memory': False,
        'drop_last': True
    }
    
    try:
        response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                               json=validation_fix_config,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 验证死锁修复测试任务创建成功: {task_id}")
                
                # 等待数据准备完成
                print("⏳ 等待数据准备完成...")
                
                max_wait_time = 180  # 3分钟等待数据准备
                start_time = time.time()
                
                while time.time() - start_time < max_wait_time:
                    progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                    
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        if progress_data.get('success'):
                            progress = progress_data.get('progress', {})
                            status = progress.get('status', 'unknown')
                            progress_percent = progress.get('progress', 0)
                            
                            current_time = time.time() - start_time
                            print(f"   [{current_time:6.1f}s] 状态: {status}, 进度: {progress_percent}%")
                            
                            if status == 'data_ready':
                                print(f"   ✅ 数据准备完成！")
                                break
                            elif status == 'failed':
                                print(f"   ❌ 数据准备失败")
                                return False
                            
                            time.sleep(5)
                        else:
                            print(f"   ❌ 获取进度失败: {progress_data.get('error')}")
                            return False
                    else:
                        print(f"   ❌ 进度请求失败: {progress_response.status_code}")
                        return False
                
                # 手动启动训练
                print(f"\n🚀 手动启动训练...")
                train_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/start-model-training/{task_id}',
                                            headers={'Content-Type': 'application/json'})
                
                if train_response.status_code == 200:
                    train_result = train_response.json()
                    if train_result.get('success'):
                        print(f"   ✅ 训练启动成功！")
                        
                        # 重点监控验证阶段
                        return monitor_validation_phases(session, task_id)
                    else:
                        print(f"   ❌ 训练启动失败: {train_result.get('error')}")
                        return False
                else:
                    print(f"   ❌ 训练启动请求失败: {train_response.status_code}")
                    return False
                
            else:
                print(f"❌ 任务创建失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_validation_phases(session, task_id):
    """监控验证阶段，检测是否有死锁"""
    print('\n🔍 监控验证阶段，检测死锁修复效果')
    print('=' * 60)
    
    max_monitor_time = 600  # 10分钟监控时间
    start_time = time.time()
    last_epoch = 0
    last_progress = 0
    validation_phases = []
    stuck_count = 0
    max_stuck_count = 6  # 连续6次无变化认为卡住
    
    print("📊 开始监控训练过程，重点关注验证阶段...")
    
    while time.time() - start_time < max_monitor_time:
        try:
            progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if progress_response.status_code == 200:
                progress_data = progress_response.json()
                if progress_data.get('success'):
                    progress = progress_data.get('progress', {})
                    status = progress.get('status', 'unknown')
                    progress_percent = progress.get('progress', 0)
                    current_epoch = progress.get('epoch', 0)
                    train_loss = progress.get('train_loss', 0)
                    val_loss = progress.get('val_loss', 0)
                    
                    current_time = time.time() - start_time
                    
                    # 详细输出
                    print(f"[{current_time:6.1f}s] 轮次: {current_epoch:2d}, 进度: {progress_percent:5.1f}%, "
                          f"状态: {status:10s}, 训练损失: {train_loss:.4f}, 验证损失: {val_loss:.4f}")
                    
                    # 检查验证损失异常
                    if val_loss == 0.0 and current_epoch > 0:
                        print(f"        ⚠️ 验证损失为0.0，可能验证阶段有问题")
                    
                    # 检查进度更新
                    if current_epoch != last_epoch or progress_percent != last_progress:
                        stuck_count = 0
                        if current_epoch > last_epoch:
                            validation_phases.append({
                                'epoch': current_epoch,
                                'time': current_time,
                                'val_loss': val_loss,
                                'status': 'completed' if val_loss > 0 else 'suspicious'
                            })
                            print(f"        ✅ 第{current_epoch}轮完成，验证损失: {val_loss:.4f}")
                    else:
                        stuck_count += 1
                        if stuck_count >= max_stuck_count:
                            print(f"        ❌ 训练卡住 (连续{stuck_count}次无变化)")
                            print(f"        📊 验证阶段统计:")
                            analyze_validation_phases(validation_phases)
                            return False
                    
                    last_epoch = current_epoch
                    last_progress = progress_percent
                    
                    # 检查训练完成
                    if status == 'completed':
                        print(f"        🎉 训练完成！")
                        print(f"        📊 验证阶段统计:")
                        analyze_validation_phases(validation_phases)
                        return True
                    elif status == 'failed':
                        print(f"        ❌ 训练失败")
                        print(f"        📊 验证阶段统计:")
                        analyze_validation_phases(validation_phases)
                        return False
                    
                    time.sleep(10)  # 每10秒检查一次
                else:
                    print(f"   ❌ 获取进度失败: {progress_data.get('error')}")
                    return False
            else:
                print(f"   ❌ 进度请求失败: {progress_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            return False
    
    print(f"⏰ 监控超时")
    print(f"📊 验证阶段统计:")
    analyze_validation_phases(validation_phases)
    return False

def analyze_validation_phases(validation_phases):
    """分析验证阶段统计"""
    if not validation_phases:
        print("   ❌ 没有完成任何验证阶段")
        return
    
    total_phases = len(validation_phases)
    successful_phases = len([p for p in validation_phases if p['status'] == 'completed'])
    suspicious_phases = len([p for p in validation_phases if p['status'] == 'suspicious'])
    
    print(f"   总验证阶段: {total_phases}")
    print(f"   成功验证: {successful_phases}")
    print(f"   可疑验证: {suspicious_phases}")
    
    if total_phases > 0:
        success_rate = successful_phases / total_phases * 100
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print(f"   ✅ 验证死锁修复效果良好")
        elif success_rate >= 50:
            print(f"   ⚠️ 验证死锁部分修复")
        else:
            print(f"   ❌ 验证死锁修复效果不佳")
    
    # 显示详细信息
    print(f"   详细信息:")
    for i, phase in enumerate(validation_phases[-5:], 1):  # 只显示最后5个
        status_icon = '✅' if phase['status'] == 'completed' else '⚠️'
        print(f"     {status_icon} 轮次{phase['epoch']}: 验证损失={phase['val_loss']:.4f}, 时间={phase['time']:.1f}s")

def main():
    """主函数"""
    print('🔧 验证死锁修复效果测试')
    print('=' * 80)
    
    print("🎯 测试目标:")
    print("   - 验证单线程数据加载是否解决验证死锁")
    print("   - 确认验证损失不再为0.0")
    print("   - 监控训练过程的稳定性")
    print("   - 评估修复效果")
    
    # 测试验证死锁修复
    success = test_validation_deadlock_fix()
    
    print(f"\n🎯 验证死锁修复测试结果:")
    if success:
        print(f"✅ 验证死锁修复成功！")
        print(f"💡 关键改进:")
        print(f"   - 单线程数据加载 (num_workers=0)")
        print(f"   - 禁用pin_memory避免内存问题")
        print(f"   - 验证阶段超时保护")
        print(f"   - 完善的异常处理")
        print(f"   - 详细的进度监控")
    else:
        print(f"❌ 验证死锁修复需要进一步优化")
        print(f"🔧 建议:")
        print(f"   - 检查GPU驱动和CUDA版本")
        print(f"   - 进一步减小batch_size")
        print(f"   - 考虑使用CPU训练")

if __name__ == "__main__":
    main()
