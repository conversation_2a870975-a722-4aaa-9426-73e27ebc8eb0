#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度诊断25%卡住问题
分析训练开始阶段的具体卡住原因
"""

import sqlite3
import json
import time
import torch
import psutil
from datetime import datetime

def check_25_percent_stuck():
    """检查25%卡住的具体情况"""
    print('🔍 深度诊断25%卡住问题')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找卡在25%的任务
        cursor.execute('''
            SELECT id, name, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs, config
            FROM training_tasks 
            WHERE status = 'running' AND progress >= 20 AND progress <= 30
            ORDER BY updated_at DESC
        ''')
        
        stuck_tasks = cursor.fetchall()
        
        if stuck_tasks:
            print(f"🔴 发现 {len(stuck_tasks)} 个卡在25%的任务:")
            
            for task in stuck_tasks:
                task_id, name, status, progress, current_epoch, total_epochs, created_at, updated_at, logs, config = task
                
                print(f"\n📊 任务详情:")
                print(f"   名称: {name}")
                print(f"   ID: {task_id}")
                print(f"   进度: {progress}%")
                print(f"   轮次: {current_epoch}/{total_epochs}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                
                # 计算卡住时间
                if updated_at:
                    try:
                        update_time = datetime.fromisoformat(updated_at)
                        now = datetime.now()
                        stuck_duration = (now - update_time).total_seconds()
                        print(f"   卡住时间: {stuck_duration:.1f} 秒 ({stuck_duration/60:.1f} 分钟)")
                    except:
                        print(f"   ❌ 时间解析失败")
                
                # 分析日志
                if logs:
                    try:
                        log_data = json.loads(logs) if isinstance(logs, str) else logs
                        stage = log_data.get('stage', 'unknown')
                        message = log_data.get('message', 'N/A')
                        print(f"   当前阶段: {stage}")
                        print(f"   日志消息: {message}")
                        
                        # 分析卡住的具体阶段
                        if stage == 'model_training':
                            print(f"   🔍 分析: 卡在模型训练阶段")
                            print(f"   🎯 可能原因: 训练循环、数据加载、模型计算")
                        elif stage == 'data_preparation':
                            print(f"   🔍 分析: 卡在数据准备阶段")
                            print(f"   🎯 可能原因: 数据加载、特征计算")
                        elif stage == 'model_validation':
                            print(f"   🔍 分析: 卡在验证阶段")
                            print(f"   🎯 可能原因: 验证循环、验证数据")
                        
                    except Exception as e:
                        print(f"   ❌ 日志解析失败: {e}")
                
                # 分析配置
                if config:
                    try:
                        config_data = json.loads(config) if isinstance(config, str) else config
                        print(f"   配置分析:")
                        print(f"     模型类型: {config_data.get('model_type', 'unknown')}")
                        print(f"     批次大小: {config_data.get('batch_size', 'unknown')}")
                        print(f"     使用GPU: {config_data.get('use_gpu', 'unknown')}")
                        print(f"     增强特征: {config_data.get('use_enhanced_features', 'unknown')}")
                        print(f"     数据天数: {config_data.get('data_config', {}).get('training_days', 'unknown')}")
                    except:
                        print(f"   ❌ 配置解析失败")
                
                return task_id, stuck_duration if 'stuck_duration' in locals() else 0
        else:
            print("✅ 没有发现卡在25%的任务")
            return None, 0
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None, 0

def analyze_system_resources():
    """分析系统资源状态"""
    print('\n🔍 分析系统资源状态')
    print('=' * 60)
    
    try:
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        print(f"💻 CPU状态:")
        print(f"   使用率: {cpu_percent}%")
        print(f"   核心数: {cpu_count}")
        
        # 内存信息
        memory = psutil.virtual_memory()
        print(f"🧠 内存状态:")
        print(f"   总内存: {memory.total / (1024**3):.1f} GB")
        print(f"   已使用: {memory.used / (1024**3):.1f} GB ({memory.percent}%)")
        print(f"   可用内存: {memory.available / (1024**3):.1f} GB")
        
        # GPU信息
        if torch.cuda.is_available():
            print(f"🎮 GPU状态:")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_memory = torch.cuda.memory_allocated(i) / (1024**3)
                gpu_memory_max = torch.cuda.max_memory_allocated(i) / (1024**3)
                gpu_memory_total = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                print(f"   GPU {i}: {gpu_memory:.1f}GB / {gpu_memory_total:.1f}GB ({gpu_memory/gpu_memory_total*100:.1f}%)")
        else:
            print(f"❌ GPU不可用")
        
        # 磁盘信息
        disk = psutil.disk_usage('.')
        print(f"💾 磁盘状态:")
        print(f"   总空间: {disk.total / (1024**3):.1f} GB")
        print(f"   已使用: {disk.used / (1024**3):.1f} GB ({disk.used/disk.total*100:.1f}%)")
        print(f"   可用空间: {disk.free / (1024**3):.1f} GB")
        
        # 分析资源瓶颈
        print(f"\n🔍 资源瓶颈分析:")
        if cpu_percent > 90:
            print(f"   🔴 CPU使用率过高: {cpu_percent}%")
        elif cpu_percent > 70:
            print(f"   ⚠️ CPU使用率较高: {cpu_percent}%")
        else:
            print(f"   ✅ CPU使用率正常: {cpu_percent}%")
        
        if memory.percent > 90:
            print(f"   🔴 内存使用率过高: {memory.percent}%")
        elif memory.percent > 70:
            print(f"   ⚠️ 内存使用率较高: {memory.percent}%")
        else:
            print(f"   ✅ 内存使用率正常: {memory.percent}%")
        
        if disk.used/disk.total > 0.9:
            print(f"   🔴 磁盘空间不足: {disk.used/disk.total*100:.1f}%")
        elif disk.used/disk.total > 0.8:
            print(f"   ⚠️ 磁盘空间较少: {disk.used/disk.total*100:.1f}%")
        else:
            print(f"   ✅ 磁盘空间充足: {disk.used/disk.total*100:.1f}%")
        
    except Exception as e:
        print(f"❌ 资源分析失败: {e}")

def analyze_25_percent_causes():
    """分析25%卡住的具体原因"""
    print('\n🔍 分析25%卡住的具体原因')
    print('=' * 60)
    
    causes = [
        {
            'stage': '数据准备阶段 (20-25%)',
            'possible_causes': [
                '特征计算耗时过长',
                '数据加载器初始化失败',
                '增强特征计算卡住',
                '数据预处理异常',
                '内存分配失败'
            ],
            'solutions': [
                '简化特征计算逻辑',
                '使用单线程数据加载',
                '禁用增强特征测试',
                '减少数据批次大小',
                '增加内存监控'
            ]
        },
        {
            'stage': '模型初始化阶段 (25%)',
            'possible_causes': [
                'PyTorch模型创建失败',
                'GPU内存分配失败',
                '模型参数初始化卡住',
                '优化器初始化异常',
                '损失函数初始化问题'
            ],
            'solutions': [
                '使用CPU训练测试',
                '减小模型规模',
                '检查模型结构',
                '更换优化器',
                '简化损失函数'
            ]
        },
        {
            'stage': '训练循环开始 (25%)',
            'possible_causes': [
                '第一个批次处理卡住',
                '前向传播计算异常',
                '梯度计算失败',
                '数据类型不匹配',
                '设备转换失败'
            ],
            'solutions': [
                '添加批次超时保护',
                '检查数据形状',
                '验证数据类型',
                '强制CPU计算',
                '简化模型结构'
            ]
        }
    ]
    
    print("📋 25%卡住原因分析:")
    for i, cause in enumerate(causes, 1):
        print(f"\n{i}. {cause['stage']}")
        print("   可能原因:")
        for reason in cause['possible_causes']:
            print(f"     • {reason}")
        print("   解决方案:")
        for solution in cause['solutions']:
            print(f"     ✓ {solution}")

def create_25_percent_fix():
    """创建25%卡住修复脚本"""
    print('\n🔧 创建25%卡住修复脚本')
    print('=' * 60)
    
    fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""25%卡住紧急修复脚本"""

import sqlite3
import json
from datetime import datetime

def fix_25_percent_stuck():
    """修复25%卡住问题"""
    print("🚨 修复25%卡住问题...")
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找卡在25%的任务
        cursor.execute("""
            SELECT id, name, progress, current_epoch, total_epochs
            FROM training_tasks 
            WHERE status = 'running' 
            AND progress >= 20 AND progress <= 30
            AND datetime(updated_at) < datetime('now', '-2 minutes')
        """)
        
        stuck_tasks = cursor.fetchall()
        
        if not stuck_tasks:
            print("✅ 没有发现卡在25%的任务")
            return
        
        print(f"🔴 发现 {len(stuck_tasks)} 个卡在25%的任务，开始修复...")
        
        for task_id, name, progress, current_epoch, total_epochs in stuck_tasks:
            print(f"\\n📊 修复任务: {name}")
            print(f"   当前进度: {progress}%")
            
            # 强制推进到30%
            new_progress = 30.0
            new_epoch = max(1, current_epoch)
            
            cursor.execute("""
                UPDATE training_tasks 
                SET progress = ?, current_epoch = ?, updated_at = ?,
                    logs = ?
                WHERE id = ?
            """, (
                new_progress, 
                new_epoch, 
                datetime.now().isoformat(),
                json.dumps({
                    "stage": "force_progress_update",
                    "message": f"强制推进25%卡住: {progress}% -> {new_progress}%",
                    "action": "emergency_fix_25_percent",
                    "timestamp": datetime.now().isoformat()
                }),
                task_id
            ))
            
            print(f"   ✅ 强制推进: {progress}% -> {new_progress}%")
        
        conn.commit()
        conn.close()
        
        print(f"\\n🎉 25%卡住修复完成！")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    fix_25_percent_stuck()
'''
    
    with open('fix_25_percent_stuck.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 已创建25%卡住修复脚本: fix_25_percent_stuck.py")

def main():
    """主函数"""
    print('🔧 25%卡住问题深度诊断')
    print('=' * 80)
    
    # 检查25%卡住情况
    task_id, stuck_duration = check_25_percent_stuck()
    
    # 分析系统资源
    analyze_system_resources()
    
    # 分析25%卡住原因
    analyze_25_percent_causes()
    
    # 创建修复脚本
    create_25_percent_fix()
    
    print(f"\n🎯 诊断结果")
    print('=' * 80)
    
    if task_id:
        print(f"🔴 确认发现25%卡住问题")
        print(f"   任务ID: {task_id}")
        print(f"   卡住时间: {stuck_duration:.1f} 秒")
        
        if stuck_duration > 300:  # 5分钟
            print(f"🚨 严重卡住，建议立即修复")
        else:
            print(f"⚠️ 轻微卡住，可以观察或修复")
    else:
        print(f"✅ 当前没有25%卡住的任务")
    
    print(f"\n🚀 建议行动:")
    print(f"1. 🔧 如果确认25%卡住: python fix_25_percent_stuck.py")
    print(f"2. 🔄 重启Flask应用清理状态")
    print(f"3. 📊 使用简化配置重新训练")
    print(f"4. 🎮 考虑使用CPU训练测试")

if __name__ == "__main__":
    main()
