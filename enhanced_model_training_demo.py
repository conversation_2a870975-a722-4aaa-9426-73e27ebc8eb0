#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的深度学习模型训练演示
展示所有优化建议的实现：
1. 增强的布林带特征 (挤压信号、突破信号)
2. ATR (平均真实波幅)
3. 随机指标 (Stochastic Oscillator)
4. 组合信号和确认机制
5. 动态风险管理
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_sample_data(n_samples=1000):
    """生成示例价格数据"""
    logger.info(f"📊 生成 {n_samples} 个样本的示例数据...")
    
    # 生成基础价格走势
    np.random.seed(42)
    
    # 模拟价格随机游走
    returns = np.random.normal(0.0001, 0.02, n_samples)  # 日收益率
    prices = 100 * np.exp(np.cumsum(returns))  # 累积价格
    
    # 生成OHLC数据
    data = []
    for i in range(n_samples):
        close = prices[i]
        
        # 生成日内波动
        daily_range = close * np.random.uniform(0.005, 0.03)  # 0.5%-3%的日内波动
        
        high = close + np.random.uniform(0, daily_range)
        low = close - np.random.uniform(0, daily_range)
        open_price = low + np.random.uniform(0, high - low)
        
        # 确保OHLC逻辑正确
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        # 生成成交量
        volume = np.random.uniform(1000, 10000)
        
        data.append([open_price, high, low, close, volume])
    
    return np.array(data)

def demonstrate_enhanced_features():
    """演示增强特征工程"""
    logger.info("🚀 开始演示增强特征工程...")
    
    # 生成示例数据
    price_data = generate_sample_data(500)
    
    # 导入增强特征工程服务
    from services.enhanced_feature_engineering import EnhancedFeatureEngineering
    
    # 创建特征工程实例
    feature_engineer = EnhancedFeatureEngineering()
    
    # 计算所有增强特征
    print("\n" + "="*60)
    print("🔧 计算增强技术指标特征")
    print("="*60)
    
    features_dict = feature_engineer.calculate_all_enhanced_features(price_data)
    
    # 显示特征统计
    print(f"\n📊 特征统计:")
    print(f"   总特征数量: {len(features_dict)}")
    
    # 显示各类特征数量
    feature_categories = {}
    for feature_name in features_dict.keys():
        category = feature_name.split('_')[0]
        feature_categories[category] = feature_categories.get(category, 0) + 1
    
    for category, count in feature_categories.items():
        print(f"   {category} 特征: {count} 个")
    
    # 获取推荐特征
    recommended_features = feature_engineer.get_recommended_features()
    print(f"\n🏆 推荐特征 ({len(recommended_features)} 个):")
    for i, feature in enumerate(recommended_features[:10]):  # 显示前10个
        print(f"   {i+1}. {feature}")
    if len(recommended_features) > 10:
        print(f"   ... 还有 {len(recommended_features) - 10} 个特征")
    
    # 准备特征矩阵
    feature_matrix = feature_engineer.prepare_features_for_model(
        features_dict, recommended_features
    )
    
    print(f"\n✅ 特征矩阵准备完成:")
    print(f"   形状: {feature_matrix.shape}")
    print(f"   数据范围: {feature_matrix.min():.3f} - {feature_matrix.max():.3f}")
    
    return features_dict, feature_matrix, price_data

def demonstrate_dynamic_risk_management():
    """演示动态风险管理"""
    logger.info("🛡️ 开始演示动态风险管理...")
    
    # 生成示例数据
    price_data = generate_sample_data(200)
    
    # 转换为pandas Series
    high = pd.Series(price_data[:, 1])
    low = pd.Series(price_data[:, 2])
    close = pd.Series(price_data[:, 3])
    
    # 导入动态风险管理服务
    from services.dynamic_risk_management import DynamicRiskManager
    
    # 创建风险管理实例
    risk_manager = DynamicRiskManager()
    
    print("\n" + "="*60)
    print("🛡️ 动态风险管理演示")
    print("="*60)
    
    # 计算动态止损位
    stop_data = risk_manager.calculate_dynamic_stops(high, low, close)
    
    current_price = close.iloc[-1]
    current_atr = stop_data['atr'].iloc[-1]
    long_stop = stop_data['long_stop'].iloc[-1]
    short_stop = stop_data['short_stop'].iloc[-1]
    
    print(f"\n📏 当前市场状况:")
    print(f"   当前价格: {current_price:.2f}")
    print(f"   ATR: {current_atr:.4f}")
    print(f"   ATR占价格比例: {(current_atr/current_price)*100:.2f}%")
    
    print(f"\n🎯 动态止损建议:")
    print(f"   多头止损位: {long_stop:.2f} (距离: {((current_price-long_stop)/current_price)*10000:.0f} pips)")
    print(f"   空头止损位: {short_stop:.2f} (距离: {((short_stop-current_price)/current_price)*10000:.0f} pips)")
    
    # 计算波动性调整参数
    vol_data = risk_manager.calculate_volatility_adjusted_parameters(high, low, close)
    
    current_vol_state = vol_data['volatility_state'].iloc[-1]
    current_atr_multiplier = vol_data['atr_multiplier'].iloc[-1]
    current_risk_adjustment = vol_data['risk_adjustment'].iloc[-1]
    
    print(f"\n📊 波动性分析:")
    print(f"   当前波动性状态: {current_vol_state}")
    print(f"   建议ATR倍数: {current_atr_multiplier:.1f}")
    print(f"   风险调整系数: {current_risk_adjustment:.1f}")
    
    # 生成风险管理信号
    risk_signals = risk_manager.generate_risk_signals(high, low, close)
    
    current_risk_warning = risk_signals['risk_warnings'].iloc[-1]
    current_position_adjustment = risk_signals['position_adjustment'].iloc[-1]
    
    print(f"\n🚨 风险管理信号:")
    print(f"   风险警告: {'是' if current_risk_warning else '否'}")
    print(f"   仓位调整建议: {current_position_adjustment}")
    
    # 计算仓位大小示例
    account_balance = 10000  # 假设账户余额10000美元
    risk_per_trade = 0.02    # 每笔交易风险2%
    
    position_size = risk_manager.calculate_position_sizing(
        account_balance, risk_per_trade, stop_data['stop_distance'], close
    )
    
    current_position_size = position_size.iloc[-1]
    position_value = current_position_size * current_price
    
    print(f"\n💰 仓位管理建议:")
    print(f"   账户余额: ${account_balance:,.2f}")
    print(f"   单笔风险: {risk_per_trade*100:.1f}%")
    print(f"   建议仓位大小: {current_position_size:.2f} 手")
    print(f"   仓位价值: ${position_value:,.2f}")
    print(f"   仓位占账户比例: {(position_value/account_balance)*100:.1f}%")
    
    return risk_signals, stop_data

def demonstrate_model_training_with_enhanced_features():
    """演示使用增强特征的模型训练"""
    logger.info("🤖 开始演示增强特征模型训练...")
    
    print("\n" + "="*60)
    print("🤖 增强特征模型训练演示")
    print("="*60)
    
    # 生成示例数据
    price_data = generate_sample_data(1000)
    
    # 模拟深度学习服务配置
    config = {
        'model_type': 'lstm',
        'sequence_length': 60,
        'hidden_size': 128,
        'num_layers': 2,
        'dropout': 0.2,
        'epochs': 50,
        'batch_size': 32,
        'learning_rate': 0.001,
        'use_enhanced_features': True,  # 启用增强特征
        'analyze_feature_importance': True,  # 启用特征重要性分析
        'selected_features': None  # 使用推荐特征集
    }
    
    print(f"📋 训练配置:")
    print(f"   模型类型: {config['model_type'].upper()}")
    print(f"   序列长度: {config['sequence_length']}")
    print(f"   隐藏层大小: {config['hidden_size']}")
    print(f"   训练轮次: {config['epochs']}")
    print(f"   使用增强特征: {'是' if config['use_enhanced_features'] else '否'}")
    
    # 导入深度学习服务
    try:
        from services.deep_learning_service import DeepLearningService
        
        # 创建深度学习服务实例
        dl_service = DeepLearningService()
        
        # 计算增强特征
        print(f"\n🔧 计算增强特征...")
        feature_matrix = dl_service._calculate_enhanced_features(price_data, config)
        
        print(f"✅ 特征计算完成:")
        print(f"   特征矩阵形状: {feature_matrix.shape}")
        print(f"   特征数量: {feature_matrix.shape[1]}")
        
        # 显示特征重要性分析结果
        if 'feature_importance' in config:
            importance_scores = config['feature_importance']
            print(f"\n🏆 特征重要性排名 (前10):")
            
            sorted_features = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)
            for i, (feature, score) in enumerate(sorted_features[:10]):
                print(f"   {i+1}. {feature}: {score:.4f}")
        
        print(f"\n✅ 增强特征演示完成！")
        print(f"💡 关键优势:")
        print(f"   • 布林带挤压信号有效识别突破机会")
        print(f"   • ATR动态调整提供自适应风险管理")
        print(f"   • 随机指标增强捕捉超买超卖状态")
        print(f"   • 组合信号减少假信号，提高准确性")
        print(f"   • 市场状态分析适应不同市场环境")
        
        return feature_matrix, config
        
    except ImportError as e:
        logger.error(f"❌ 无法导入深度学习服务: {e}")
        print(f"⚠️ 深度学习服务不可用，跳过模型训练演示")
        return None, config

def create_visualization():
    """创建可视化图表"""
    logger.info("📊 创建可视化图表...")
    
    # 生成示例数据
    price_data = generate_sample_data(200)
    
    # 转换为pandas DataFrame
    df = pd.DataFrame(price_data, columns=['Open', 'High', 'Low', 'Close', 'Volume'])
    df.index = pd.date_range(start='2024-01-01', periods=len(df), freq='D')
    
    # 计算技术指标
    from services.technical_indicators import TechnicalIndicators
    ti = TechnicalIndicators()
    
    # 布林带
    bb_data = ti.enhanced_bollinger_bands(df['Close'])
    
    # ATR
    atr_data = ti.enhanced_atr(df['High'], df['Low'], df['Close'])
    
    # 创建图表
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    
    # 第一个子图：价格和布林带
    axes[0].plot(df.index, df['Close'], label='收盘价', color='black', linewidth=1)
    axes[0].plot(df.index, bb_data['upper'], label='布林带上轨', color='red', alpha=0.7)
    axes[0].plot(df.index, bb_data['middle'], label='布林带中轨', color='blue', alpha=0.7)
    axes[0].plot(df.index, bb_data['lower'], label='布林带下轨', color='green', alpha=0.7)
    axes[0].fill_between(df.index, bb_data['upper'], bb_data['lower'], alpha=0.1, color='gray')
    
    # 标记挤压信号
    squeeze_points = df.index[bb_data['bb_squeeze']]
    if len(squeeze_points) > 0:
        axes[0].scatter(squeeze_points, df.loc[squeeze_points, 'Close'], 
                       color='orange', marker='o', s=50, label='布林带挤压', zorder=5)
    
    axes[0].set_title('价格走势与布林带', fontsize=14, fontweight='bold')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # 第二个子图：%B指标和带宽
    ax2_twin = axes[1].twinx()
    
    axes[1].plot(df.index, bb_data['percent_b'], label='%B指标', color='purple', linewidth=2)
    axes[1].axhline(y=0.8, color='red', linestyle='--', alpha=0.7, label='超买线')
    axes[1].axhline(y=0.2, color='green', linestyle='--', alpha=0.7, label='超卖线')
    axes[1].axhline(y=0.5, color='gray', linestyle='-', alpha=0.5)
    
    ax2_twin.plot(df.index, bb_data['band_width'], label='带宽', color='brown', alpha=0.7)
    
    axes[1].set_title('布林带%B指标与带宽', fontsize=14, fontweight='bold')
    axes[1].set_ylabel('%B指标', color='purple')
    ax2_twin.set_ylabel('带宽', color='brown')
    axes[1].legend(loc='upper left')
    ax2_twin.legend(loc='upper right')
    axes[1].grid(True, alpha=0.3)
    
    # 第三个子图：ATR和波动性状态
    axes[2].plot(df.index, atr_data['atr'], label='ATR', color='red', linewidth=2)
    axes[2].fill_between(df.index, 0, atr_data['atr'], alpha=0.3, color='red')
    
    # 标记波动性状态
    low_vol_points = df.index[atr_data['low_volatility']]
    high_vol_points = df.index[atr_data['high_volatility']]
    
    if len(low_vol_points) > 0:
        axes[2].scatter(low_vol_points, atr_data['atr'].loc[low_vol_points], 
                       color='green', marker='v', s=30, label='低波动性', alpha=0.7)
    
    if len(high_vol_points) > 0:
        axes[2].scatter(high_vol_points, atr_data['atr'].loc[high_vol_points], 
                       color='red', marker='^', s=30, label='高波动性', alpha=0.7)
    
    axes[2].set_title('ATR与波动性状态', fontsize=14, fontweight='bold')
    axes[2].set_ylabel('ATR')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('enhanced_features_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 可视化图表已保存为 'enhanced_features_visualization.png'")

def main():
    """主函数"""
    print("🚀 增强的深度学习模型训练优化演示")
    print("="*80)
    
    try:
        # 1. 演示增强特征工程
        features_dict, feature_matrix, price_data = demonstrate_enhanced_features()
        
        # 2. 演示动态风险管理
        risk_signals, stop_data = demonstrate_dynamic_risk_management()
        
        # 3. 演示模型训练
        model_result, config = demonstrate_model_training_with_enhanced_features()
        
        # 4. 创建可视化
        create_visualization()
        
        print(f"\n🎉 演示完成！")
        print(f"📋 总结:")
        print(f"   ✅ 增强布林带特征：挤压信号、突破信号、%B指标")
        print(f"   ✅ ATR动态风险管理：自适应止损、波动性分析")
        print(f"   ✅ 增强随机指标：超买超卖、金叉死叉信号")
        print(f"   ✅ 组合信号确认：多指标验证，减少假信号")
        print(f"   ✅ 市场状态分析：趋势识别、波动性分类")
        print(f"   ✅ 特征重要性分析：数据驱动的特征选择")
        
    except Exception as e:
        logger.error(f"❌ 演示过程中出现错误: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
