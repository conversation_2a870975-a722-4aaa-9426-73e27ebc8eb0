#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试置信度类型修复
"""

import requests
import json

def test_confidence_fix():
    """测试置信度类型修复"""
    
    print("🧪 测试置信度类型修复")
    print("=" * 60)
    
    # 测试推理API
    print("📊 步骤1: 测试推理API")
    print("-" * 40)
    
    try:
        # 获取最新的增强特征模型
        models_response = requests.get('http://localhost:5000/api/models')
        if models_response.status_code == 200:
            models = models_response.json()
            enhanced_models = [m for m in models if m.get('name', '').find('增强') != -1]
            
            if enhanced_models:
                model = enhanced_models[0]
                print(f"✅ 找到增强特征模型: {model['name']}")
                
                # 测试推理
                inference_data = {
                    'model_id': model['id'],
                    'use_enhanced_features': True,
                    'feature_selection_strategy': 'recommended'
                }
                
                response = requests.post('http://localhost:5000/api/inference', json=inference_data)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        results = result.get('results', [])
                        if results:
                            pred = results[0]
                            confidence = pred.get('confidence', 0)
                            print(f"✅ 推理成功")
                            print(f"   预测: {pred.get('prediction')}")
                            print(f"   置信度: {confidence} (类型: {type(confidence)})")
                            print(f"   当前价格: {pred.get('current_price', 0):.5f}")
                            
                            # 测试置信度比较
                            try:
                                test_threshold = 0.3
                                comparison_result = confidence >= test_threshold
                                print(f"   ✅ 置信度比较测试: {confidence} >= {test_threshold} = {comparison_result}")
                            except Exception as e:
                                print(f"   ❌ 置信度比较失败: {e}")
                                return False
                        else:
                            print("❌ 推理成功但无结果")
                            return False
                    else:
                        print(f"❌ 推理失败: {result.get('error')}")
                        return False
                else:
                    print(f"❌ 推理请求失败: HTTP {response.status_code}")
                    return False
            else:
                print("❌ 没有找到增强特征模型")
                return False
        else:
            print(f"❌ 获取模型列表失败: HTTP {models_response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        return False
    
    # 测试回测API
    print(f"\n📊 步骤2: 测试回测API")
    print("-" * 40)
    
    try:
        backtest_data = {
            'model_id': model['id'],
            'symbol': 'XAUUSD',
            'timeframe': '1h',
            'days': 7,
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'min_confidence': 0.3,  # 确保是浮点数
            'use_enhanced_features': True,
            'feature_selection_strategy': 'recommended'
        }
        
        print(f"📋 回测参数:")
        print(f"   模型: {model['name']}")
        print(f"   最小置信度: {backtest_data['min_confidence']} (类型: {type(backtest_data['min_confidence'])})")
        print(f"   使用增强特征: {backtest_data['use_enhanced_features']}")
        
        response = requests.post('http://localhost:5000/api/backtest', json=backtest_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('statistics', {})
                trades = result.get('trades', [])
                
                print(f"✅ 回测成功!")
                print(f"   交易数量: {len(trades)}")
                print(f"   总收益: {stats.get('total_return', 0):.2f}%")
                print(f"   胜率: {stats.get('win_rate', 0):.1f}%")
                
                if len(trades) > 0:
                    print(f"   📈 交易示例:")
                    for i, trade in enumerate(trades[:3], 1):
                        confidence = trade.get('confidence', 0)
                        print(f"      {i}. {trade['prediction']} @ {trade['entry_price']:.5f}")
                        print(f"         置信度: {confidence} (类型: {type(confidence)})")
                        print(f"         盈亏: ${trade['profit']:.2f}")
                
                return True
            else:
                error_msg = result.get('error', 'Unknown error')
                print(f"❌ 回测失败: {error_msg}")
                
                # 检查是否还有类型比较错误
                if "'>' not supported between instances of 'str' and 'float'" in error_msg:
                    print(f"🚨 仍然存在字符串和浮点数比较错误!")
                    return False
                else:
                    print(f"💡 这是其他类型的错误，置信度类型问题已修复")
                    return True
        else:
            print(f"❌ 回测请求失败: HTTP {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ 回测测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🚀 测试置信度类型修复")
    print("=" * 80)
    
    if test_confidence_fix():
        print(f"\n✅ 置信度类型修复测试成功!")
        print(f"💡 现在可以正常进行回测，不会出现类型比较错误")
    else:
        print(f"\n❌ 置信度类型修复测试失败")
        print(f"💡 可能需要进一步检查其他地方的类型问题")

if __name__ == "__main__":
    main()
