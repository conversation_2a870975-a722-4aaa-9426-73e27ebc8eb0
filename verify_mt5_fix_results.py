#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证MT5修复结果
测试手动下单和持仓显示功能是否正常
"""

import requests
import json
import time
from datetime import datetime

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post('http://127.0.0.1:5000/login', data=login_data)
    if response.status_code == 200:
        print("✅ 登录成功")
        return session
    else:
        print(f"❌ 登录失败: {response.status_code}")
        return None

def test_manual_trading_after_fix(session):
    """测试修复后的手动下单功能"""
    print("\n🧪 测试修复后的手动下单功能")
    print("=" * 60)
    
    # 构建手动交易数据
    trade_data = {
        'symbol': 'XAUUSD',
        'action': 'BUY',
        'lot_size': 0.01,
        'stop_loss_pips': 50,
        'take_profit_pips': 100,
        'trading_config': {
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'enable_dynamic_sl': True,
            'enable_trailing_stop': False,
            'account_type': 'demo'
        },
        'inference_result': {
            'prediction': 'BUY',
            'confidence': 1.0,
            'reasoning': '修复后手动下单测试'
        },
        'manual_trade': True
    }
    
    print(f"📊 手动交易参数:")
    print(f"   品种: {trade_data['symbol']}")
    print(f"   方向: {trade_data['action']}")
    print(f"   手数: {trade_data['lot_size']}")
    
    try:
        print(f"\n🔄 发送手动交易请求...")
        response = session.post('http://127.0.0.1:5000/api/deep-learning/execute-trade', 
                               json=trade_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                order_id = result.get('order_id')
                print(f"✅ 手动下单成功: 订单ID {order_id}")
                
                # 验证订单是否在MT5中
                print(f"\n🔍 验证订单是否在MT5中...")
                time.sleep(2)  # 等待2秒
                
                mt5_found = verify_order_in_mt5(order_id)
                if mt5_found:
                    print(f"✅ 订单在MT5中找到 - 问题已修复！")
                    return True, order_id
                else:
                    print(f"❌ 订单在MT5中未找到 - 问题仍存在")
                    return False, order_id
            else:
                print(f"❌ 手动下单失败: {result.get('error')}")
                return False, None
        else:
            print(f"❌ 手动下单请求失败: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ 手动下单测试异常: {e}")
        return False, None

def verify_order_in_mt5(order_id):
    """验证订单是否在MT5中"""
    try:
        import MetaTrader5 as mt5
        
        # 检查持仓
        positions = mt5.positions_get()
        if positions:
            for pos in positions:
                if pos.ticket == order_id:
                    print(f"   ✅ 在持仓中找到: 票号 {pos.ticket}, 品种 {pos.symbol}, 注释 '{pos.comment}'")
                    return True
        
        print(f"   ❌ 在MT5持仓中未找到订单 {order_id}")
        return False
        
    except Exception as e:
        print(f"   ❌ 验证MT5订单异常: {e}")
        return False

def test_position_display_consistency(session):
    """测试持仓显示一致性"""
    print("\n🔍 测试持仓显示一致性")
    print("=" * 60)
    
    results = {}
    
    # 1. 测试交易统计API
    print("1. 测试交易统计API")
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/trading-statistics')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('statistics', {})
                current_positions = stats.get('current_positions', 0)
                results['trading_stats'] = current_positions
                print(f"   ✅ 交易统计API: {current_positions} 个持仓")
            else:
                print(f"   ❌ 交易统计API失败: {result.get('error')}")
                results['trading_stats'] = -1
        else:
            print(f"   ❌ 交易统计API请求失败: {response.status_code}")
            results['trading_stats'] = -1
    except Exception as e:
        print(f"   ❌ 交易统计API异常: {e}")
        results['trading_stats'] = -1
    
    # 2. 测试MT5持仓API
    print("\n2. 测试MT5持仓API")
    try:
        response = session.get('http://127.0.0.1:5000/api/mt5/positions')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                positions = result.get('positions', [])
                results['mt5_api'] = len(positions)
                print(f"   ✅ MT5持仓API: {len(positions)} 个持仓")
                
                # 显示前3个持仓
                for i, pos in enumerate(positions[:3]):
                    print(f"   持仓 {i+1}: {pos.get('symbol')} 票号{pos.get('ticket')} {pos.get('volume')}手")
            else:
                print(f"   ❌ MT5持仓API失败: {result.get('error')}")
                results['mt5_api'] = -1
        else:
            print(f"   ❌ MT5持仓API请求失败: {response.status_code}")
            results['mt5_api'] = -1
    except Exception as e:
        print(f"   ❌ MT5持仓API异常: {e}")
        results['mt5_api'] = -1
    
    # 3. 测试持仓详情API
    print("\n3. 测试持仓详情API")
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/position-details')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                positions = result.get('positions', [])
                results['position_details'] = len(positions)
                print(f"   ✅ 持仓详情API: {len(positions)} 个持仓")
            else:
                print(f"   ❌ 持仓详情API失败: {result.get('error')}")
                results['position_details'] = -1
        else:
            print(f"   ❌ 持仓详情API请求失败: {response.status_code}")
            results['position_details'] = -1
    except Exception as e:
        print(f"   ❌ 持仓详情API异常: {e}")
        results['position_details'] = -1
    
    # 4. 直接查询MT5
    print("\n4. 直接查询MT5")
    try:
        import MetaTrader5 as mt5
        positions = mt5.positions_get()
        if positions:
            results['mt5_direct'] = len(positions)
            print(f"   ✅ MT5直接查询: {len(positions)} 个持仓")
        else:
            results['mt5_direct'] = 0
            print(f"   ✅ MT5直接查询: 0 个持仓")
    except Exception as e:
        print(f"   ❌ MT5直接查询异常: {e}")
        results['mt5_direct'] = -1
    
    # 5. 分析一致性
    print(f"\n📊 一致性分析:")
    print(f"   交易统计API: {results.get('trading_stats', 'N/A')}")
    print(f"   MT5持仓API: {results.get('mt5_api', 'N/A')}")
    print(f"   持仓详情API: {results.get('position_details', 'N/A')}")
    print(f"   MT5直接查询: {results.get('mt5_direct', 'N/A')}")
    
    # 检查一致性
    valid_results = [v for v in results.values() if v >= 0]
    if len(set(valid_results)) <= 1:
        print(f"✅ 持仓数据一致")
        return True
    else:
        print(f"❌ 持仓数据不一致")
        return False

def test_ai_inference_trading_integration(session):
    """测试AI推理交易集成"""
    print("\n🤖 测试AI推理交易集成")
    print("=" * 60)
    
    # 1. 检查AI交易状态
    print("1. 检查AI交易状态")
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/auto-trading/status')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                active = result.get('active', False)
                print(f"   AI交易状态: {'✅ 活跃' if active else '⚠️ 未活跃'}")
                
                if active:
                    model_info = result.get('model_info', {})
                    print(f"   使用模型: {model_info.get('name', 'Unknown')}")
                    print(f"   交易品种: {model_info.get('symbol', 'Unknown')}")
            else:
                print(f"   ❌ 获取AI交易状态失败: {result.get('error')}")
        else:
            print(f"   ❌ AI交易状态请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ AI交易状态检查异常: {e}")
    
    # 2. 检查MT5连接状态
    print("\n2. 检查MT5连接状态")
    try:
        response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('connected'):
                print(f"   ✅ MT5连接正常")
            else:
                print(f"   ❌ MT5连接异常: {result.get('error', 'Unknown')}")
        else:
            print(f"   ❌ MT5连接状态请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ MT5连接状态检查异常: {e}")

def main():
    """主函数"""
    print("🔧 验证MT5修复结果")
    print("=" * 80)
    
    print("📋 验证目标:")
    print("1. 手动下单功能是否正常")
    print("2. 订单是否能正确发送到MT5")
    print("3. 持仓显示是否一致")
    print("4. AI推理交易集成是否正常")
    print()
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 测试手动下单
    manual_trade_success, order_id = test_manual_trading_after_fix(session)
    
    # 3. 测试持仓显示一致性
    position_consistency = test_position_display_consistency(session)
    
    # 4. 测试AI推理交易集成
    test_ai_inference_trading_integration(session)
    
    # 5. 总结验证结果
    print(f"\n📊 验证结果总结")
    print("=" * 80)
    
    print(f"手动下单功能: {'✅ 正常' if manual_trade_success else '❌ 异常'}")
    print(f"持仓显示一致性: {'✅ 一致' if position_consistency else '❌ 不一致'}")
    
    if manual_trade_success and position_consistency:
        print(f"\n🎉 MT5修复验证完全成功!")
        print("💡 现在可以正常使用:")
        print("   • 手动下单功能")
        print("   • AI推理交易")
        print("   • 持仓显示和管理")
        
        if order_id:
            print(f"\n⚠️ 注意: 测试订单 {order_id} 仍在持仓中，建议手动平仓")
    else:
        print(f"\n⚠️ 部分功能仍有问题")
        if not manual_trade_success:
            print("   • 手动下单功能需要进一步检查")
        if not position_consistency:
            print("   • 持仓显示不一致，可能需要重启应用")
    
    return 0

if __name__ == "__main__":
    main()
