#!/usr/bin/env python3
"""
检查深度学习训练状态
"""

import sqlite3
from datetime import datetime, timedelta
import os

def check_training_status():
    """检查训练状态"""
    
    print("🔍 检查深度学习训练状态")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 1. 检查正在运行的训练任务
        print("📋 正在运行的训练任务:")
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   created_at, updated_at, logs
            FROM training_tasks
            WHERE status IN ('running', 'pending', 'data_preparation', 'data_ready')
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        running_tasks = cursor.fetchall()
        if running_tasks:
            for task in running_tasks:
                task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs = task

                print(f"   任务ID: {task_id}")
                print(f"   模型ID: {model_id}")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   轮次: {current_epoch}/{total_epochs}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                
                # 检查是否卡住（超过10分钟没更新）
                if updated_at:
                    try:
                        last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                        now = datetime.now()
                        time_diff = now - last_update
                        
                        if time_diff > timedelta(minutes=10):
                            print(f"   ⚠️ 警告: 任务可能卡住 (已 {time_diff} 没有更新)")
                        else:
                            print(f"   ✅ 任务正常 (最后更新: {time_diff} 前)")
                    except:
                        print(f"   ❓ 无法解析更新时间")
                
                if logs:
                    print(f"   最新日志: {logs[-200:]}")
                
                print("-" * 40)
        else:
            print("   没有正在运行的训练任务")
        
        # 2. 检查最近完成或失败的任务
        print(f"\n📊 最近完成的任务:")
        cursor.execute('''
            SELECT id, name, status, progress, updated_at, logs
            FROM training_tasks 
            WHERE status IN ('completed', 'failed', 'stopped') 
            ORDER BY updated_at DESC 
            LIMIT 5
        ''')
        
        recent_tasks = cursor.fetchall()
        if recent_tasks:
            for task in recent_tasks:
                task_id, name, status, progress, updated_at, logs = task
                status_icon = "✅" if status == "completed" else "❌" if status == "failed" else "⏹️"
                print(f"   {status_icon} 任务ID: {task_id} | {name} | {status} | {progress}% | {updated_at}")
        else:
            print("   没有最近完成的任务")
        
        # 3. 检查训练进程是否还在运行
        print(f"\n🔄 检查训练进程:")
        
        # 检查是否有训练相关的Python进程
        import psutil
        training_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] == 'python.exe' or proc.info['name'] == 'python':
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'deep_learning' in cmdline.lower() or 'training' in cmdline.lower():
                        training_processes.append({
                            'pid': proc.info['pid'],
                            'cmdline': cmdline
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if training_processes:
            print("   发现训练相关进程:")
            for proc in training_processes:
                print(f"     PID: {proc['pid']} | 命令: {proc['cmdline'][:100]}...")
        else:
            print("   没有发现训练相关进程")
        
        # 4. 检查模型文件
        print(f"\n📁 检查模型文件:")
        models_dir = "models"
        if os.path.exists(models_dir):
            model_files = [f for f in os.listdir(models_dir) if f.endswith('.pth')]
            print(f"   发现 {len(model_files)} 个模型文件:")
            for model_file in model_files[-5:]:  # 显示最新的5个
                file_path = os.path.join(models_dir, model_file)
                file_size = os.path.getsize(file_path)
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                print(f"     {model_file} | {file_size/1024/1024:.1f}MB | {file_time}")
        else:
            print("   models目录不存在")
        
        conn.close()
        
        return running_tasks
        
    except Exception as e:
        print(f"❌ 检查训练状态失败: {e}")
        return []

def diagnose_stuck_training(running_tasks):
    """诊断卡住的训练"""
    
    if not running_tasks:
        print("\n✅ 没有卡住的训练任务")
        return
    
    print(f"\n🔧 诊断卡住的训练任务")
    print("=" * 60)
    
    for task in running_tasks:
        task_id, name, symbol, timeframe, status, progress, current_epoch, total_epochs, created_at, updated_at, logs = task
        
        print(f"📋 诊断任务 {task_id}: {name}")
        
        # 检查更新时间
        if updated_at:
            try:
                last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                now = datetime.now()
                time_diff = now - last_update
                
                if time_diff > timedelta(minutes=10):
                    print(f"   ⚠️ 任务卡住: 已 {time_diff} 没有更新")
                    
                    # 建议解决方案
                    print(f"   🔧 建议解决方案:")
                    print(f"     1. 重启训练任务")
                    print(f"     2. 检查GPU内存使用情况")
                    print(f"     3. 检查数据加载是否正常")
                    print(f"     4. 查看详细错误日志")
                    
                    # 提供修复命令
                    print(f"   💡 修复命令:")
                    print(f"     python -c \"")
                    print(f"import sqlite3")
                    print(f"conn = sqlite3.connect('trading_system.db')")
                    print(f"cursor = conn.cursor()")
                    print(f"cursor.execute('UPDATE training_tasks SET status = \\\"failed\\\", logs = \\\"任务卡住，已重置\\\" WHERE id = {task_id}')")
                    print(f"conn.commit()")
                    print(f"conn.close()")
                    print(f"print('任务 {task_id} 已重置')")
                    print(f"     \"")
                    
            except Exception as e:
                print(f"   ❌ 无法解析时间: {e}")
        
        # 检查日志中的错误
        if logs:
            error_keywords = ['error', 'exception', 'failed', 'cuda', 'memory', 'timeout']
            log_lower = logs.lower()
            
            found_errors = [kw for kw in error_keywords if kw in log_lower]
            if found_errors:
                print(f"   ⚠️ 日志中发现错误关键词: {found_errors}")
                print(f"   📝 相关日志: {logs[-300:]}")

def main():
    """主函数"""
    
    print("🔍 深度学习训练状态诊断工具")
    print("=" * 80)
    
    # 检查训练状态
    running_tasks = check_training_status()
    
    # 诊断卡住的训练
    diagnose_stuck_training(running_tasks)
    
    print(f"\n🎯 总结")
    print("=" * 80)
    
    if running_tasks:
        print(f"📊 发现 {len(running_tasks)} 个正在运行的训练任务")
        
        # 检查是否有卡住的任务
        stuck_count = 0
        for task in running_tasks:
            updated_at = task[9]
            if updated_at:
                try:
                    last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    now = datetime.now()
                    time_diff = now - last_update
                    
                    if time_diff > timedelta(minutes=10):
                        stuck_count += 1
                except:
                    pass
        
        if stuck_count > 0:
            print(f"⚠️ 其中 {stuck_count} 个任务可能卡住")
            print(f"💡 建议:")
            print(f"   1. 重启卡住的训练任务")
            print(f"   2. 检查系统资源使用情况")
            print(f"   3. 查看服务器日志获取详细错误信息")
            print(f"   4. 考虑降低批次大小或模型复杂度")
        else:
            print(f"✅ 所有任务运行正常")
    else:
        print(f"✅ 当前没有正在运行的训练任务")
    
    print(f"\n🔧 如需重启训练:")
    print(f"   1. 访问深度学习训练页面")
    print(f"   2. 停止卡住的任务")
    print(f"   3. 重新开始训练")
    print(f"   4. 监控训练进度")

if __name__ == '__main__':
    main()
