#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的完整训练流程
"""

import requests
import json
import time
from datetime import datetime

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    try:
        # 登录
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_data_preparation(session):
    """测试数据准备流程"""
    print("\n🔍 测试数据准备流程")
    print("=" * 40)
    
    # 数据准备配置
    config = {
        'model_name': 'TEST-数据准备流程测试',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 5,  # 少量轮次用于测试
        'batch_size': 32,
        'sequence_length': 60,
        'data_config': {
            'days': 30,  # 少量数据用于测试
            'validation_split': 0.2
        }
    }
    
    try:
        print("🚀 启动数据准备...")
        
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 数据准备启动成功!")
                print(f"   任务ID: {task_id}")
                return task_id
            else:
                print(f"❌ 数据准备启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 数据准备启动异常: {e}")
        return None

def monitor_data_preparation(session, task_id, max_wait=300):
    """监控数据准备进度"""
    print(f"\n📊 监控数据准备进度 (任务: {task_id[:8]}...)")
    print("=" * 50)
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress = result['progress']
                    status = progress['status']
                    progress_pct = progress['progress']
                    logs = progress.get('logs')
                    
                    # 解析日志获取阶段信息
                    stage = 'unknown'
                    message = ''
                    
                    if logs:
                        try:
                            log_data = json.loads(logs)
                            stage = log_data.get('stage', 'unknown')
                            message = log_data.get('message', '')
                        except:
                            pass
                    
                    print(f"📊 [{datetime.now().strftime('%H:%M:%S')}] 状态: {status} | 进度: {progress_pct}% | 阶段: {stage}")
                    if message:
                        print(f"    消息: {message}")
                    
                    # 检查是否完成数据准备
                    if status == 'data_ready':
                        print("✅ 数据准备完成！")
                        return True
                    elif status == 'failed':
                        print("❌ 数据准备失败")
                        return False
                    elif status == 'running' and stage == 'model_training':
                        print("🚀 数据准备完成，模型训练已自动启动")
                        return True
                else:
                    print(f"❌ 获取进度失败: {result.get('error')}")
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 监控异常: {e}")
        
        time.sleep(5)  # 5秒检查一次
    
    print("⏰ 监控超时")
    return False

def test_model_training_start(session, task_id):
    """测试模型训练启动"""
    print(f"\n🚀 测试模型训练启动 (任务: {task_id[:8]}...)")
    print("=" * 50)
    
    try:
        response = session.post(f'http://127.0.0.1:5000/api/deep-learning/start-model-training/{task_id}')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ 模型训练启动成功!")
                print(f"   消息: {result.get('message')}")
                return True
            else:
                print(f"❌ 模型训练启动失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 模型训练启动异常: {e}")
        return False

def test_status_check_logic(session, task_id):
    """测试状态检查逻辑"""
    print(f"\n🔍 测试状态检查逻辑 (任务: {task_id[:8]}...)")
    print("=" * 50)
    
    try:
        response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                progress = result['progress']
                status = progress['status']
                logs = progress.get('logs')
                
                print(f"📊 当前状态: {status}")
                
                if logs:
                    try:
                        log_data = json.loads(logs)
                        stage = log_data.get('stage')
                        message = log_data.get('message')
                        
                        print(f"📋 日志阶段: {stage}")
                        print(f"📝 日志消息: {message}")
                        
                        # 模拟前端状态检查逻辑
                        if status == 'data_ready':
                            print("✅ 前端判断: 可以开始训练")
                        elif status == 'running':
                            if stage == 'model_training':
                                print("✅ 前端判断: 模型训练进行中")
                            elif stage == 'data_preparation':
                                print("✅ 前端判断: 数据准备进行中")
                            elif stage == 'data_ready':
                                print("⚠️ 前端判断: 状态同步中")
                            else:
                                print("❓ 前端判断: 未知阶段")
                        else:
                            print(f"❓ 前端判断: 未知状态 {status}")
                        
                        return True
                        
                    except json.JSONDecodeError:
                        print("❌ 日志解析失败")
                        return False
                else:
                    print("⚠️ 无日志信息")
                    return False
            else:
                print(f"❌ 获取状态失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 状态检查异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 模型训练流程完整测试")
    print("=" * 60)
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 测试数据准备
    task_id = test_data_preparation(session)
    if not task_id:
        return
    
    # 3. 监控数据准备进度
    data_prep_success = monitor_data_preparation(session, task_id)
    if not data_prep_success:
        print("❌ 数据准备未成功完成")
        return
    
    # 4. 测试状态检查逻辑
    status_check_success = test_status_check_logic(session, task_id)
    if not status_check_success:
        print("❌ 状态检查逻辑测试失败")
        return
    
    # 5. 如果数据准备完成但未自动启动训练，手动启动
    print("\n🔍 检查是否需要手动启动训练...")
    response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            progress = result['progress']
            if progress['status'] == 'data_ready':
                print("📋 数据准备完成，测试手动启动训练...")
                training_start_success = test_model_training_start(session, task_id)
                if training_start_success:
                    print("✅ 手动启动训练成功")
                else:
                    print("❌ 手动启动训练失败")
            else:
                print("📋 训练已自动启动或正在进行中")
    
    print("\n🎉 测试完成！")
    print("=" * 60)
    print("📋 测试总结:")
    print("   ✅ 数据准备流程正常")
    print("   ✅ 状态检查逻辑正常")
    print("   ✅ 前端状态判断逻辑正常")

if __name__ == "__main__":
    main()
