<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MateTrade4 AI自动交易系统{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet"></script>

    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
        }

        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }

        .profit-positive {
            color: #28a745;
        }

        .profit-negative {
            color: #dc3545;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-chart-line"></i>
                            MateTrade4
                        </h4>
                        <small class="text-white-50">AI自动交易系统</small>
                    </div>

                    <ul class="nav flex-column">
                        <!-- 用户信息显示 -->
                        <li class="nav-item mb-3">
                            <div class="text-center">
                                <div class="text-white">
                                    <i class="fas fa-user-circle fa-2x"></i>
                                </div>
                                <div class="text-white mt-2">
                                    <strong>{{ current_user.username }}</strong>
                                    {% if current_user.user_type == 'admin' %}
                                        <span class="badge bg-danger ms-1">管理员</span>
                                    {% elif current_user.user_type == 'vip' %}
                                        <span class="badge bg-warning ms-1">VIP</span>
                                    {% else %}
                                        <span class="badge bg-secondary ms-1">普通用户</span>
                                    {% endif %}
                                </div>
                            </div>
                        </li>

                        <!-- 首页仪表盘 - 所有用户都可访问 -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}"
                               href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                首页仪表盘
                            </a>
                        </li>

                        <!-- 交易管理 - 所有用户都可访问 -->
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>交易管理</span>
                            </h6>
                        </li>


                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'risk_events' %}active{% endif %}"
                               href="{{ url_for('risk_events') }}">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                风险事件
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'pattern_monitoring' %}active{% endif %}"
                               href="{{ url_for('pattern_monitoring') }}">
                                <i class="fas fa-chart-line text-info"></i>
                                形态监测
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'low_risk_trading' %}active{% endif %}"
                               href="{{ url_for('low_risk_trading') }}">
                                <i class="fas fa-shield-alt text-success"></i>
                                低风险交易
                                <span class="badge bg-success ms-1">SAFE</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'strategy_trading' %}active{% endif %}"
                               href="{{ url_for('strategy_trading') }}">
                                <i class="fas fa-robot text-primary"></i>
                                策略交易
                                <span class="badge bg-primary ms-1">AI</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'ai_strategy_backtest' %}active{% endif %}"
                               href="{{ url_for('ai_strategy_backtest') }}">
                                <i class="fas fa-chart-line text-info"></i>
                                策略回测
                                <span class="badge bg-info ms-1">验证</span>
                            </a>
                        </li>

                        <!-- AI推理交易 - 从深度学习模块移动到交易管理 -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'model_inference' %}active{% endif %}"
                               href="{{ url_for('model_inference') }}">
                                <i class="fas fa-magic text-warning"></i>
                                AI推理交易
                                <span class="badge bg-warning ms-1">实盘</span>
                            </a>
                        </li>

                        <!-- AI推理回测 - 历史数据验证 -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'model_backtest' %}active{% endif %}"
                               href="{{ url_for('model_backtest') }}">
                                <i class="fas fa-chart-line text-success"></i>
                                AI推理回测
                                <span class="badge bg-success ms-1">回测</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'mt5_connection' %}active{% endif %}"
                               href="{{ url_for('mt5_connection') }}">
                                <i class="fas fa-plug"></i>
                                MT5连接
                            </a>
                        </li>


                        <!-- 分析工具 - 仅VIP和管理员可见 -->
                        {% if current_user.user_type in ['vip', 'admin'] %}
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>分析工具</span>
                                <span class="badge bg-warning">VIP</span>
                            </h6>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'ai_analysis_process' %}active{% endif %}"
                               href="{{ url_for('ai_analysis_process') }}">
                                <i class="fas fa-brain"></i>
                                AI策略分析过程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'ai_training' %}active{% endif %}"
                               href="{{ url_for('ai_training') }}">
                                <i class="fas fa-graduation-cap"></i>
                                训练AI策略
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'charts' %}active{% endif %}"
                               href="{{ url_for('charts') }}">
                                <i class="fas fa-chart-candlestick"></i>
                                专业图表
                            </a>
                        </li>

                        <!-- 深度学习模块 -->
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>深度学习</span>
                                <span class="badge bg-info">GPU</span>
                            </h6>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'deep_learning_dashboard' %}active{% endif %}"
                               href="{{ url_for('deep_learning_dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                深度学习仪表板
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'model_training' %}active{% endif %}"
                               href="{{ url_for('model_training') }}">
                                <i class="fas fa-dumbbell"></i>
                                模型训练
                            </a>
                        </li>

                        <!-- 训练修复工具 -->
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showTrainingFixModal()">
                                <i class="fas fa-wrench text-warning"></i>
                                训练修复工具
                                <span class="badge bg-warning ms-1">修复</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'model_management' %}active{% endif %}"
                               href="{{ url_for('model_management') }}">
                                <i class="fas fa-database"></i>
                                模型管理
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'gpu_monitor' %}active{% endif %}"
                               href="{{ url_for('gpu_monitor') }}">
                                <i class="fas fa-microchip"></i>
                                GPU监控
                            </a>
                        </li>
                        {% endif %}
                        {% if current_user.user_type in ['vip', 'admin'] %}

                        {% endif %}

                        <!-- 用户管理和系统设置 - 仅管理员可见 -->
                        {% if current_user.user_type == 'admin' %}
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>用户管理</span>
                                <span class="badge bg-danger">管理员</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'user_management' %}active{% endif %}"
                               href="{{ url_for('user_management') }}">
                                <i class="fas fa-users"></i>
                                用户管理
                            </a>
                        </li>

                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>系统管理</span>
                                <span class="badge bg-danger">管理员</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'settings' %}active{% endif %}"
                               href="{{ url_for('settings') }}">
                                <i class="fas fa-cog"></i>
                                系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'ai_strategy_management' %}active{% endif %}"
                               href="{{ url_for('ai_strategy_management') }}">
                                <i class="fas fa-share-alt"></i>
                                AI策略分享管理
                            </a>
                        </li>
                        {% endif %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}仪表盘{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <span class="navbar-text">
                                欢迎, {{ current_user.username }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Flash消息 -->
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- 训练修复模态框 -->
    <div class="modal fade" id="trainingFixModal" tabindex="-1" aria-labelledby="trainingFixModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="trainingFixModalLabel">
                        <i class="fas fa-wrench me-2"></i>训练修复工具
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- 训练状态检查 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-search me-2"></i>训练状态检查</h6>
                        </div>
                        <div class="card-body">
                            <div id="trainingStatusCheck">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">检查中...</span>
                                    </div>
                                    <p class="mt-2">正在检查训练状态...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 修复选项 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-tools me-2"></i>修复选项</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <button class="btn btn-warning w-100 mb-2" onclick="fix25PercentStuck()">
                                        <i class="fas fa-bolt me-2"></i>修复25%卡住
                                    </button>
                                    <small class="text-muted">强制推进卡在25%的训练任务</small>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-info w-100 mb-2" onclick="forceProgressUpdate()">
                                        <i class="fas fa-sync me-2"></i>强制进度更新
                                    </button>
                                    <small class="text-muted">强制更新训练进度和时间戳</small>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <button class="btn btn-danger w-100 mb-2" onclick="stopStuckTraining()">
                                        <i class="fas fa-stop me-2"></i>停止卡住任务
                                    </button>
                                    <small class="text-muted">停止长时间无响应的训练任务</small>
                                </div>
                                <div class="col-md-6">
                                    <button class="btn btn-success w-100 mb-2" onclick="restartTrainingService()">
                                        <i class="fas fa-restart me-2"></i>重启训练服务
                                    </button>
                                    <small class="text-muted">重启训练服务清理状态</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 修复日志 -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-list me-2"></i>修复日志</h6>
                        </div>
                        <div class="card-body">
                            <div id="fixLog" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                                <p class="text-muted mb-0">等待修复操作...</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="refreshTrainingStatus()">
                        <i class="fas fa-refresh me-2"></i>刷新状态
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 全局状态管理器 -->
    <script src="{{ url_for('static', filename='js/global_state_manager.js') }}"></script>

    <!-- 训练修复工具JavaScript -->
    <script>
        // 显示训练修复模态框
        function showTrainingFixModal() {
            const modal = new bootstrap.Modal(document.getElementById('trainingFixModal'));
            modal.show();

            // 自动检查训练状态
            setTimeout(() => {
                checkTrainingStatus();
            }, 500);
        }

        // 检查训练状态
        function checkTrainingStatus() {
            addFixLog('🔍 开始检查训练状态...');

            fetch('/api/training-fix/check-status')
                .then(response => response.json())
                .then(data => {
                    console.log('API返回数据:', data); // 调试日志

                    if (data.success && data.tasks) {
                        displayTrainingStatus(data.tasks);
                        addFixLog(`✅ 成功获取 ${data.count} 个训练任务状态`);
                    } else {
                        addFixLog('❌ API返回格式错误: ' + (data.error || '未知错误'));
                        document.getElementById('trainingStatusCheck').innerHTML =
                            '<div class="alert alert-warning">获取训练状态失败: ' + (data.error || '未知错误') + '</div>';
                    }
                })
                .catch(error => {
                    console.error('检查训练状态失败:', error);
                    addFixLog('❌ 检查训练状态失败: ' + error.message);
                    document.getElementById('trainingStatusCheck').innerHTML =
                        '<div class="alert alert-danger">检查训练状态失败: ' + error.message + '</div>';
                });
        }

        // 显示训练状态
        function displayTrainingStatus(tasks) {
            const statusDiv = document.getElementById('trainingStatusCheck');

            if (!tasks || tasks.length === 0) {
                statusDiv.innerHTML = '<div class="alert alert-info">✅ 没有运行中的训练任务</div>';
                addFixLog('✅ 没有发现运行中的训练任务');
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>任务名称</th><th>进度</th><th>轮次</th><th>状态</th><th>更新间隔</th></tr></thead><tbody>';

            let stuckTasks = 0;

            tasks.forEach(task => {
                const updateInterval = task.update_interval || 0;

                let statusClass = 'success';
                let statusText = '正常';

                if (task.status === 'running') {
                    if (updateInterval > 300) { // 5分钟
                        statusClass = 'danger';
                        statusText = '严重卡住';
                        stuckTasks++;
                    } else if (updateInterval > 120) { // 2分钟
                        statusClass = 'warning';
                        statusText = '可能卡住';
                        stuckTasks++;
                    } else if (task.progress >= 20 && task.progress <= 30) {
                        statusClass = 'warning';
                        statusText = '25%阶段';
                    }
                }

                const taskName = task.name || task.model_name || `任务_${task.id.substring(0, 8)}`;
                const intervalText = updateInterval < 60 ?
                    `${Math.floor(updateInterval)}秒前` :
                    `${Math.floor(updateInterval / 60)}分钟前`;

                html += `<tr>
                    <td>${taskName}</td>
                    <td>${task.progress || 0}%</td>
                    <td>${task.current_epoch || 0}/${task.total_epochs || 0}</td>
                    <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                    <td>${intervalText}</td>
                </tr>`;
            });

            html += '</tbody></table></div>';

            if (stuckTasks > 0) {
                html = `<div class="alert alert-warning">🔴 发现 ${stuckTasks} 个可能卡住的任务</div>` + html;
                addFixLog(`🔴 发现 ${stuckTasks} 个可能卡住的训练任务`);
            } else {
                html = '<div class="alert alert-success">✅ 所有训练任务运行正常</div>' + html;
                addFixLog('✅ 所有训练任务运行正常');
            }

            statusDiv.innerHTML = html;
        }

        // 修复25%卡住
        function fix25PercentStuck() {
            addFixLog('🔧 开始修复25%卡住问题...');

            fetch('/api/training-fix/fix-25-percent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addFixLog('✅ 25%卡住修复成功: ' + data.message);
                    setTimeout(() => {
                        refreshTrainingStatus();
                    }, 2000);
                } else {
                    addFixLog('❌ 25%卡住修复失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('修复失败:', error);
                addFixLog('❌ 修复请求失败: ' + error.message);
            });
        }

        // 强制进度更新
        function forceProgressUpdate() {
            addFixLog('🔧 开始强制进度更新...');

            fetch('/api/training-fix/force-progress-update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addFixLog('✅ 强制进度更新成功: ' + data.message);
                    setTimeout(() => {
                        refreshTrainingStatus();
                    }, 2000);
                } else {
                    addFixLog('❌ 强制进度更新失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('更新失败:', error);
                addFixLog('❌ 更新请求失败: ' + error.message);
            });
        }

        // 停止卡住任务
        function stopStuckTraining() {
            if (!confirm('确定要停止所有卡住的训练任务吗？')) {
                return;
            }

            addFixLog('🔧 开始停止卡住的训练任务...');

            fetch('/api/training-fix/stop-stuck-training', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addFixLog('✅ 卡住任务停止成功: ' + data.message);
                    setTimeout(() => {
                        refreshTrainingStatus();
                    }, 2000);
                } else {
                    addFixLog('❌ 停止任务失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('停止失败:', error);
                addFixLog('❌ 停止请求失败: ' + error.message);
            });
        }

        // 重启训练服务
        function restartTrainingService() {
            if (!confirm('确定要重启训练服务吗？这将清理所有训练状态。')) {
                return;
            }

            addFixLog('🔧 开始重启训练服务...');
            addFixLog('⚠️ 注意: 这个功能需要手动重启Flask应用');
            addFixLog('💡 建议: 停止Flask应用并重新运行 python app.py');
        }

        // 刷新训练状态
        function refreshTrainingStatus() {
            addFixLog('🔄 刷新训练状态...');
            checkTrainingStatus();
        }

        // 添加修复日志
        function addFixLog(message) {
            const logDiv = document.getElementById('fixLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `<div class="mb-1"><small class="text-muted">[${timestamp}]</small> ${message}</div>`;

            if (logDiv.innerHTML.includes('等待修复操作')) {
                logDiv.innerHTML = logEntry;
            } else {
                logDiv.innerHTML += logEntry;
            }

            // 滚动到底部
            logDiv.scrollTop = logDiv.scrollHeight;
        }
    </script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/common.js') }}"></script>
    <!-- AI交易全局管理器 -->
    <script src="{{ url_for('static', filename='js/ai_trading_manager.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
