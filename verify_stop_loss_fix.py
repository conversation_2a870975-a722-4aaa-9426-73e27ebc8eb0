#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证止损止盈修改是否成功
检查HTML模板中的所有相关默认值
"""

import re

def verify_html_changes():
    """验证HTML模板中的修改"""
    print("🔍 验证HTML模板中的止损止盈修改")
    print("=" * 80)
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查主要的止损止盈字段
        checks = [
            {
                'name': 'AI交易配置 - 止损',
                'pattern': r'id="stopLossPips"[^>]*value="(\d+)"',
                'expected': '1000'
            },
            {
                'name': 'AI交易配置 - 止盈', 
                'pattern': r'id="takeProfitPips"[^>]*value="(\d+)"',
                'expected': '2000'
            },
            {
                'name': 'AI推理配置 - 止损',
                'pattern': r'id="inferenceStopLoss"[^>]*value="(\d+)"',
                'expected': '1000'
            },
            {
                'name': 'AI推理配置 - 止盈',
                'pattern': r'id="inferenceTakeProfit"[^>]*value="(\d+)"',
                'expected': '2000'
            },
            {
                'name': '回测配置 - 止损',
                'pattern': r'id="backtestStopLoss"[^>]*value="(\d+)"',
                'expected': '1000'
            },
            {
                'name': '回测配置 - 止盈',
                'pattern': r'id="backtestTakeProfit"[^>]*value="(\d+)"',
                'expected': '2000'
            },
            {
                'name': '移动止损距离',
                'pattern': r'id="inferenceTrailingStopDistance"[^>]*value="(\d+)"',
                'expected': '80'
            }
        ]
        
        print("📊 HTML字段检查结果:")
        all_passed = True
        
        for check in checks:
            match = re.search(check['pattern'], content)
            if match:
                actual_value = match.group(1)
                status = "✅" if actual_value == check['expected'] else "❌"
                print(f"{status} {check['name']}: {actual_value} (期望: {check['expected']})")
                if actual_value != check['expected']:
                    all_passed = False
            else:
                print(f"❌ {check['name']}: 未找到匹配")
                all_passed = False
        
        # 检查JavaScript预设配置
        print(f"\n📊 JavaScript预设配置检查:")

        js_checks = [
            {
                'name': '保守型AI交易止损',
                'pattern': r"case 'conservative':.*?stopLossPips.*?value = (\d+);.*?黄金保守型",
                'expected': '800'
            },
            {
                'name': '保守型AI交易止盈',
                'pattern': r"case 'conservative':.*?takeProfitPips.*?value = (\d+);.*?黄金保守型",
                'expected': '1600'
            },
            {
                'name': '平衡型AI交易止损',
                'pattern': r"case 'balanced':.*?stopLossPips.*?value = (\d+);.*?黄金推荐",
                'expected': '1000'
            },
            {
                'name': '平衡型AI交易止盈',
                'pattern': r"case 'balanced':.*?takeProfitPips.*?value = (\d+);.*?黄金推荐",
                'expected': '2000'
            },
            {
                'name': '激进型AI交易止损',
                'pattern': r"case 'aggressive':.*?stopLossPips.*?value = (\d+);.*?黄金激进型",
                'expected': '1500'
            },
            {
                'name': '激进型AI交易止盈',
                'pattern': r"case 'aggressive':.*?takeProfitPips.*?value = (\d+);.*?黄金激进型",
                'expected': '3000'
            }
        ]
        
        for check in js_checks:
            match = re.search(check['pattern'], content, re.DOTALL)
            if match:
                actual_value = match.group(1)
                status = "✅" if actual_value == check['expected'] else "❌"
                print(f"{status} {check['name']}: {actual_value} (期望: {check['expected']})")
                if actual_value != check['expected']:
                    all_passed = False
            else:
                print(f"❌ {check['name']}: 未找到匹配")
                all_passed = False
        
        # 检查最大值限制
        print(f"\n📊 字段限制检查:")
        
        limit_checks = [
            {
                'name': 'stopLossPips最大值',
                'pattern': r'id="stopLossPips"[^>]*max="(\d+)"',
                'expected': '3000'
            },
            {
                'name': 'takeProfitPips最大值',
                'pattern': r'id="takeProfitPips"[^>]*max="(\d+)"',
                'expected': '5000'
            }
        ]
        
        for check in limit_checks:
            match = re.search(check['pattern'], content)
            if match:
                actual_value = match.group(1)
                status = "✅" if actual_value == check['expected'] else "❌"
                print(f"{status} {check['name']}: {actual_value} (期望: {check['expected']})")
                if actual_value != check['expected']:
                    all_passed = False
            else:
                print(f"❌ {check['name']}: 未找到匹配")
                all_passed = False
        
        print(f"\n📋 总体结果:")
        if all_passed:
            print("🎉 所有修改都已正确应用！")
            print("✅ HTML默认值已更新为黄金适配配置")
            print("✅ JavaScript默认值已更新")
            print("✅ 字段限制已放宽")
        else:
            print("⚠️ 部分修改可能未完全生效")
            print("请检查上面标记为❌的项目")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def provide_next_steps():
    """提供下一步操作建议"""
    print(f"\n💡 下一步操作:")
    print("1. 重启应用程序 (让HTML模板修改生效)")
    print("2. 刷新AI推理交易页面")
    print("3. 检查页面上的配置是否显示为:")
    print("   • 止损点数: 1000 pips")
    print("   • 止盈点数: 2000 pips")
    print("4. 测试手动下单功能")
    print("5. 验证MT5中的实际止损距离")
    
    print(f"\n🎯 预期效果:")
    print("• 页面刷新后默认显示1000/2000 pips")
    print("• 手动下单止损约10美元 (而不是0.5美元)")
    print("• 所有配置区域都使用黄金适配的默认值")
    
    print(f"\n⚠️ 重要提醒:")
    print("• 必须重启应用程序才能看到HTML模板的修改")
    print("• 如果仍显示50/100，说明浏览器缓存了旧页面")
    print("• 可以尝试强制刷新 (Ctrl+F5) 或清除缓存")

def main():
    """主函数"""
    print("🔧 验证止损止盈配置修复")
    print("=" * 80)
    
    print("📋 修改内容总结:")
    print("• HTML模板默认值: 50/100 → 1000/2000 pips")
    print("• JavaScript默认值: 50/100 → 1000/2000 pips") 
    print("• 字段最大值限制: 500/1000 → 3000/5000 pips")
    print("• 移动止损距离: 50 → 80 pips")
    print("• 所有预设配置都已更新为黄金适配")
    
    # 验证修改
    success = verify_html_changes()
    
    # 提供下一步建议
    provide_next_steps()
    
    if success:
        print(f"\n🎉 修复完成！")
        print("现在重启应用程序，页面将显示适合黄金交易的默认配置")
    else:
        print(f"\n⚠️ 请检查修改是否完全成功")
    
    return 0

if __name__ == "__main__":
    main()
