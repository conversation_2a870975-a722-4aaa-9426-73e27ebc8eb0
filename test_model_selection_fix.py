#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型选择修复结果
"""

import requests
import time

def test_model_selection_fix():
    """测试模型选择修复结果"""
    print("🧪 测试AI推理交易模型选择修复结果")
    print("=" * 50)
    
    # 等待应用启动
    time.sleep(5)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 获取AI推理交易页面
        print(f"\n🔍 获取AI推理交易页面...")
        inference_response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if inference_response.status_code != 200:
            print(f"❌ 页面访问失败: {inference_response.status_code}")
            return False
        
        content = inference_response.text
        print(f"✅ 页面访问成功，大小: {len(content):,} 字符")
        
        # 保存页面内容用于分析
        with open('fixed_model_selection_page.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("💾 页面内容已保存到: fixed_model_selection_page.html")
        
        # 检查修复内容
        print(f"\n🔍 检查修复内容:")
        
        # 1. 检查改进的loadTradingModels函数
        if '🔄 开始加载交易模型...' in content:
            print("✅ 改进的loadTradingModels函数存在")
        else:
            print("❌ 改进的loadTradingModels函数缺失")
        
        # 2. 检查DOM元素检查逻辑
        if 'tradingModelSelect元素未找到' in content:
            print("✅ DOM元素检查逻辑已添加")
        else:
            print("❌ DOM元素检查逻辑缺失")
        
        # 3. 检查初始化顺序改进
        if '🔄 开始基础功能初始化...' in content:
            print("✅ 初始化顺序已改进")
        else:
            print("❌ 初始化顺序未改进")
        
        # 4. 检查与回测页面相同的格式
        if '${model.symbol}-${model.timeframe}' in content:
            print("✅ 使用与回测页面相同的模型显示格式")
        else:
            print("❌ 模型显示格式未统一")
        
        # 测试API端点
        print(f"\n🔍 测试模型列表API...")
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if models_response.status_code == 200:
            models_data = models_response.json()
            if models_data.get('success'):
                models = models_data.get('models', [])
                completed_models = [m for m in models if m.get('status') == 'completed']
                print(f"✅ 模型API正常: {len(completed_models)} 个已完成模型")
                
                if completed_models:
                    print("📋 可用模型:")
                    for i, model in enumerate(completed_models[:3]):
                        print(f"   {i+1}. {model.get('name')} ({model.get('symbol')}-{model.get('timeframe')})")
                else:
                    print("⚠️ 没有已完成的模型")
                    return False
            else:
                print(f"❌ 模型API错误: {models_data.get('error')}")
                return False
        else:
            print(f"❌ 模型API失败: {models_response.status_code}")
            return False
        
        # 对比AI推理回测页面
        print(f"\n🔍 对比AI推理回测页面...")
        backtest_response = session.get('http://127.0.0.1:5000/deep-learning/backtest')
        
        if backtest_response.status_code == 200:
            backtest_content = backtest_response.text
            print(f"✅ 回测页面访问成功，大小: {len(backtest_content):,} 字符")
            
            # 检查回测页面的模型选择实现
            if 'loadAvailableModels();' in backtest_content:
                print("✅ 回测页面使用loadAvailableModels函数")
            
            if 'id="modelSelect"' in backtest_content:
                print("✅ 回测页面有modelSelect元素")
            
            # 保存回测页面内容用于对比
            with open('backtest_page_for_comparison.html', 'w', encoding='utf-8') as f:
                f.write(backtest_content)
            
            print("💾 回测页面内容已保存到: backtest_page_for_comparison.html")
        else:
            print(f"❌ 回测页面访问失败: {backtest_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def create_browser_debug_script():
    """创建浏览器调试脚本"""
    print(f"\n🔧 创建浏览器调试脚本...")
    
    debug_script = '''
// AI推理交易模型选择调试脚本
console.log("🔧 开始调试AI推理交易模型选择...");

// 1. 检查DOM元素
const tradingModelSelect = document.getElementById('tradingModelSelect');
const tradingModelInfo = document.getElementById('tradingModelInfo');

console.log("📋 DOM元素检查:");
console.log("tradingModelSelect:", tradingModelSelect);
console.log("tradingModelInfo:", tradingModelInfo);

if (tradingModelSelect) {
    console.log("模型选择选项数量:", tradingModelSelect.options.length);
    console.log("当前选项:");
    for (let i = 0; i < tradingModelSelect.options.length; i++) {
        console.log(`  ${i}: ${tradingModelSelect.options[i].text}`);
    }
} else {
    console.log("❌ tradingModelSelect元素未找到");
}

// 2. 手动调用loadTradingModels函数
console.log("🔄 手动调用loadTradingModels...");
if (typeof loadTradingModels === 'function') {
    loadTradingModels().then(() => {
        console.log("✅ loadTradingModels调用完成");
        
        // 重新检查选项
        if (tradingModelSelect) {
            console.log("更新后的选项数量:", tradingModelSelect.options.length);
            for (let i = 0; i < tradingModelSelect.options.length; i++) {
                console.log(`  ${i}: ${tradingModelSelect.options[i].text}`);
            }
        }
    }).catch(error => {
        console.error("❌ loadTradingModels调用失败:", error);
    });
} else {
    console.log("❌ loadTradingModels函数不存在");
}

// 3. 检查API端点
console.log("🔍 测试模型API...");
fetch('/api/deep-learning/models')
    .then(response => response.json())
    .then(data => {
        console.log("模型API响应:", data);
        if (data.success) {
            const completed = data.models.filter(m => m.status === 'completed');
            console.log(`找到 ${completed.length} 个已完成的模型`);
            completed.forEach((model, i) => {
                console.log(`  ${i+1}. ${model.name} (${model.symbol}-${model.timeframe})`);
            });
        } else {
            console.error("模型API错误:", data.error);
        }
    })
    .catch(error => {
        console.error("模型API异常:", error);
    });

console.log("🎯 调试脚本执行完成！");
'''
    
    with open('model_selection_debug.js', 'w', encoding='utf-8') as f:
        f.write(debug_script)
    
    print("✅ 浏览器调试脚本已创建: model_selection_debug.js")
    print("📋 使用方法:")
    print("   1. 打开AI推理交易页面")
    print("   2. 按F12打开开发者工具")
    print("   3. 在Console标签中粘贴model_selection_debug.js的内容并执行")

if __name__ == "__main__":
    success = test_model_selection_fix()
    create_browser_debug_script()
    
    if success:
        print("\n🎉 模型选择修复测试完成！")
        print("📋 修复总结:")
        print("   1. ✅ 改进了loadTradingModels函数，添加详细日志")
        print("   2. ✅ 添加了DOM元素存在性检查")
        print("   3. ✅ 改进了初始化顺序，确保DOM准备完成")
        print("   4. ✅ 统一了与回测页面相同的模型显示格式")
        print("   5. ✅ 简化了loadAvailableModels函数")
        print("\n🔄 请刷新浏览器页面并使用调试脚本测试")
    else:
        print("\n❌ 模型选择修复测试失败")
        print("🔧 请检查上述错误信息")
