
// 持仓功能测试脚本
console.log("🧪 开始测试持仓功能...");

// 1. 检查变量初始化
console.log("📋 检查变量初始化:");
if (typeof lastPositionsData !== 'undefined') {
    console.log("✅ lastPositionsData变量已定义:", lastPositionsData);
} else {
    console.log("❌ lastPositionsData变量未定义");
}

// 2. 测试refreshPositions函数
console.log("📋 测试refreshPositions函数:");
if (typeof refreshPositions === 'function') {
    console.log("✅ refreshPositions函数存在");
    
    // 手动调用refreshPositions
    console.log("🔄 手动调用refreshPositions...");
    refreshPositions().then(() => {
        console.log("✅ refreshPositions调用成功");
    }).catch(error => {
        console.error("❌ refreshPositions调用失败:", error);
    });
} else {
    console.log("❌ refreshPositions函数不存在");
}

// 3. 检查持仓容器元素
console.log("📋 检查持仓容器元素:");
const container = document.getElementById('currentPositionsContainer');
if (container) {
    console.log("✅ 持仓容器元素存在");
    console.log("   当前内容:", container.innerHTML.substring(0, 100) + "...");
} else {
    console.log("❌ 持仓容器元素不存在");
}

// 4. 检查其他相关元素
const elements = [
    'positionsLoadingState',
    'positionsErrorState', 
    'noPositionsState'
];

elements.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ ${id}: 存在`);
    } else {
        console.log(`❌ ${id}: 不存在`);
    }
});

// 5. 测试MT5连接状态
console.log("📋 测试MT5连接状态:");
if (typeof checkMT5Connection === 'function') {
    console.log("🔄 检查MT5连接状态...");
    checkMT5Connection();
} else {
    console.log("❌ checkMT5Connection函数不存在");
}

// 6. 延迟检查结果
setTimeout(() => {
    console.log("🎯 测试结果总结:");
    
    // 检查是否还有错误
    const errorState = document.getElementById('positionsErrorState');
    if (errorState && errorState.style.display !== 'none') {
        console.log("⚠️ 持仓区域仍显示错误状态");
        console.log("   错误内容:", errorState.textContent);
    } else {
        console.log("✅ 持仓区域无错误状态");
    }
    
    // 检查当前显示内容
    const container = document.getElementById('currentPositionsContainer');
    if (container) {
        const content = container.textContent.trim();
        if (content.includes('网络连接异常')) {
            console.log("❌ 仍然显示网络连接异常");
        } else if (content.includes('当前无持仓')) {
            console.log("✅ 正常显示无持仓状态");
        } else if (content.includes('正在获取')) {
            console.log("🔄 正在加载持仓数据");
        } else {
            console.log("📋 持仓内容:", content.substring(0, 50) + "...");
        }
    }
    
    console.log("🎉 持仓功能测试完成！");
}, 3000);

console.log("⏳ 测试进行中，请等待3秒查看结果...");
