#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化特征处理 - 解决基础特征和增强特征的重复问题
"""

def create_optimized_feature_strategy():
    """创建优化的特征处理策略"""
    
    strategy = {
        'basic_features': {
            'description': '8个基础特征（必选项，保证兼容性）',
            'always_included': True,
            'features': {
                'price_features': ['open', 'high', 'low', 'close', 'volume'],  # 5个
                'technical_indicators': ['sma_5', 'sma_20', 'rsi']             # 3个
            },
            'total_count': 8,
            'note': '这些是系统的核心特征，确保与现有模型兼容'
        },
        
        'enhanced_features': {
            'description': '增强特征（可选项，与基础特征互补）',
            'strategies': {
                'recommended_complementary': {
                    'name': '推荐互补特征集',
                    'description': '与基础特征互补，避免重复',
                    'features': [
                        # 布林带特征（基础特征中没有）
                        'bb_percent_b', 'bb_band_width', 'bb_squeeze', 'bb_breakout',
                        'bb_distance_from_middle', 'bb_percent_b_change',
                        
                        # ATR特征（基础特征中没有）
                        'atr_atr', 'atr_ratio', 'atr_percentile', 'atr_change',
                        'atr_low_volatility', 'atr_high_volatility',
                        
                        # 随机指标（基础特征中没有）
                        'stoch_stoch_k', 'stoch_stoch_d', 'stoch_k_d_diff',
                        'stoch_overbought', 'stoch_oversold',
                        
                        # 组合信号（基础特征中没有）
                        'combined_squeeze_low_vol', 'combined_breakout_confirmed',
                        'combined_bullish_confluence', 'combined_bearish_confluence',
                        
                        # 市场状态（与基础SMA互补）
                        'market_trend_strength', 'market_trending_market', 'market_trend_direction',
                        
                        # 价格衍生特征（与基础OHLCV互补）
                        'close_returns', 'close_position', 'price_gap',
                        
                        # 动量特征（与基础SMA互补）
                        'momentum_5', 'momentum_10', 'momentum_20', 'trend_strength'
                    ],
                    'count': 31,
                    'total_with_basic': 39  # 8 + 31
                },
                
                'all_enhanced': {
                    'name': '全部增强特征',
                    'description': '所有52个增强特征，不包含基础特征',
                    'count': 52,
                    'note': '独立的增强特征集，需要重新训练模型'
                },
                
                'custom_selection': {
                    'name': '自定义特征选择',
                    'description': '用户手动选择特定的增强特征',
                    'note': '与基础特征组合使用'
                }
            }
        },
        
        'combination_modes': {
            'mode_1_basic_only': {
                'name': '仅基础特征模式',
                'description': '只使用8个基础特征',
                'config': {
                    'use_enhanced_features': False,
                    'features': {
                        'price': True,
                        'volume': True,
                        'technical': True,
                        'time': True
                    }
                },
                'feature_count': 8,
                'compatibility': '完全兼容现有模型',
                'use_case': '现有模型推理、快速训练'
            },
            
            'mode_2_basic_plus_enhanced': {
                'name': '基础+增强特征模式',
                'description': '8个基础特征 + 31个互补增强特征',
                'config': {
                    'use_enhanced_features': True,
                    'feature_selection_strategy': 'recommended_complementary',
                    'include_basic_features': True
                },
                'feature_count': 39,
                'compatibility': '需要重新训练',
                'use_case': '新模型训练、最佳性能'
            },
            
            'mode_3_enhanced_only': {
                'name': '仅增强特征模式',
                'description': '只使用52个增强特征',
                'config': {
                    'use_enhanced_features': True,
                    'feature_selection_strategy': 'all',
                    'include_basic_features': False
                },
                'feature_count': 52,
                'compatibility': '需要重新训练',
                'use_case': '高级用户、实验性训练'
            }
        }
    }
    
    return strategy

def generate_feature_handling_code():
    """生成优化的特征处理代码"""
    
    code_template = '''
def _calculate_features_optimized(self, price_data: np.ndarray, config: Dict[str, Any]) -> np.ndarray:
    """
    优化的特征计算函数 - 处理基础特征和增强特征的组合
    """
    try:
        logger.info("🔧 开始优化特征计算...")
        
        # 获取配置
        use_enhanced_features = config.get('use_enhanced_features', False)
        include_basic_features = config.get('include_basic_features', True)
        
        features_list = []
        
        # 1. 基础特征（8个必选项）
        if include_basic_features:
            logger.info("📊 计算基础特征（8个必选项）...")
            basic_features = self._calculate_basic_features_core(price_data)
            features_list.append(basic_features)
            logger.info(f"   基础特征形状: {basic_features.shape}")
        
        # 2. 增强特征（可选项）
        if use_enhanced_features:
            logger.info("🚀 计算增强特征...")
            
            feature_selection_strategy = config.get('feature_selection_strategy', 'recommended_complementary')
            
            if feature_selection_strategy == 'recommended_complementary':
                # 推荐互补特征集（与基础特征互补）
                enhanced_features = self._calculate_complementary_enhanced_features(price_data, config)
                logger.info(f"   互补增强特征形状: {enhanced_features.shape}")
                
            elif feature_selection_strategy == 'all':
                # 全部增强特征
                enhanced_features = self._calculate_all_enhanced_features(price_data, config)
                logger.info(f"   全部增强特征形状: {enhanced_features.shape}")
                
            elif feature_selection_strategy == 'custom':
                # 自定义特征选择
                enhanced_features = self._calculate_custom_enhanced_features(price_data, config)
                logger.info(f"   自定义增强特征形状: {enhanced_features.shape}")
            
            features_list.append(enhanced_features)
        
        # 3. 合并所有特征
        if features_list:
            final_features = np.concatenate(features_list, axis=1)
            logger.info(f"✅ 特征计算完成: 最终形状={final_features.shape}")
            
            # 特征组合说明
            feature_info = self._get_feature_combination_info(config)
            logger.info(f"📋 特征组合: {feature_info}")
            
            return final_features
        else:
            logger.warning("⚠️ 没有计算任何特征，使用默认")
            return price_data[:, 3:4]  # 默认返回收盘价
            
    except Exception as e:
        logger.error(f"❌ 优化特征计算失败: {e}")
        return price_data[:, 3:4]

def _calculate_basic_features_core(self, price_data: np.ndarray) -> np.ndarray:
    """
    计算核心基础特征（8个必选项）
    确保与现有模型完全兼容
    """
    try:
        # 提取OHLCV
        opens = price_data[:, 0]
        highs = price_data[:, 1]
        lows = price_data[:, 2]
        closes = price_data[:, 3]
        volumes = price_data[:, 4]
        
        # 1. 价格特征（5个）- 标准化
        price_features = np.column_stack([opens, highs, lows, closes, volumes])
        price_features_norm = (price_features - price_features.mean(axis=0)) / (price_features.std(axis=0) + 1e-10)
        
        # 2. 技术指标（3个）
        # SMA_5
        sma_5 = np.convolve(closes, np.ones(5)/5, mode='same')
        
        # SMA_20  
        sma_20 = np.convolve(closes, np.ones(20)/20, mode='same')
        
        # RSI（简化版，与现有模型一致）
        price_changes = np.diff(closes)
        gains = np.where(price_changes > 0, price_changes, 0)
        losses = np.where(price_changes < 0, -price_changes, 0)
        
        avg_gains = np.convolve(gains, np.ones(14)/14, mode='same')
        avg_losses = np.convolve(losses, np.ones(14)/14, mode='same')
        
        rs = avg_gains / (avg_losses + 1e-10)
        rsi = 100 - (100 / (1 + rs))
        rsi = np.concatenate([[50], rsi])  # 添加第一个值
        
        # 技术指标标准化
        technical_features = np.column_stack([sma_5, sma_20, rsi])
        technical_features_norm = (technical_features - technical_features.mean(axis=0)) / (technical_features.std(axis=0) + 1e-10)
        
        # 合并基础特征
        basic_features = np.concatenate([price_features_norm, technical_features_norm], axis=1)
        
        return basic_features
        
    except Exception as e:
        logger.error(f"❌ 基础特征计算失败: {e}")
        return np.zeros((len(price_data), 8))

def _get_feature_combination_info(self, config: Dict[str, Any]) -> str:
    """获取特征组合信息"""
    use_enhanced = config.get('use_enhanced_features', False)
    include_basic = config.get('include_basic_features', True)
    strategy = config.get('feature_selection_strategy', 'recommended_complementary')
    
    if not use_enhanced:
        return "仅基础特征（8个）"
    elif include_basic and strategy == 'recommended_complementary':
        return "基础特征（8个）+ 互补增强特征（31个）= 39个"
    elif include_basic and strategy == 'all':
        return "基础特征（8个）+ 全部增强特征（52个）= 60个"
    elif not include_basic and strategy == 'all':
        return "仅增强特征（52个）"
    else:
        return f"自定义组合（策略: {strategy}）"
'''
    
    return code_template

def create_implementation_plan():
    """创建实施计划"""
    
    plan = {
        'phase_1_analysis': {
            'status': '✅ 已完成',
            'tasks': [
                '分析基础特征构成（8个）',
                '分析增强特征构成（31个推荐，52个全部）',
                '识别潜在重复（发现2个低级别重复）',
                '确认重复程度很低，主要是互补关系'
            ]
        },
        
        'phase_2_optimization': {
            'status': '🔄 进行中',
            'tasks': [
                '设计优化的特征处理策略',
                '创建3种特征组合模式',
                '生成优化的特征计算代码',
                '确保向后兼容性'
            ]
        },
        
        'phase_3_implementation': {
            'status': '⏳ 待执行',
            'tasks': [
                '修改deep_learning_service.py',
                '添加优化的特征计算函数',
                '更新前端配置选项',
                '添加特征组合说明'
            ]
        },
        
        'phase_4_testing': {
            'status': '⏳ 待执行',
            'tasks': [
                '测试基础特征模式（兼容性）',
                '测试基础+增强特征模式',
                '测试仅增强特征模式',
                '验证特征数量和形状'
            ]
        }
    }
    
    return plan

def main():
    """主函数"""
    print('🔧 优化特征处理方案')
    print('=' * 80)
    
    # 创建优化策略
    strategy = create_optimized_feature_strategy()
    
    print("📊 优化特征处理策略:")
    print(f"\n🔹 基础特征（必选项）:")
    print(f"   - 数量: {strategy['basic_features']['total_count']}个")
    print(f"   - 组成: {strategy['basic_features']['features']}")
    print(f"   - 说明: {strategy['basic_features']['note']}")
    
    print(f"\n🔹 增强特征（可选项）:")
    for name, config in strategy['enhanced_features']['strategies'].items():
        print(f"   - {config['name']}: {config.get('count', '自定义')}个特征")
        print(f"     {config['description']}")
    
    print(f"\n🔹 组合模式:")
    for mode, config in strategy['combination_modes'].items():
        print(f"   - {config['name']}: {config['feature_count']}个特征")
        print(f"     {config['description']}")
        print(f"     兼容性: {config['compatibility']}")
    
    # 生成代码
    print(f"\n📝 生成优化代码模板...")
    code = generate_feature_handling_code()
    
    # 创建实施计划
    plan = create_implementation_plan()
    
    print(f"\n📋 实施计划:")
    for phase, details in plan.items():
        status = details['status']
        print(f"\n{status} {phase.replace('_', ' ').title()}:")
        for task in details['tasks']:
            print(f"   - {task}")
    
    print(f"\n✅ 优化方案设计完成！")
    print(f"💡 关键优势:")
    print(f"   - 基础特征作为必选项，确保兼容性")
    print(f"   - 增强特征与基础特征互补，避免重复")
    print(f"   - 提供3种灵活的组合模式")
    print(f"   - 支持渐进式升级（8→39→52个特征）")

if __name__ == "__main__":
    main()
