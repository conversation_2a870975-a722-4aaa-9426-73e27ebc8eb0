#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理AI推理交易页面中的回测相关代码
"""

import re

def cleanup_backtest_code():
    """清理回测相关代码"""
    
    # 读取文件
    with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🧹 开始清理AI推理交易页面中的回测相关代码...")
    
    # 需要删除的HTML元素ID和类名
    html_patterns_to_remove = [
        # 回测配置相关的HTML元素
        r'<input[^>]*id="backtestInitialBalance"[^>]*>',
        r'<input[^>]*id="backtestLotSize"[^>]*>',
        r'<input[^>]*id="backtestStopLoss"[^>]*>',
        r'<input[^>]*id="backtestTakeProfit"[^>]*>',
        r'<input[^>]*id="backtestMinConfidence"[^>]*>',
        r'<input[^>]*id="backtestDynamicSL"[^>]*>',
        r'<input[^>]*id="backtestCliffBrake"[^>]*>',
        r'<select[^>]*id="backtestPreset"[^>]*>.*?</select>',
        r'<select[^>]*id="optimizationPeriod"[^>]*>.*?</select>',
        r'<select[^>]*id="riskPreference"[^>]*>.*?</select>',
        
        # 回测相关的div和section
        r'<!-- 参数优化配置 -->.*?</div>\s*</div>\s*</div>',
        r'<div[^>]*class="form-text"[^>]*>选择用于参数优化的历史数据时间范围</div>',
        r'<div[^>]*class="form-text"[^>]*>选择参数优化的风险偏好策略</div>',
    ]
    
    # 需要删除的JavaScript函数
    js_functions_to_remove = [
        'applyBacktestPreset',
        'startBacktest',
        'loadSavedOptimizationResults',
        'changeSortPreference',
        'exportOptimizationResults',
        'startParameterOptimization',
        'displayBacktestResults',
        'displayOptimizationResults',
        'applyOptimizedParameters',
    ]
    
    # 删除HTML元素
    for pattern in html_patterns_to_remove:
        matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
        if matches:
            print(f"删除HTML元素: {len(matches)} 个匹配")
            content = re.sub(pattern, '', content, flags=re.DOTALL | re.IGNORECASE)
    
    # 删除JavaScript函数
    for func_name in js_functions_to_remove:
        # 匹配函数定义（包括async函数）
        pattern = rf'(async\s+)?function\s+{func_name}\s*\([^)]*\)\s*\{{[^}}]*(?:\{{[^}}]*\}}[^}}]*)*\}}'
        matches = re.findall(pattern, content, re.DOTALL)
        if matches:
            print(f"删除JavaScript函数: {func_name}")
            content = re.sub(pattern, '', content, flags=re.DOTALL)
    
    # 删除回测相关的变量和调用
    backtest_calls_to_remove = [
        r'loadSavedOptimizationResults\(\);?',
        r'document\.getElementById\([\'"]backtestCard[\'"]\)',
        r'document\.getElementById\([\'"]backtestStats[\'"]\)',
        r'document\.getElementById\([\'"]backtestResults[\'"]\)',
        r'document\.getElementById\([\'"]optimizationPeriod[\'"]\)',
        r'document\.getElementById\([\'"]riskPreference[\'"]\)',
        r'// 自动加载保存的优化结果.*?\n',
        r'// 检查是否有参数优化结果.*?\n.*?return;\s*\}',
        r'exportOptimizationResults\(\);?',
    ]
    
    for pattern in backtest_calls_to_remove:
        content = re.sub(pattern, '', content, flags=re.DOTALL)
    
    # 删除回测相关的注释
    comment_patterns = [
        r'// ==================== 回测相关函数 ====================\s*\n',
        r'// 获取回测配置.*?\n',
        r'// 验证回测配置.*?\n',
        r'// 开始交易回测.*?\n',
        r'// 显示回测结果.*?\n',
        r'// 参数优化.*?\n',
        r'// 应用优化后的参数.*?\n',
    ]
    
    for pattern in comment_patterns:
        content = re.sub(pattern, '', content, flags=re.DOTALL)
    
    # 清理多余的空行
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # 写回文件
    with open('templates/model_inference.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 回测相关代码清理完成！")
    
    # 统计清理结果
    remaining_backtest_refs = len(re.findall(r'backtest|回测|optimization|优化', content, re.IGNORECASE))
    print(f"📊 剩余回测相关引用: {remaining_backtest_refs} 个")
    
    return True

if __name__ == "__main__":
    cleanup_backtest_code()
