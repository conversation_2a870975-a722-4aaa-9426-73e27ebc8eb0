#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全清理推理配置区域
"""

def complete_cleanup():
    """完全清理推理配置区域"""
    print("🔧 完全清理推理配置区域")
    print("=" * 40)
    
    try:
        # 读取文件
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 原始文件大小: {len(content):,} 字符")
        
        # 找到AI推理交易区域的开始位置
        trading_section_start = content.find('<!-- 推理交易区域 -->')
        if trading_section_start == -1:
            trading_section_start = content.find('<div class="row" id="tradingSection">')
        
        if trading_section_start == -1:
            print("❌ 未找到AI推理交易区域")
            return False
        
        print(f"🔍 AI推理交易区域开始位置: {trading_section_start}")
        
        # 构建新的内容结构
        # 保留页面开头到推理配置区域之前的内容
        page_start = content.find('{% block content %}')
        if page_start == -1:
            print("❌ 未找到页面开始标记")
            return False
        
        # 找到页面标题区域的结束
        title_end = content.find('</div>\n    </div>\n\n    <!-- 推理配置区域已删除')
        if title_end == -1:
            title_end = content.find('</div>\n    </div>')
        
        if title_end == -1:
            print("❌ 未找到标题区域结束")
            return False
        
        # 构建新内容
        before_content = content[:title_end + len('</div>\n    </div>')]
        after_content = content[trading_section_start:]
        
        new_content = before_content + '\n\n    ' + after_content
        
        print(f"📄 新文件大小: {len(new_content):,} 字符")
        print(f"📊 删除了: {len(content) - len(new_content):,} 字符")
        
        # 写入新内容
        with open('templates/model_inference.html', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 推理配置区域完全清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def update_javascript():
    """更新JavaScript函数"""
    print(f"\n🔧 更新JavaScript函数...")
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新getInferenceConfig函数
        old_function = '''function getInferenceConfig() {
    return {
        trade_size: parseFloat(document.getElementById('inferenceTradeSize').value),
        min_confidence: parseFloat(document.getElementById('inferenceMinConfidence').value),
        stop_loss_pips: parseInt(document.getElementById('inferenceStopLoss').value),
        take_profit_pips: parseInt(document.getElementById('inferenceTakeProfit').value),
        trade_mode: document.getElementById('inferenceTradeMode').value,
        dynamic_sl: document.getElementById('inferenceDynamicSL').checked,
        trailing_stop: document.getElementById('inferenceTrailingStop').checked
    };
}'''
        
        new_function = '''function getInferenceConfig() {
    // 现在使用交易配置的参数
    return {
        trade_size: parseFloat(document.getElementById('tradingLotSize').value || '0.01'),
        min_confidence: parseFloat(document.getElementById('minConfidence').value || '0.7'),
        stop_loss_pips: parseInt(document.getElementById('stopLossPips').value || '50'),
        take_profit_pips: parseInt(document.getElementById('takeProfitPips').value || '100'),
        trade_mode: 'auto',
        dynamic_sl: document.getElementById('enableDynamicSL').checked,
        trailing_stop: document.getElementById('enableTrailingStop').checked
    };
}'''
        
        if old_function in content:
            content = content.replace(old_function, new_function)
            print("✅ 更新了getInferenceConfig函数")
        else:
            print("⚠️ 未找到完整的getInferenceConfig函数")
        
        # 删除不需要的函数
        functions_to_remove = [
            'toggleInferenceConfig',
            'applyInferencePreset', 
            'toggleTimeRangeVisibility',
            'applyTimeRangePreset',
            'loadSampleData',
            'resetForm'
        ]
        
        for func_name in functions_to_remove:
            # 简单的函数删除
            start_pattern = f'function {func_name}('
            start_pos = content.find(start_pattern)
            if start_pos != -1:
                # 找到函数结束位置
                brace_count = 0
                pos = start_pos
                in_function = False
                
                while pos < len(content):
                    char = content[pos]
                    if char == '{':
                        brace_count += 1
                        in_function = True
                    elif char == '}':
                        brace_count -= 1
                        if in_function and brace_count == 0:
                            # 找到函数结束
                            end_pos = pos + 1
                            # 删除函数
                            content = content[:start_pos] + content[end_pos:]
                            print(f"✅ 删除了{func_name}函数")
                            break
                    pos += 1
            else:
                print(f"⚠️ 未找到{func_name}函数")
        
        # 删除对已删除元素的引用
        elements_to_remove = [
            "document.getElementById('modelSelect')",
            "document.getElementById('symbol')",
            "document.getElementById('timeframe')",
            "document.getElementById('dataPoints')",
            "document.getElementById('inferenceMode')",
            "document.getElementById('startDate')",
            "document.getElementById('endDate')",
            "document.getElementById('useGPU')",
            "document.getElementById('showConfidence')",
            "document.getElementById('startInferenceBtn')",
            "document.getElementById('toggleInferenceBtn')"
        ]
        
        for element in elements_to_remove:
            if element in content:
                # 简单替换为null或默认值
                if 'modelSelect' in element:
                    content = content.replace(element, 'null')
                elif 'symbol' in element:
                    content = content.replace(element + '.value', "'XAUUSD'")
                elif 'timeframe' in element:
                    content = content.replace(element + '.value', "'1h'")
                elif 'dataPoints' in element:
                    content = content.replace(element + '.value', '100')
                elif 'inferenceMode' in element:
                    content = content.replace(element + '.value', "'realtime'")
                elif 'useGPU' in element:
                    content = content.replace(element + '.checked', 'true')
                elif 'showConfidence' in element:
                    content = content.replace(element + '.checked', 'true')
                else:
                    content = content.replace(element, 'null')
                
                print(f"✅ 更新了对{element}的引用")
        
        # 写入更新后的内容
        with open('templates/model_inference.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ JavaScript更新完成")
        return True
        
    except Exception as e:
        print(f"❌ JavaScript更新失败: {e}")
        return False

def final_verification():
    """最终验证"""
    print(f"\n🔍 最终验证...")
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查页面结构
        div_count = content.count('<div')
        div_close_count = content.count('</div>')
        
        print(f"📊 页面结构:")
        print(f"   • 文件大小: {len(content):,} 字符")
        print(f"   • div标签: {div_count} 开始, {div_close_count} 结束")
        
        if div_count == div_close_count:
            print("✅ div标签匹配")
        else:
            print(f"⚠️ div标签不匹配，差异: {div_count - div_close_count}")
        
        # 检查关键功能是否保留
        key_elements = [
            'AI推理交易',
            'tradingLotSize',
            'minConfidence', 
            'stopLossPips',
            'takeProfitPips',
            'startTradingBtn',
            'enableAutoTrading'
        ]
        
        preserved = []
        for element in key_elements:
            if element in content:
                preserved.append(element)
        
        print(f"✅ 保留的关键功能: {len(preserved)}/{len(key_elements)}")
        
        return len(preserved) == len(key_elements)
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始完全清理推理配置区域...")
    
    if complete_cleanup():
        if update_javascript():
            if final_verification():
                print("\n🎉 推理配置区域清理完成！")
                print("📋 清理总结:")
                print("   • 删除了所有推理配置UI")
                print("   • 更新了JavaScript函数")
                print("   • 保留了实盘交易功能")
                print("\n🔄 请重启应用并测试")
            else:
                print("⚠️ 验证未通过")
        else:
            print("❌ JavaScript更新失败")
    else:
        print("❌ 清理失败")
