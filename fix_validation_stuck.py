#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复验证阶段卡住问题
解决验证损失为0.0的问题
"""

import sqlite3
import json
from datetime import datetime
import requests

def analyze_validation_issue():
    """分析验证阶段问题"""
    print('🔍 分析验证阶段卡住问题')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询卡住的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   train_loss, val_loss, logs
            FROM training_tasks 
            WHERE id = '76dd3e3a-c1b0-4360-91f6-15424cc96f96'
        ''')
        
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            print("❌ 没有找到指定任务")
            return None
        
        task_id, model_id, status, progress, current_epoch, total_epochs, train_loss, val_loss, logs = result
        
        print(f"📊 任务详情:")
        print(f"   任务ID: {task_id}")
        print(f"   状态: {status}")
        print(f"   进度: {current_epoch}/{total_epochs} ({progress:.1f}%)")
        print(f"   训练损失: {train_loss}")
        print(f"   验证损失: {val_loss}")
        
        # 分析验证问题
        print(f"\n🔍 验证问题分析:")
        
        if val_loss == 0.0:
            print(f"   ❌ 验证损失为0.0 - 验证阶段异常")
            print(f"   可能原因:")
            print(f"     - 验证数据加载失败")
            print(f"     - 验证计算过程卡住")
            print(f"     - GPU内存不足")
            print(f"     - 数据加载器死锁")
        
        if train_loss > 0:
            print(f"   ✅ 训练损失正常 ({train_loss:.4f})")
            print(f"   说明训练过程本身是正常的")
        
        # 解析日志
        if logs:
            try:
                log_data = json.loads(logs) if isinstance(logs, str) else logs
                print(f"\n📋 日志信息:")
                print(f"   当前轮次: {log_data.get('epoch', 'unknown')}")
                print(f"   日志中训练损失: {log_data.get('train_loss', 'N/A')}")
                print(f"   日志中验证损失: {log_data.get('val_loss', 'N/A')}")
                print(f"   训练准确率: {log_data.get('train_acc', 'N/A')}")
                print(f"   验证准确率: {log_data.get('val_acc', 'N/A')}")
            except Exception as e:
                print(f"   ❌ 日志解析失败: {e}")
        
        return {
            'task_id': task_id,
            'current_epoch': current_epoch,
            'total_epochs': total_epochs,
            'val_loss_issue': val_loss == 0.0
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def stop_stuck_training():
    """停止卡住的训练"""
    print('\n🛑 停止卡住的训练')
    print('=' * 60)
    
    try:
        # 使用API停止训练
        session = requests.Session()
        
        # 登录
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
            
            # 停止训练
            task_id = '76dd3e3a-c1b0-4360-91f6-15424cc96f96'
            stop_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/stop-training/{task_id}')
            
            if stop_response.status_code == 200:
                result = stop_response.json()
                if result.get('success'):
                    print(f"✅ 训练已停止")
                    return True
                else:
                    print(f"❌ 停止失败: {result.get('error')}")
            else:
                print(f"❌ 停止请求失败: {stop_response.status_code}")
        else:
            print(f"❌ 登录失败: {response.status_code}")
        
        return False
        
    except Exception as e:
        print(f"❌ 停止训练失败: {e}")
        return False

def force_update_task_status():
    """强制更新任务状态"""
    print('\n🔧 强制更新任务状态')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        task_id = '76dd3e3a-c1b0-4360-91f6-15424cc96f96'
        
        # 更新任务状态为stopped
        cursor.execute('''
            UPDATE training_tasks 
            SET status = 'stopped',
                updated_at = ?,
                logs = ?
            WHERE id = ?
        ''', (
            datetime.now().isoformat(),
            json.dumps({
                "stage": "stopped",
                "message": "训练已手动停止 - 验证阶段卡住",
                "reason": "验证损失异常(0.0)，可能验证阶段卡住",
                "stopped_at_epoch": 45,
                "total_epochs": 200
            }),
            task_id
        ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 任务状态已更新为 stopped")
        return True
        
    except Exception as e:
        print(f"❌ 状态更新失败: {e}")
        return False

def check_gpu_memory():
    """检查GPU内存使用"""
    print('\n🔍 检查GPU内存使用')
    print('=' * 60)
    
    try:
        import subprocess
        result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total,utilization.gpu', 
                               '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for i, line in enumerate(lines):
                if line.strip():
                    parts = line.split(', ')
                    if len(parts) >= 3:
                        mem_used, mem_total, gpu_util = parts[:3]
                        mem_percent = float(mem_used) / float(mem_total) * 100
                        
                        print(f"   GPU {i}:")
                        print(f"     内存使用: {mem_used}/{mem_total} MB ({mem_percent:.1f}%)")
                        print(f"     GPU使用率: {gpu_util}%")
                        
                        if mem_percent > 90:
                            print(f"     ⚠️ GPU内存使用率过高！")
                        if float(gpu_util) == 0:
                            print(f"     ⚠️ GPU使用率为0，可能训练已停止")
        else:
            print(f"❌ nvidia-smi命令失败")
            
    except Exception as e:
        print(f"❌ GPU检查失败: {e}")

def provide_restart_guidance():
    """提供重启指导"""
    print('\n🔧 重启训练指导')
    print('=' * 60)
    
    print("📋 推荐的重启配置:")
    
    restart_config = {
        'model_name': '增强特征模型_重启',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'data_config': {'mode': 'days', 'training_days': 90},
        'sequence_length': 20,
        'hidden_size': 64,
        'num_layers': 2,
        'dropout': 0.3,
        'batch_size': 8,  # 减小batch_size避免内存问题
        'learning_rate': 0.0001,
        'epochs': 100,  # 减少轮次
        'patience': 10,
        'early_stopping': True,
        'min_epochs': 5,
        'use_gpu': True,
        'save_checkpoints': True,
        'use_enhanced_features': True,
        'feature_selection_strategy': 'enhanced',
        'include_basic_features': True,
        'analyze_feature_importance': True,
        'auto_start_training': False,  # 手动启动
        'validation_split': 0.2,  # 明确设置验证集比例
        'num_workers': 0,  # 避免数据加载器问题
    }
    
    print("🔧 关键优化:")
    print(f"   - batch_size: 8 (从16减少，避免GPU内存问题)")
    print(f"   - epochs: 100 (从200减少，更快完成)")
    print(f"   - num_workers: 0 (避免数据加载器死锁)")
    print(f"   - auto_start_training: false (手动控制)")
    print(f"   - validation_split: 0.2 (明确验证集设置)")
    
    print(f"\n💡 重启步骤:")
    print(f"   1. 确认当前训练已停止")
    print(f"   2. 清理GPU内存 (重启Flask应用)")
    print(f"   3. 使用优化配置重新开始训练")
    print(f"   4. 密切监控验证阶段")

def main():
    """主函数"""
    print('🔧 修复验证阶段卡住问题')
    print('=' * 80)
    
    # 分析验证问题
    task_info = analyze_validation_issue()
    
    if task_info and task_info['val_loss_issue']:
        print(f"\n🎯 确认问题: 验证损失为0.0，验证阶段卡住")
        
        # 检查GPU内存
        check_gpu_memory()
        
        # 停止卡住的训练
        print(f"\n🛑 正在停止卡住的训练...")
        stop_success = stop_stuck_training()
        
        if not stop_success:
            print(f"⚠️ API停止失败，尝试强制更新状态...")
            force_update_task_status()
        
        # 提供重启指导
        provide_restart_guidance()
        
        print(f"\n✅ 修复完成！")
        print(f"💡 建议:")
        print(f"   1. 等待几分钟确保训练完全停止")
        print(f"   2. 重启Flask应用清理GPU内存")
        print(f"   3. 使用优化配置重新开始训练")
        print(f"   4. 监控验证阶段是否正常")
        
    else:
        print(f"✅ 没有发现验证问题或任务不存在")

if __name__ == "__main__":
    main()
