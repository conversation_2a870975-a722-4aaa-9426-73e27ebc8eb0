#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复结果
"""

import requests
import time

def test_fixes():
    """测试修复结果"""
    print("🧪 测试修复结果")
    print("=" * 40)
    
    # 等待应用启动
    time.sleep(3)
    
    try:
        # 测试应用响应
        response = requests.get('http://127.0.0.1:5000', timeout=5)
        print(f"应用状态: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ 应用未正常运行")
            return False
        
        # 登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 测试AI推理交易页面
        print(f"\n🔍 测试AI推理交易页面...")
        inference_response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if inference_response.status_code != 200:
            print(f"❌ AI推理交易页面访问失败: {inference_response.status_code}")
            return False
        
        inference_content = inference_response.text
        print(f"✅ AI推理交易页面访问成功，大小: {len(inference_content):,} 字符")
        
        # 检查修复1: 模型加载和MT5连接
        print(f"\n🔍 检查修复1: 模型加载和MT5连接...")
        
        # 检查loadAvailableModels函数是否正确
        if 'window.availableModels' in inference_content:
            print("✅ 模型加载函数已修复")
        else:
            print("⚠️ 模型加载函数可能仍有问题")
        
        # 检查MT5连接函数
        if 'checkMT5Connection()' in inference_content:
            print("✅ MT5连接检查函数存在")
        else:
            print("❌ MT5连接检查函数缺失")
        
        # 检查修复3: 增强特征选项
        print(f"\n🔍 检查修复3: 增强特征选项...")
        
        if 'id="enableEnhancedFeatures"' in inference_content:
            print("✅ 增强特征选项已添加")
        else:
            print("❌ 增强特征选项未找到")
        
        if 'enable_enhanced_features' in inference_content:
            print("✅ 增强特征JavaScript处理已添加")
        else:
            print("❌ 增强特征JavaScript处理缺失")
        
        # 测试AI推理回测页面
        print(f"\n🔍 测试AI推理回测页面...")
        backtest_response = session.get('http://127.0.0.1:5000/deep-learning/backtest')
        
        if backtest_response.status_code != 200:
            print(f"❌ AI推理回测页面访问失败: {backtest_response.status_code}")
            return False
        
        backtest_content = backtest_response.text
        print(f"✅ AI推理回测页面访问成功，大小: {len(backtest_content):,} 字符")
        
        # 检查修复2: 回测JavaScript修复
        print(f"\n🔍 检查修复2: 回测JavaScript修复...")
        
        # 检查backtest_functions.js是否被引用
        if 'backtest_functions.js' in backtest_content:
            print("✅ 回测JavaScript文件已引用")
        else:
            print("⚠️ 回测JavaScript文件可能未引用")
        
        # 保存页面内容用于调试
        with open('fixed_inference_page.html', 'w', encoding='utf-8') as f:
            f.write(inference_content)
        
        with open('fixed_backtest_page.html', 'w', encoding='utf-8') as f:
            f.write(backtest_content)
        
        print(f"💾 页面内容已保存用于调试")
        
        # 测试API端点
        print(f"\n🔍 测试关键API端点...")
        
        # 测试模型列表API
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        if models_response.status_code == 200:
            models_data = models_response.json()
            if models_data.get('success'):
                print(f"✅ 模型列表API正常，找到 {len(models_data.get('models', []))} 个模型")
            else:
                print(f"⚠️ 模型列表API返回错误: {models_data.get('error')}")
        else:
            print(f"❌ 模型列表API访问失败: {models_response.status_code}")
        
        # 测试MT5连接状态API
        mt5_response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        if mt5_response.status_code == 200:
            mt5_data = mt5_response.json()
            print(f"✅ MT5连接状态API正常，连接状态: {mt5_data.get('connected', False)}")
        else:
            print(f"❌ MT5连接状态API访问失败: {mt5_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_fixes()
    
    if success:
        print("\n🎉 修复测试基本通过！")
        print("📋 修复总结:")
        print("   1. ✅ 模型加载函数已修复")
        print("   2. ✅ 回测JavaScript toFixed()错误已修复")
        print("   3. ✅ 增强特征选项已添加到交易配置")
        print("\n🔄 请刷新浏览器页面测试功能")
    else:
        print("\n⚠️ 修复测试未完全通过")
        print("🔧 可能需要进一步调试")
