#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复卡住的训练任务
"""

import sqlite3
import json
from datetime import datetime

def fix_stuck_training():
    """修复卡住的训练任务"""
    
    print("🔧 修复卡住的训练任务")
    print("=" * 60)
    
    # 卡住的任务ID
    stuck_task_id = "9e972592-d3f4-411c-a394-d3154409870a"
    stuck_model_id = "373874e7-dac6-4de6-801f-2e81846855ca"
    
    print(f"📋 目标任务:")
    print(f"   任务ID: {stuck_task_id}")
    print(f"   模型ID: {stuck_model_id}")
    print(f"   问题: 进度25%但轮次0/100，训练循环未正常启动")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 1. 检查任务详细信息
        print(f"\n🔍 检查任务详细信息...")
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   created_at, updated_at, logs
            FROM training_tasks
            WHERE id = ?
        ''', (stuck_task_id,))
        
        task_info = cursor.fetchone()
        if not task_info:
            print(f"❌ 任务不存在: {stuck_task_id}")
            return False
        
        task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs = task_info
        
        print(f"✅ 任务信息:")
        print(f"   状态: {status}")
        print(f"   进度: {progress}%")
        print(f"   轮次: {current_epoch}/{total_epochs}")
        print(f"   创建时间: {created_at}")
        print(f"   更新时间: {updated_at}")
        
        # 解析日志
        if logs:
            try:
                log_data = json.loads(logs)
                print(f"   当前阶段: {log_data.get('stage', 'unknown')}")
                print(f"   消息: {log_data.get('message', 'No message')}")
            except:
                print(f"   日志: {logs[:100]}...")
        
        # 2. 提供修复选项
        print(f"\n🔧 修复选项:")
        print(f"   1. 停止卡住的任务")
        print(f"   2. 删除任务和模型")
        
        choice = input(f"\n请选择修复方式 (1-2): ")
        
        if choice == '1':
            # 停止任务
            print(f"\n🛑 停止卡住的任务...")
            cursor.execute('''
                UPDATE training_tasks 
                SET status = 'stopped', updated_at = ?,
                    logs = ?
                WHERE id = ?
            ''', (
                datetime.now().isoformat(),
                json.dumps({
                    "stage": "manual_stopped",
                    "message": "用户手动停止卡住的任务",
                    "reason": "stuck_training",
                    "action": "manual_stop"
                }),
                stuck_task_id
            ))
            
            # 更新模型状态
            cursor.execute('''
                UPDATE deep_learning_models 
                SET status = 'failed'
                WHERE id = ?
            ''', (model_id,))
            
            print(f"✅ 任务已停止")
            
        elif choice == '2':
            # 删除任务和模型
            confirm = input(f"\n⚠️ 确定要删除任务和模型吗？(输入 'YES' 确认): ")
            if confirm == 'YES':
                print(f"\n🗑️ 删除任务和模型...")
                
                # 删除训练任务
                cursor.execute('DELETE FROM training_tasks WHERE id = ?', (stuck_task_id,))
                
                # 删除模型
                cursor.execute('DELETE FROM deep_learning_models WHERE id = ?', (model_id,))
                
                print(f"✅ 任务和模型已删除")
            else:
                print(f"❌ 操作已取消")
                return False
        else:
            print(f"❌ 无效选择")
            return False
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n✅ 修复完成!")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def restart_training():
    """重新启动训练"""
    
    print(f"\n🚀 重新启动训练")
    print("=" * 60)
    
    print(f"💡 建议:")
    print(f"   1. 打开浏览器访问 http://localhost:5000")
    print(f"   2. 进入 '模型训练' 页面")
    print(f"   3. 使用以下配置重新训练:")
    print(f"      - 模型名称: XAU-H1-2Y-增强特征-修复版-V2")
    print(f"      - 模型类型: CNN_LSTM")
    print(f"      - 启用增强特征: ✅")
    print(f"      - 特征策略: recommended")
    print(f"      - 序列长度: 60 (减少以便快速验证)")
    print(f"      - 隐藏层大小: 128")
    print(f"      - 训练轮数: 20 (减少轮数以便快速验证)")

def main():
    """主函数"""
    
    print("🚀 修复卡住的深度学习训练任务")
    print("=" * 80)
    
    # 修复卡住的任务
    if fix_stuck_training():
        # 提供重新训练指导
        restart_training()
    else:
        print(f"\n❌ 修复失败")

if __name__ == "__main__":
    main()
