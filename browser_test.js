
// 浏览器控制台测试脚本
console.log("🧪 开始测试AI推理交易功能...");

// 1. 检查关键元素是否存在
const elements = {
    tradingModelSelect: document.getElementById('tradingModelSelect'),
    mt5ConnectionStatus: document.getElementById('mt5ConnectionStatus'),
    startTradingBtn: document.getElementById('startTradingBtn'),
    enableEnhancedFeatures: document.getElementById('enableEnhancedFeatures')
};

console.log("📋 DOM元素检查:");
for (const [name, element] of Object.entries(elements)) {
    if (element) {
        console.log(`✅ ${name}: 存在`);
    } else {
        console.log(`❌ ${name}: 缺失`);
    }
}

// 2. 检查模型选择下拉框选项
const modelSelect = elements.tradingModelSelect;
if (modelSelect) {
    console.log(`📋 模型选择选项数量: ${modelSelect.options.length}`);
    if (modelSelect.options.length > 1) {
        console.log("✅ 模型选择有选项");
        for (let i = 1; i < Math.min(modelSelect.options.length, 4); i++) {
            console.log(`   ${i}. ${modelSelect.options[i].text}`);
        }
    } else {
        console.log("❌ 模型选择没有选项");
    }
}

// 3. 检查MT5连接状态
const mt5Status = elements.mt5ConnectionStatus;
if (mt5Status) {
    console.log(`📋 MT5连接状态: ${mt5Status.textContent}`);
    console.log(`📋 MT5状态样式: ${mt5Status.className}`);
}

// 4. 测试关键函数是否可调用
console.log("🔧 测试关键函数:");
try {
    if (typeof loadTradingModels === 'function') {
        console.log("✅ loadTradingModels函数可用");
    } else {
        console.log("❌ loadTradingModels函数不可用");
    }
    
    if (typeof checkMT5Connection === 'function') {
        console.log("✅ checkMT5Connection函数可用");
    } else {
        console.log("❌ checkMT5Connection函数不可用");
    }
} catch (error) {
    console.log("❌ 函数测试出错:", error);
}

console.log("🎯 测试完成！请检查上述结果。");
