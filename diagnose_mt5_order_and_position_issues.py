#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断MT5订单和持仓显示问题
1. 手动买入订单执行成功但MT5软件没有接收到订单
2. 当前持仓显示8，页面下方有时候能显示持仓具体信息，大多数时间没显示
"""

import requests
import json
import time
from datetime import datetime

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post('http://127.0.0.1:5000/login', data=login_data)
    if response.status_code == 200:
        print("✅ 登录成功")
        return session
    else:
        print(f"❌ 登录失败: {response.status_code}")
        return None

def check_mt5_connection_status():
    """检查MT5连接状态"""
    print("\n🔍 检查MT5连接状态")
    print("=" * 60)
    
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5
        
        # 1. 检查服务连接状态
        print("1. 检查MT5服务连接状态")
        status = mt5_service.get_connection_status()
        print(f"   连接状态: {'✅ 已连接' if status.get('connected') else '❌ 未连接'}")
        print(f"   重连服务: {'✅ 活跃' if status.get('reconnect_service_active') else '❌ 未活跃'}")
        
        # 2. 检查MT5库连接状态
        print("\n2. 检查MT5库连接状态")
        if mt5.initialize():
            print("   ✅ MT5库初始化成功")
            
            # 获取账户信息
            account_info = mt5.account_info()
            if account_info:
                print(f"   ✅ 账户信息: {account_info.login} ({account_info.server})")
                print(f"   账户余额: ${account_info.balance:.2f}")
                print(f"   账户净值: ${account_info.equity:.2f}")
                print(f"   账户类型: {'模拟' if account_info.trade_mode == mt5.ACCOUNT_TRADE_MODE_DEMO else '真实'}")
            else:
                print("   ❌ 无法获取账户信息")
                
            # 检查终端信息
            terminal_info = mt5.terminal_info()
            if terminal_info:
                print(f"   ✅ 终端信息: {terminal_info.name}")
                print(f"   终端路径: {terminal_info.path}")
                print(f"   是否连接: {'是' if terminal_info.connected else '否'}")
                print(f"   交易允许: {'是' if terminal_info.trade_allowed else '否'}")
            else:
                print("   ❌ 无法获取终端信息")
                
        else:
            print("   ❌ MT5库初始化失败")
            
        return status.get('connected', False)
        
    except Exception as e:
        print(f"❌ 检查MT5连接状态异常: {e}")
        return False

def test_manual_order_execution(session):
    """测试手动订单执行"""
    print("\n🧪 测试手动订单执行")
    print("=" * 60)
    
    # 构建测试订单数据
    test_order = {
        'symbol': 'XAUUSD',
        'action': 'BUY',
        'lot_size': 0.01,
        'stop_loss_pips': 50,
        'take_profit_pips': 100,
        'trading_config': {
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'enable_dynamic_sl': True,
            'enable_trailing_stop': False,
            'account_type': 'demo'
        },
        'inference_result': {
            'prediction': 'BUY',
            'confidence': 1.0,
            'reasoning': '手动测试订单'
        },
        'manual_trade': True
    }
    
    print(f"📊 测试订单参数:")
    print(f"   品种: {test_order['symbol']}")
    print(f"   方向: {test_order['action']}")
    print(f"   手数: {test_order['lot_size']}")
    print(f"   止损: {test_order['stop_loss_pips']} pips")
    print(f"   止盈: {test_order['take_profit_pips']} pips")
    
    try:
        print(f"\n🔄 发送订单到深度学习交易API...")
        response = session.post('http://127.0.0.1:5000/api/deep-learning/execute-trade', 
                               json=test_order)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                order_id = result.get('order_id')
                print(f"✅ 订单API执行成功: 订单ID {order_id}")
                
                # 立即检查MT5中是否有这个订单
                print(f"\n🔍 检查MT5中的订单...")
                mt5_found = check_order_in_mt5(order_id)
                
                if mt5_found:
                    print(f"✅ 订单在MT5中找到")
                    return True, order_id
                else:
                    print(f"❌ 订单在MT5中未找到 - 这是问题所在！")
                    return False, order_id
            else:
                print(f"❌ 订单API执行失败: {result.get('error')}")
                return False, None
        else:
            print(f"❌ 订单API请求失败: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ 测试订单执行异常: {e}")
        return False, None

def check_order_in_mt5(order_id):
    """检查订单是否在MT5中"""
    try:
        import MetaTrader5 as mt5
        
        # 1. 检查持仓
        positions = mt5.positions_get()
        if positions:
            for pos in positions:
                if pos.ticket == order_id:
                    print(f"   ✅ 在持仓中找到: 票号 {pos.ticket}, 品种 {pos.symbol}")
                    return True
        
        # 2. 检查历史订单
        from datetime import datetime, timedelta
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)  # 查看最近1小时
        
        orders = mt5.history_orders_get(start_time, end_time)
        if orders:
            for order in orders:
                if order.ticket == order_id:
                    print(f"   ✅ 在历史订单中找到: 票号 {order.ticket}, 状态 {order.state}")
                    return True
        
        # 3. 检查历史交易
        deals = mt5.history_deals_get(start_time, end_time)
        if deals:
            for deal in deals:
                if deal.order == order_id or deal.position == order_id:
                    print(f"   ✅ 在历史交易中找到: 订单 {deal.order}, 持仓 {deal.position}")
                    return True
        
        print(f"   ❌ 在MT5中未找到订单 {order_id}")
        return False
        
    except Exception as e:
        print(f"   ❌ 检查MT5订单异常: {e}")
        return False

def diagnose_position_display_issue(session):
    """诊断持仓显示问题"""
    print("\n🔍 诊断持仓显示问题")
    print("=" * 60)
    
    # 1. 检查交易统计API
    print("1. 检查交易统计API")
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/trading-statistics')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('statistics', {})
                current_positions = stats.get('current_positions', 0)
                print(f"   ✅ 交易统计API: 当前持仓 {current_positions}")
            else:
                print(f"   ❌ 交易统计API失败: {result.get('error')}")
        else:
            print(f"   ❌ 交易统计API请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 交易统计API异常: {e}")
    
    # 2. 检查MT5持仓API
    print("\n2. 检查MT5持仓API")
    try:
        response = session.get('http://127.0.0.1:5000/api/mt5/positions')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                positions = result.get('positions', [])
                print(f"   ✅ MT5持仓API: 获取到 {len(positions)} 个持仓")
                
                # 显示前3个持仓的详细信息
                for i, pos in enumerate(positions[:3]):
                    print(f"   持仓 {i+1}: {pos.get('symbol')} {pos.get('ticket')} {pos.get('volume')}手")
            else:
                print(f"   ❌ MT5持仓API失败: {result.get('error')}")
        else:
            print(f"   ❌ MT5持仓API请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ MT5持仓API异常: {e}")
    
    # 3. 直接检查MT5持仓
    print("\n3. 直接检查MT5持仓")
    try:
        import MetaTrader5 as mt5
        positions = mt5.positions_get()
        if positions:
            print(f"   ✅ MT5直接查询: 获取到 {len(positions)} 个持仓")
            
            # 显示所有持仓的基本信息
            for pos in positions:
                print(f"   持仓: {pos.symbol} 票号{pos.ticket} {pos.volume}手 魔术号{pos.magic} 注释'{pos.comment}'")
        else:
            print(f"   ❌ MT5直接查询: 无持仓或查询失败")
    except Exception as e:
        print(f"   ❌ MT5直接查询异常: {e}")
    
    # 4. 检查持仓详情API
    print("\n4. 检查持仓详情API")
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/position-details')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                positions = result.get('positions', [])
                print(f"   ✅ 持仓详情API: 获取到 {len(positions)} 个持仓")
            else:
                print(f"   ❌ 持仓详情API失败: {result.get('error')}")
        else:
            print(f"   ❌ 持仓详情API请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 持仓详情API异常: {e}")

def test_position_consistency():
    """测试持仓数据一致性"""
    print("\n🔄 测试持仓数据一致性")
    print("=" * 60)
    
    try:
        import MetaTrader5 as mt5
        from services.mt5_service import mt5_service
        
        # 1. MT5直接查询
        mt5_direct = mt5.positions_get()
        mt5_direct_count = len(mt5_direct) if mt5_direct else 0
        print(f"1. MT5直接查询: {mt5_direct_count} 个持仓")
        
        # 2. MT5服务查询
        mt5_service_positions = mt5_service.get_positions()
        mt5_service_count = len(mt5_service_positions) if mt5_service_positions else 0
        print(f"2. MT5服务查询: {mt5_service_count} 个持仓")
        
        # 3. 比较结果
        if mt5_direct_count == mt5_service_count:
            print(f"✅ 持仓数据一致: {mt5_direct_count} 个")
        else:
            print(f"❌ 持仓数据不一致: 直接查询{mt5_direct_count} vs 服务查询{mt5_service_count}")
            
        # 4. 详细比较
        if mt5_direct and mt5_service_positions:
            direct_tickets = {pos.ticket for pos in mt5_direct}
            service_tickets = {pos['ticket'] for pos in mt5_service_positions}
            
            if direct_tickets == service_tickets:
                print(f"✅ 持仓票号一致")
            else:
                print(f"❌ 持仓票号不一致:")
                print(f"   直接查询: {direct_tickets}")
                print(f"   服务查询: {service_tickets}")
                
        return mt5_direct_count, mt5_service_count
        
    except Exception as e:
        print(f"❌ 测试持仓一致性异常: {e}")
        return 0, 0

def main():
    """主函数"""
    print("🔧 诊断MT5订单和持仓显示问题")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("1. 手动买入订单执行成功但MT5软件没有接收到订单")
    print("2. 当前持仓显示8，页面下方有时候能显示持仓具体信息，大多数时间没显示")
    print()
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 检查MT5连接状态
    mt5_connected = check_mt5_connection_status()
    
    # 3. 测试手动订单执行
    if mt5_connected:
        order_success, order_id = test_manual_order_execution(session)
    else:
        print("⚠️ MT5未连接，跳过订单测试")
        order_success = False
        order_id = None
    
    # 4. 诊断持仓显示问题
    diagnose_position_display_issue(session)
    
    # 5. 测试持仓数据一致性
    direct_count, service_count = test_position_consistency()
    
    # 6. 总结和建议
    print(f"\n📊 诊断结果总结")
    print("=" * 80)
    
    print(f"MT5连接状态: {'✅ 正常' if mt5_connected else '❌ 异常'}")
    print(f"订单执行测试: {'✅ 成功' if order_success else '❌ 失败'}")
    print(f"持仓数据一致性: {'✅ 一致' if direct_count == service_count else '❌ 不一致'}")
    
    print(f"\n💡 问题分析和建议:")
    
    if not mt5_connected:
        print("🔧 MT5连接问题:")
        print("   • 检查MT5终端是否正常运行")
        print("   • 检查MT5是否允许自动交易")
        print("   • 重启MT5终端和应用程序")
    
    if not order_success and mt5_connected:
        print("🔧 订单执行问题:")
        print("   • 检查MT5账户是否为模拟账户")
        print("   • 检查交易品种是否可交易")
        print("   • 检查账户余额是否充足")
        print("   • 检查魔术号设置是否正确")
    
    if direct_count != service_count:
        print("🔧 持仓显示问题:")
        print("   • MT5服务和直接查询结果不一致")
        print("   • 可能是服务缓存或过滤逻辑问题")
        print("   • 建议重启MT5服务")
    
    if direct_count > 0:
        print("🔧 持仓显示建议:")
        print("   • 检查前端JavaScript控制台错误")
        print("   • 检查API响应时间和超时设置")
        print("   • 清除浏览器缓存重新加载页面")
    
    return 0

if __name__ == "__main__":
    main()
