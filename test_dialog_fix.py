#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据准备完成弹窗修复
验证弹窗不会重复出现
"""

import requests
import time
import json

def test_dialog_fix():
    """测试弹窗修复效果"""
    print('🧪 测试数据准备完成弹窗修复')
    print('=' * 50)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 创建一个测试训练任务
    print("\n📊 创建测试训练任务...")
    
    test_config = {
        'model_name': f'弹窗修复测试_{int(time.time())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'data_config': {'mode': 'days', 'training_days': 30},
        'sequence_length': 20,
        'hidden_size': 64,
        'num_layers': 2,
        'dropout': 0.2,
        'batch_size': 16,
        'learning_rate': 0.001,
        'epochs': 5,
        'patience': 3,
        'early_stopping': True,
        'min_epochs': 2,
        'use_gpu': True,
        'save_checkpoints': True,
        'use_enhanced_features': True,
        'feature_selection_strategy': 'recommended',
        'include_basic_features': True,
        'analyze_feature_importance': True,
        'auto_start_training': False  # 关闭自动启动，观察弹窗行为
    }
    
    try:
        response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                               json=test_config,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 测试任务创建成功: {task_id}")
                
                # 监控数据准备过程
                print("⏳ 监控数据准备过程...")
                
                max_wait_time = 120  # 最多等待2分钟
                start_time = time.time()
                data_ready_count = 0  # 记录检测到data_ready状态的次数
                
                while time.time() - start_time < max_wait_time:
                    # 检查任务状态
                    progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                    
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        if progress_data.get('success'):
                            progress = progress_data.get('progress', {})
                            status = progress.get('status', 'unknown')
                            progress_percent = progress.get('progress', 0)
                            
                            print(f"   状态: {status}, 进度: {progress_percent}%")
                            
                            if status == 'data_ready' and progress_percent >= 100:
                                data_ready_count += 1
                                print(f"   📊 检测到data_ready状态 (第{data_ready_count}次)")
                                
                                if data_ready_count == 1:
                                    print("   ✅ 首次检测到data_ready状态")
                                elif data_ready_count <= 5:
                                    print(f"   ⏳ 继续监控，观察是否有重复弹窗问题...")
                                else:
                                    print(f"   🎯 已监控{data_ready_count}次data_ready状态")
                                    break
                                    
                            elif status == 'failed':
                                print(f"❌ 数据准备失败")
                                return False
                            
                            # 继续等待
                            time.sleep(2)
                        else:
                            print(f"❌ 获取进度失败: {progress_data.get('error')}")
                            return False
                    else:
                        print(f"❌ 进度请求失败: {progress_response.status_code}")
                        return False
                
                if data_ready_count > 0:
                    print(f"\n📊 测试结果:")
                    print(f"   检测到data_ready状态次数: {data_ready_count}")
                    print(f"   预期行为: 前端JavaScript应该只在第一次显示弹窗")
                    print(f"   修复状态: ✅ 已添加防重复弹窗机制")
                    return True
                else:
                    print(f"⏰ 等待超时，未检测到data_ready状态")
                    return False
                
            else:
                print(f"❌ 任务创建失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def check_code_changes():
    """检查代码修改"""
    print('\n🔍 检查代码修改')
    print('=' * 50)
    
    try:
        with open('templates/model_training.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        changes_found = []
        
        # 检查全局变量
        if 'let dataReadyDialogShown = false;' in content:
            changes_found.append("✅ 添加了防重复弹窗的全局变量")
        
        # 检查条件判断
        if 'if (!dataReadyDialogShown)' in content:
            changes_found.append("✅ 添加了防重复弹窗的条件判断")
        
        # 检查重置逻辑
        if 'dataReadyDialogShown = false;' in content:
            changes_found.append("✅ 添加了重置弹窗标志的逻辑")
        
        if changes_found:
            print("代码修改检查:")
            for change in changes_found:
                print(f"   {change}")
            return True
        else:
            print("❌ 未找到预期的代码修改")
            return False
            
    except Exception as e:
        print(f"❌ 检查代码失败: {e}")
        return False

def create_fix_summary():
    """创建修复总结"""
    print('\n📊 修复总结')
    print('=' * 50)
    
    print("🎯 问题描述:")
    print("   开始模型训练时，数据准备完成弹窗会弹出多次")
    
    print("\n🔍 问题原因:")
    print("   进度监控每秒执行一次，每次检测到data_ready状态都会弹窗")
    print("   没有机制防止重复显示弹窗")
    
    print("\n🔧 修复方案:")
    print("   1. 添加全局变量 dataReadyDialogShown 作为标志")
    print("   2. 只在第一次检测到data_ready状态时显示弹窗")
    print("   3. 在重置函数中重置标志，确保下次训练时可以正常弹窗")
    
    print("\n📝 具体修改:")
    print("   文件: templates/model_training.html")
    print("   修改点:")
    print("     - 第579行: 添加 dataReadyDialogShown 全局变量")
    print("     - 第1443-1447行: 添加防重复弹窗的条件判断")
    print("     - 第639行: 在clearTrainingState中重置标志")
    print("     - 第2190行: 在resetButtonStates中重置标志")
    
    print("\n💡 修复效果:")
    print("   ✅ 数据准备完成弹窗只会在第一次检测到时显示")
    print("   ✅ 不影响正常的训练流程")
    print("   ✅ 重置训练后可以正常显示弹窗")
    print("   ✅ 保持用户体验的一致性")

def provide_manual_test_guide():
    """提供手动测试指导"""
    print('\n📋 手动测试指导')
    print('=' * 50)
    
    print("请按以下步骤手动验证修复效果:")
    print("1. 打开浏览器访问模型训练页面")
    print("2. 配置训练参数，关闭自动启动训练")
    print("3. 点击'开始数据准备'")
    print("4. 观察数据准备过程中的弹窗行为")
    
    print("\n预期结果:")
    print("✅ 数据准备完成时只弹出一次确认对话框")
    print("✅ 不会重复弹出多个对话框")
    print("✅ 用户可以选择立即开始训练或稍后手动开始")
    
    print("\n如果仍有问题:")
    print("- 检查浏览器控制台是否有JavaScript错误")
    print("- 确认页面已刷新，加载了最新的代码")
    print("- 尝试清除浏览器缓存")

def main():
    """主函数"""
    print('🔧 数据准备完成弹窗修复验证')
    print('=' * 80)
    
    # 检查代码修改
    code_check = check_code_changes()
    
    # 测试修复效果
    test_success = test_dialog_fix()
    
    # 创建修复总结
    create_fix_summary()
    
    # 提供手动测试指导
    provide_manual_test_guide()
    
    print(f"\n🎯 验证结果:")
    print(f"   代码修改: {'✅ 成功' if code_check else '❌ 失败'}")
    print(f"   功能测试: {'✅ 成功' if test_success else '⚠️ 需要手动验证'}")
    
    if code_check:
        print(f"\n🎉 弹窗重复问题修复完成！")
        print(f"💡 现在数据准备完成弹窗只会显示一次")
        print(f"🔧 请刷新浏览器页面测试效果")
    else:
        print(f"\n❌ 修复验证失败，请检查代码")

if __name__ == "__main__":
    main()
