#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强特征推理功能
验证模型推理页面的增强特征配置是否正常工作
"""

import requests
import json
import time
from datetime import datetime

def test_enhanced_inference():
    """测试增强特征推理"""
    print("🚀 测试增强特征推理功能")
    print("=" * 60)
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 2. 获取可用模型
        print("\n📋 获取可用模型...")
        response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if response.status_code != 200:
            print(f"❌ 获取模型列表失败: {response.status_code}")
            return False
        
        result = response.json()
        if not result.get('success'):
            print(f"❌ 获取模型列表失败: {result.get('error')}")
            return False
        
        models = result.get('models', [])
        completed_models = [m for m in models if m.get('status') == 'completed']
        
        if not completed_models:
            print("❌ 没有可用的训练完成模型")
            return False
        
        test_model = completed_models[0]
        print(f"✅ 找到可用模型: {test_model['name']} ({test_model['id'][:8]}...)")
        
        # 3. 测试基础推理（不使用增强特征）
        print(f"\n📊 测试基础推理...")
        basic_inference_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'realtime',
            'data_points': 100,
            'use_gpu': True,
            'show_confidence': True,
            'use_enhanced_features': False
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                               json=basic_inference_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 基础推理成功")
                print(f"   预测结果数量: {len(result.get('results', []))}")
                print(f"   使用增强特征: {result.get('use_enhanced_features', False)}")
            else:
                print(f"❌ 基础推理失败: {result.get('error')}")
        else:
            print(f"❌ 基础推理请求失败: {response.status_code}")
        
        # 4. 测试增强特征推理
        print(f"\n🚀 测试增强特征推理...")
        enhanced_inference_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'realtime',
            'data_points': 100,
            'use_gpu': True,
            'show_confidence': True,
            # 增强特征配置
            'use_enhanced_features': True,
            'analyze_feature_importance': True,
            'feature_selection_strategy': 'recommended',
            'selected_features': None
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                               json=enhanced_inference_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 增强特征推理成功")
                print(f"   预测结果数量: {len(result.get('results', []))}")
                print(f"   使用增强特征: {result.get('use_enhanced_features', False)}")
                print(f"   特征选择策略: {result.get('feature_selection_strategy', 'N/A')}")
                print(f"   特征重要性分析: {result.get('analyze_feature_importance', False)}")
                
                # 显示特征重要性结果
                feature_importance = result.get('feature_importance')
                if feature_importance:
                    print(f"   特征重要性数据: {type(feature_importance)} 类型")
                    if isinstance(feature_importance, dict):
                        print(f"   特征重要性条目数: {len(feature_importance)}")
                
            else:
                print(f"❌ 增强特征推理失败: {result.get('error')}")
        else:
            print(f"❌ 增强特征推理请求失败: {response.status_code}")
        
        # 5. 测试动态风险管理
        print(f"\n🛡️ 测试动态风险管理...")
        risk_management_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'realtime',
            'data_points': 100,
            'use_gpu': True,
            'show_confidence': True,
            # 增强特征配置
            'use_enhanced_features': True,
            'analyze_feature_importance': False,
            'feature_selection_strategy': 'recommended',
            # 动态风险管理配置
            'enable_dynamic_risk_management': True,
            'atr_multiplier': 2.5,
            'risk_per_trade': 0.02,
            'min_stop_pips': 15,
            'volatility_multipliers': {
                'low': 1.5,
                'medium': 2.0,
                'high': 3.0
            }
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                               json=risk_management_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 动态风险管理推理成功")
                print(f"   预测结果数量: {len(result.get('results', []))}")
                print(f"   启用动态风险管理: {result.get('enable_dynamic_risk_management', False)}")
                print(f"   ATR倍数: {result.get('atr_multiplier', 'N/A')}")
                print(f"   风险比例: {result.get('risk_per_trade', 'N/A')}")
                print(f"   最小止损: {result.get('min_stop_pips', 'N/A')} pips")
                
            else:
                print(f"❌ 动态风险管理推理失败: {result.get('error')}")
        else:
            print(f"❌ 动态风险管理推理请求失败: {response.status_code}")
        
        # 6. 测试自定义特征选择
        print(f"\n🎯 测试自定义特征选择...")
        custom_features_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'realtime',
            'data_points': 100,
            'use_gpu': True,
            'show_confidence': True,
            # 自定义特征配置
            'use_enhanced_features': True,
            'analyze_feature_importance': True,
            'feature_selection_strategy': 'custom',
            'selected_features': [
                'bb_percent_b', 'bb_squeeze', 'bb_breakout',
                'atr_atr', 'atr_ratio', 'atr_low_volatility',
                'stoch_stoch_k', 'stoch_overbought', 'stoch_oversold',
                'combined_squeeze_low_vol', 'combined_breakout_confirmed'
            ]
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                               json=custom_features_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 自定义特征选择推理成功")
                print(f"   预测结果数量: {len(result.get('results', []))}")
                print(f"   特征选择策略: {result.get('feature_selection_strategy', 'N/A')}")
                print(f"   自定义特征数量: {len(result.get('selected_features', []))}")
                
            else:
                print(f"❌ 自定义特征选择推理失败: {result.get('error')}")
        else:
            print(f"❌ 自定义特征选择推理请求失败: {response.status_code}")
        
        print(f"\n🎉 增强特征推理测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🧪 增强特征推理功能测试")
    print("=" * 80)
    
    print("📋 测试内容:")
    print("• 基础推理功能")
    print("• 增强特征推理")
    print("• 特征重要性分析")
    print("• 动态风险管理")
    print("• 自定义特征选择")
    print()
    
    success = test_enhanced_inference()
    
    if success:
        print("\n✅ 所有测试通过！")
        print("💡 增强特征推理功能正常工作")
        print("🎯 用户现在可以在模型推理页面使用以下新功能:")
        print("   • 启用/禁用增强特征")
        print("   • 选择特征选择策略")
        print("   • 查看特征重要性分析")
        print("   • 配置动态风险管理")
        print("   • 自定义特征组合")
    else:
        print("\n❌ 测试失败")
        print("🔧 请检查系统配置和服务状态")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
