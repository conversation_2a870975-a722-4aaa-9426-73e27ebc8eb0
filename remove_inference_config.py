#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全删除推理配置区域，保留实盘交易功能
"""

import re

def remove_inference_config():
    """删除推理配置区域"""
    print("🔧 删除推理配置区域，保留实盘交易功能")
    print("=" * 50)
    
    try:
        # 读取文件
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 原始文件大小: {len(content):,} 字符")
        
        # 找到推理配置区域的开始和结束
        # 开始：<!-- 推理配置 --> 或者相关的div
        # 结束：<!-- 模型信息和状态 --> 之前
        
        # 方法1：使用正则表达式删除整个推理配置区域
        # 从 "<!-- 推理配置 -->" 开始到 "<!-- 模型信息和状态 -->" 之前
        
        # 先找到关键标记点
        inference_start = content.find('<!-- 推理配置 -->')
        model_info_start = content.find('<!-- 模型信息和状态 -->')
        
        if inference_start == -1:
            print("⚠️ 未找到推理配置开始标记")
            # 尝试其他方式找到开始位置
            inference_start = content.find('<div class="col-xl-8 col-lg-7">')
        
        if model_info_start == -1:
            print("⚠️ 未找到模型信息开始标记")
            return False
        
        print(f"🔍 推理配置开始位置: {inference_start}")
        print(f"🔍 模型信息开始位置: {model_info_start}")
        
        if inference_start == -1 or model_info_start == -1:
            print("❌ 无法找到关键标记点")
            return False
        
        # 构建新内容
        # 保留开始部分（到推理配置之前）
        before_inference = content[:inference_start]
        
        # 保留结束部分（从模型信息开始）
        after_inference = content[model_info_start:]
        
        # 构建新的内容，直接连接到模型信息区域
        new_content = before_inference + """    <!-- 推理配置区域已删除，专注于实盘交易功能 -->
    
    <div class="row">
        """ + after_inference
        
        print(f"📄 新文件大小: {len(new_content):,} 字符")
        print(f"📊 删除了: {len(content) - len(new_content):,} 字符")
        
        # 写入新内容
        with open('templates/model_inference.html', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 推理配置区域删除成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 删除过程中出现异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def update_javascript_functions():
    """更新JavaScript函数，使其使用交易配置而不是推理配置"""
    print(f"\n🔧 更新JavaScript函数...")
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修改getInferenceConfig函数，使其使用交易配置的参数
        old_get_inference_config = '''// 获取推理配置
function getInferenceConfig() {
    return {
        trade_size: parseFloat(document.getElementById('inferenceTradeSize').value),
        min_confidence: parseFloat(document.getElementById('inferenceMinConfidence').value),
        stop_loss_pips: parseInt(document.getElementById('inferenceStopLoss').value),
        take_profit_pips: parseInt(document.getElementById('inferenceTakeProfit').value),
        trade_mode: document.getElementById('inferenceTradeMode').value,
        dynamic_sl: document.getElementById('inferenceDynamicSL').checked,
        trailing_stop: document.getElementById('inferenceTrailingStop').checked
    };
}'''
        
        new_get_inference_config = '''// 获取推理配置（现在使用交易配置的参数）
function getInferenceConfig() {
    return {
        trade_size: parseFloat(document.getElementById('tradingLotSize').value || '0.01'),
        min_confidence: parseFloat(document.getElementById('minConfidence').value || '0.7'),
        stop_loss_pips: parseInt(document.getElementById('stopLossPips').value || '50'),
        take_profit_pips: parseInt(document.getElementById('takeProfitPips').value || '100'),
        trade_mode: 'auto', // 固定为自动模式
        dynamic_sl: document.getElementById('enableDynamicSL').checked,
        trailing_stop: document.getElementById('enableTrailingStop').checked
    };
}'''
        
        # 替换函数
        if old_get_inference_config in content:
            content = content.replace(old_get_inference_config, new_get_inference_config)
            print("✅ 更新了getInferenceConfig函数")
        else:
            print("⚠️ 未找到getInferenceConfig函数的完整匹配")
            # 尝试部分替换
            if 'function getInferenceConfig()' in content:
                # 使用正则表达式替换
                pattern = r'function getInferenceConfig\(\)\s*\{[^}]*\{[^}]*\}[^}]*\}'
                replacement = '''function getInferenceConfig() {
    return {
        trade_size: parseFloat(document.getElementById('tradingLotSize').value || '0.01'),
        min_confidence: parseFloat(document.getElementById('minConfidence').value || '0.7'),
        stop_loss_pips: parseInt(document.getElementById('stopLossPips').value || '50'),
        take_profit_pips: parseInt(document.getElementById('takeProfitPips').value || '100'),
        trade_mode: 'auto',
        dynamic_sl: document.getElementById('enableDynamicSL').checked,
        trailing_stop: document.getElementById('enableTrailingStop').checked
    };
}'''
                content = re.sub(pattern, replacement, content, flags=re.DOTALL)
                print("✅ 使用正则表达式更新了getInferenceConfig函数")
        
        # 删除不需要的推理相关函数
        functions_to_remove = [
            'toggleInferenceConfig',
            'applyInferencePreset',
            'toggleTimeRangeVisibility',
            'applyTimeRangePreset'
        ]
        
        for func_name in functions_to_remove:
            # 查找并删除函数
            pattern = rf'function {func_name}\([^)]*\)\s*\{{[^{{}}]*(?:\{{[^{{}}]*\}}[^{{}}]*)*\}}'
            matches = re.findall(pattern, content, re.DOTALL)
            if matches:
                content = re.sub(pattern, '', content, flags=re.DOTALL)
                print(f"✅ 删除了{func_name}函数")
            else:
                print(f"⚠️ 未找到{func_name}函数")
        
        # 写入更新后的内容
        with open('templates/model_inference.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ JavaScript函数更新完成")
        return True
        
    except Exception as e:
        print(f"❌ 更新JavaScript函数失败: {e}")
        return False

def verify_cleanup():
    """验证清理结果"""
    print(f"\n🔍 验证清理结果...")
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有推理配置相关的元素
        inference_elements = [
            'id="modelSelect"',
            'id="symbol"',
            'id="timeframe"',
            'id="dataPoints"',
            'id="inferenceMode"',
            'id="startDate"',
            'id="endDate"',
            'id="useGPU"',
            'id="showConfidence"',
            'id="startInferenceBtn"'
        ]
        
        removed_elements = []
        remaining_elements = []
        
        for element in inference_elements:
            if element in content:
                remaining_elements.append(element)
            else:
                removed_elements.append(element)
        
        print(f"✅ 已删除的元素: {len(removed_elements)}")
        for element in removed_elements:
            print(f"   - {element}")
        
        if remaining_elements:
            print(f"⚠️ 仍存在的元素: {len(remaining_elements)}")
            for element in remaining_elements:
                print(f"   - {element}")
        
        # 检查关键的交易功能是否保留
        trading_elements = [
            'id="tradingLotSize"',
            'id="minConfidence"',
            'id="stopLossPips"',
            'id="takeProfitPips"',
            'id="startTradingBtn"',
            'id="enableAutoTrading"'
        ]
        
        preserved_elements = []
        for element in trading_elements:
            if element in content:
                preserved_elements.append(element)
        
        print(f"✅ 保留的交易元素: {len(preserved_elements)}")
        for element in preserved_elements:
            print(f"   - {element}")
        
        # 统计页面结构
        div_count = content.count('<div')
        div_close_count = content.count('</div>')
        card_count = content.count('class="card')
        
        print(f"\n📊 页面结构统计:")
        print(f"   • 文件大小: {len(content):,} 字符")
        print(f"   • div标签: {div_count} 开始, {div_close_count} 结束")
        print(f"   • 卡片数量: {card_count}")
        
        if div_count == div_close_count:
            print("✅ div标签匹配")
        else:
            print(f"⚠️ div标签不匹配，差异: {div_count - div_close_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始清理推理配置区域...")
    
    # 1. 删除推理配置区域
    if remove_inference_config():
        print("✅ 推理配置区域删除成功")
        
        # 2. 更新JavaScript函数
        if update_javascript_functions():
            print("✅ JavaScript函数更新成功")
            
            # 3. 验证清理结果
            if verify_cleanup():
                print("\n🎉 推理配置清理完成！")
                print("📋 清理总结:")
                print("   • 删除了推理配置UI区域")
                print("   • 更新了JavaScript函数使用交易配置")
                print("   • 保留了所有实盘交易功能")
                print("   • 页面结构保持完整")
                print("\n🔄 请重启应用并测试功能")
            else:
                print("⚠️ 验证过程中发现问题")
        else:
            print("❌ JavaScript函数更新失败")
    else:
        print("❌ 推理配置区域删除失败")
