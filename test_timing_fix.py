#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时序修复效果
验证数据准备完成后再启动训练
"""

import requests
import time
import json

def test_timing_fix():
    """测试时序修复效果"""
    print('🧪 测试数据准备和训练启动时序修复')
    print('=' * 60)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 创建时序修复测试任务
    print("\n📊 创建时序修复测试任务...")
    
    timing_fix_config = {
        'model_name': f'时序修复测试_{int(time.time())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'data_config': {'mode': 'days', 'training_days': 30},
        'sequence_length': 10,
        'hidden_size': 16,
        'num_layers': 1,
        'dropout': 0.2,
        'batch_size': 4,
        'learning_rate': 0.001,
        'epochs': 10,
        'patience': 3,
        'early_stopping': True,
        'min_epochs': 2,
        'use_gpu': False,
        'save_checkpoints': True,
        'use_enhanced_features': True,
        'feature_selection_strategy': 'minimal',  # 使用最小特征集
        'include_basic_features': True,
        'analyze_feature_importance': False,
        'auto_start_training': True,  # 启用自动启动测试修复效果
    }
    
    try:
        response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                               json=timing_fix_config,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 时序修复测试任务创建成功: {task_id}")
                
                # 监控数据准备和训练启动过程
                print("⏳ 监控数据准备和训练启动过程...")
                
                max_wait_time = 300  # 最多等待5分钟
                start_time = time.time()
                data_ready_time = None
                training_start_time = None
                
                while time.time() - start_time < max_wait_time:
                    # 检查任务状态
                    progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                    
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        if progress_data.get('success'):
                            progress = progress_data.get('progress', {})
                            status = progress.get('status', 'unknown')
                            progress_percent = progress.get('progress', 0)
                            
                            current_time = time.time() - start_time
                            print(f"   [{current_time:6.1f}s] 状态: {status}, 进度: {progress_percent}%")
                            
                            # 记录关键时间点
                            if status == 'data_ready' and data_ready_time is None:
                                data_ready_time = current_time
                                print(f"   🎯 数据准备完成时间: {data_ready_time:.1f}秒")
                                print(f"   ⏳ 等待自动启动训练 (延迟10秒)...")
                            
                            elif status == 'running' and training_start_time is None:
                                training_start_time = current_time
                                print(f"   🚀 训练启动时间: {training_start_time:.1f}秒")
                                
                                if data_ready_time is not None:
                                    delay_time = training_start_time - data_ready_time
                                    print(f"   ⏱️ 实际延迟时间: {delay_time:.1f}秒")
                                    
                                    if delay_time >= 8:  # 至少8秒延迟
                                        print(f"   ✅ 时序修复成功！延迟时间合理")
                                    else:
                                        print(f"   ⚠️ 延迟时间可能仍然太短")
                                
                                # 观察训练是否正常进行
                                print(f"   📊 观察训练是否正常进行...")
                                time.sleep(10)  # 等待10秒观察训练
                                
                                # 再次检查状态
                                final_check = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                                if final_check.status_code == 200:
                                    final_data = final_check.json()
                                    if final_data.get('success'):
                                        final_progress = final_data.get('progress', {})
                                        final_status = final_progress.get('status', 'unknown')
                                        final_percent = final_progress.get('progress', 0)
                                        
                                        if final_status == 'running' and final_percent > 25:
                                            print(f"   🎉 训练正常进行！进度: {final_percent}%")
                                            return True
                                        elif final_status == 'running' and final_percent == 25:
                                            print(f"   ⚠️ 训练仍卡在25%，可能还有其他问题")
                                            return False
                                        else:
                                            print(f"   ❌ 训练状态异常: {final_status} ({final_percent}%)")
                                            return False
                                
                            elif status == 'failed':
                                print(f"   ❌ 任务失败")
                                return False
                            
                            # 继续等待
                            time.sleep(3)
                        else:
                            print(f"   ❌ 获取进度失败: {progress_data.get('error')}")
                            return False
                    else:
                        print(f"   ❌ 进度请求失败: {progress_response.status_code}")
                        return False
                
                print(f"⏰ 等待超时")
                return False
                
            else:
                print(f"❌ 任务创建失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_manual_timing():
    """测试手动控制时序"""
    print('\n🧪 测试手动控制时序')
    print('=' * 60)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 创建手动控制测试任务
    print("\n📊 创建手动控制测试任务...")
    
    manual_config = {
        'model_name': f'手动时序测试_{int(time.time())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'data_config': {'mode': 'days', 'training_days': 30},
        'sequence_length': 10,
        'hidden_size': 16,
        'num_layers': 1,
        'dropout': 0.2,
        'batch_size': 4,
        'learning_rate': 0.001,
        'epochs': 10,
        'patience': 3,
        'early_stopping': True,
        'min_epochs': 2,
        'use_gpu': False,
        'save_checkpoints': True,
        'use_enhanced_features': True,
        'feature_selection_strategy': 'minimal',
        'include_basic_features': True,
        'analyze_feature_importance': False,
        'auto_start_training': False,  # 禁用自动启动
    }
    
    try:
        response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                               json=manual_config,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 手动控制测试任务创建成功: {task_id}")
                
                # 等待数据准备完成
                print("⏳ 等待数据准备完成...")
                
                max_wait_time = 180  # 最多等待3分钟
                start_time = time.time()
                
                while time.time() - start_time < max_wait_time:
                    progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                    
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        if progress_data.get('success'):
                            progress = progress_data.get('progress', {})
                            status = progress.get('status', 'unknown')
                            progress_percent = progress.get('progress', 0)
                            
                            current_time = time.time() - start_time
                            print(f"   [{current_time:6.1f}s] 状态: {status}, 进度: {progress_percent}%")
                            
                            if status == 'data_ready' and progress_percent >= 100:
                                print(f"   ✅ 数据准备完成！")
                                print(f"   ⏳ 等待额外15秒确保数据完全准备好...")
                                time.sleep(15)  # 额外等待15秒
                                
                                # 手动启动训练
                                print(f"   🚀 手动启动模型训练...")
                                train_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/start-model-training/{task_id}',
                                                            headers={'Content-Type': 'application/json'})
                                
                                if train_response.status_code == 200:
                                    train_result = train_response.json()
                                    if train_result.get('success'):
                                        print(f"   ✅ 手动启动成功！")
                                        
                                        # 观察训练进度
                                        time.sleep(10)
                                        final_check = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                                        if final_check.status_code == 200:
                                            final_data = final_check.json()
                                            if final_data.get('success'):
                                                final_progress = final_data.get('progress', {})
                                                final_percent = final_progress.get('progress', 0)
                                                print(f"   📊 训练进度: {final_percent}%")
                                                
                                                if final_percent > 25:
                                                    print(f"   🎉 手动时序控制成功！")
                                                    return True
                                                else:
                                                    print(f"   ⚠️ 仍卡在25%，可能有其他问题")
                                                    return False
                                    else:
                                        print(f"   ❌ 手动启动失败: {train_result.get('error')}")
                                        return False
                                else:
                                    print(f"   ❌ 启动请求失败: {train_response.status_code}")
                                    return False
                                    
                            elif status == 'failed':
                                print(f"   ❌ 数据准备失败")
                                return False
                            
                            time.sleep(3)
                        else:
                            print(f"   ❌ 获取进度失败: {progress_data.get('error')}")
                            return False
                    else:
                        print(f"   ❌ 进度请求失败: {progress_response.status_code}")
                        return False
                
                print(f"⏰ 等待超时")
                return False
                
            else:
                print(f"❌ 任务创建失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print('🔧 时序修复效果测试')
    print('=' * 80)
    
    # 测试自动启动时序修复
    auto_success = test_timing_fix()
    
    # 测试手动控制时序
    manual_success = test_manual_timing()
    
    print(f"\n🎯 时序修复测试结果:")
    print(f"   自动启动时序修复: {'✅ 成功' if auto_success else '❌ 失败'}")
    print(f"   手动控制时序: {'✅ 成功' if manual_success else '❌ 失败'}")
    
    if auto_success or manual_success:
        print(f"\n🎉 时序问题修复成功！")
        print(f"💡 建议:")
        if auto_success:
            print(f"   - 自动启动延迟修复有效，可以继续使用")
        if manual_success:
            print(f"   - 手动控制时序更可靠，推荐使用")
        print(f"   - 数据准备完成后等待足够时间再启动训练")
    else:
        print(f"\n❌ 时序问题仍然存在")
        print(f"🔧 建议进一步调查其他原因")

if __name__ == "__main__":
    main()
