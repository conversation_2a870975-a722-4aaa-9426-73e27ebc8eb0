
def minimal_train_test():
    """最小化训练测试"""
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    import numpy as np
    
    print("🧪 开始最小化训练测试...")
    
    # 创建虚拟数据
    X = torch.randn(100, 10, 5)  # 100个样本，10个时间步，5个特征
    y = torch.randn(100)         # 100个标签
    
    dataset = TensorDataset(X, y)
    dataloader = DataLoader(dataset, batch_size=4, shuffle=True, num_workers=0)
    
    # 创建简单模型
    class SimpleModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.lstm = nn.LSTM(5, 16, 1, batch_first=True)
            self.fc = nn.Linear(16, 1)
        
        def forward(self, x):
            out, _ = self.lstm(x)
            out = self.fc(out[:, -1, :])
            return out.squeeze()
    
    model = SimpleModel()
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    print("📊 开始训练循环...")
    
    try:
        for epoch in range(2):  # 只训练2轮
            print(f"   Epoch {epoch + 1}/2")
            
            for batch_idx, (batch_X, batch_y) in enumerate(dataloader):
                print(f"     批次 {batch_idx + 1}/{len(dataloader)}")
                
                # 前向传播
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                print(f"       损失: {loss.item():.4f}")
                
                # 每5个批次输出一次
                if batch_idx >= 4:  # 只处理前5个批次
                    break
            
            print(f"   ✅ Epoch {epoch + 1} 完成")
        
        print("🎉 最小化训练测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 最小化训练测试失败: {e}")
        return False

if __name__ == "__main__":
    minimal_train_test()
