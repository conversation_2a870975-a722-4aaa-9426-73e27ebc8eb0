#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动增强特征配置功能
"""

def test_enhanced_model_detection():
    """测试增强特征模型检测逻辑"""
    
    print("🧪 测试增强特征模型检测逻辑")
    print("=" * 60)
    
    # 模拟不同类型的模型
    test_models = [
        {
            'name': 'xau-0804-LSTM-增强-1Y-10技术-高精度',
            'input_size': 18,
            'config': '{"use_enhanced_features": true, "feature_selection_strategy": "minimal"}',
            'expected': True,
            'expected_strategy': 'minimal'
        },
        {
            'name': 'xau-enhanced-features-model',
            'input_size': 34,
            'config': '{"use_enhanced_features": true, "feature_selection_strategy": "recommended"}',
            'expected': True,
            'expected_strategy': 'recommended'
        },
        {
            'name': 'xau-basic-lstm-model',
            'input_size': 8,
            'config': '{"use_enhanced_features": false}',
            'expected': False,
            'expected_strategy': 'recommended'
        },
        {
            'name': 'xau-large-feature-model',
            'input_size': 60,
            'config': None,
            'expected': True,
            'expected_strategy': 'enhanced'
        },
        {
            'name': 'xau-特征-model',
            'input_size': 25,
            'config': None,
            'expected': True,
            'expected_strategy': 'recommended'
        }
    ]
    
    for i, model in enumerate(test_models, 1):
        print(f"\n📊 测试模型 {i}: {model['name']}")
        print(f"   输入特征数: {model['input_size']}")
        print(f"   配置: {model['config']}")
        
        # 模拟JavaScript检测逻辑
        is_enhanced = detect_enhanced_model(model)
        strategy = get_feature_strategy(model)
        
        print(f"   检测结果: {'增强模型' if is_enhanced else '普通模型'}")
        print(f"   推荐策略: {strategy}")
        print(f"   期望结果: {'增强模型' if model['expected'] else '普通模型'}")
        print(f"   期望策略: {model['expected_strategy']}")
        
        # 验证结果
        if is_enhanced == model['expected'] and strategy == model['expected_strategy']:
            print(f"   ✅ 检测正确")
        else:
            print(f"   ❌ 检测错误")

def detect_enhanced_model(model):
    """模拟JavaScript的增强模型检测逻辑"""
    
    # 检查模型名称中是否包含增强特征关键词
    name_keywords = ['增强', 'enhanced', 'feature', '特征']
    model_name = (model.get('name', '')).lower()
    
    has_enhanced_keyword = any(keyword.lower() in model_name for keyword in name_keywords)
    
    # 检查模型配置中的增强特征设置
    config_has_enhanced = False
    if model.get('config'):
        try:
            import json
            config = json.loads(model['config'])
            config_has_enhanced = config.get('use_enhanced_features', False)
        except:
            pass
    
    # 检查特征数量（增强特征模型通常有更多特征）
    has_more_features = (model.get('input_size', 0)) > 8
    
    is_enhanced = has_enhanced_keyword or config_has_enhanced or has_more_features
    
    return is_enhanced

def get_feature_strategy(model):
    """模拟JavaScript的特征策略获取逻辑"""
    
    # 优先从模型配置中获取
    if model.get('config'):
        try:
            import json
            config = json.loads(model['config'])
            if config.get('feature_selection_strategy'):
                return config['feature_selection_strategy']
        except:
            pass
    
    # 根据特征数量推断策略
    input_size = model.get('input_size', 0)
    if input_size == 18:
        return 'minimal'      # 8基础 + 10增强 = 18
    elif input_size == 34:
        return 'recommended'  # 8基础 + 26增强 = 34
    elif input_size == 60:
        return 'enhanced'     # 8基础 + 52增强 = 60
    else:
        return 'recommended'  # 默认推荐策略

def test_strategy_mapping():
    """测试策略映射"""
    
    print(f"\n🧪 测试策略映射")
    print("=" * 60)
    
    strategy_info = {
        'minimal': {'features': 18, 'description': '8基础 + 10增强'},
        'recommended': {'features': 34, 'description': '8基础 + 26增强'},
        'enhanced': {'features': 60, 'description': '8基础 + 52增强'},
        'top_importance': {'features': 23, 'description': '8基础 + 15重要'},
        'custom': {'features': '变动', 'description': '用户自定义'}
    }
    
    print("📋 特征策略映射表:")
    print("-" * 60)
    print(f"{'策略':<15} {'特征数':<8} {'描述':<20}")
    print("-" * 60)
    
    for strategy, info in strategy_info.items():
        print(f"{strategy:<15} {str(info['features']):<8} {info['description']:<20}")
    
    print(f"\n💡 自动配置逻辑:")
    print(f"   1. 检测模型名称中的关键词：增强、enhanced、feature、特征")
    print(f"   2. 检查模型配置中的 use_enhanced_features 设置")
    print(f"   3. 检查特征数量是否大于8（基础特征数）")
    print(f"   4. 根据特征数量自动推断最适合的策略")
    print(f"   5. 自动启用增强特征并设置对应策略")

def main():
    """主函数"""
    
    print("🚀 自动增强特征配置功能测试")
    print("=" * 80)
    
    # 测试增强模型检测
    test_enhanced_model_detection()
    
    # 测试策略映射
    test_strategy_mapping()
    
    print(f"\n🎉 测试完成!")
    print(f"💡 功能说明:")
    print(f"   ✅ 当选择增强特征模型时，系统会自动:")
    print(f"      1. 启用增强特征选项")
    print(f"      2. 根据模型配置设置最适合的特征策略")
    print(f"      3. 显示配置成功的通知")
    print(f"   ✅ 当选择普通模型时，保持用户当前的增强特征设置")
    print(f"   ✅ 支持多种检测方式：名称关键词、配置信息、特征数量")

if __name__ == "__main__":
    main()
