#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI推理交易页面的增强特征配置一致性
验证与模型训练页面的特征选择策略是否一致
"""

import requests
import time

def test_inference_page_features():
    """测试推理页面的特征配置"""
    print('🧪 测试AI推理交易页面的增强特征配置')
    print('=' * 60)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 获取推理页面
    try:
        response = session.get('http://127.0.0.1:5000/model-inference')
        
        if response.status_code == 200:
            print("✅ 成功访问AI推理交易页面")
            
            # 检查页面内容
            content = response.text
            
            # 检查增强特征相关元素
            feature_elements = [
                'enableEnhancedFeatures',
                'enhancedFeaturesDetails',
                'featureSelectionStrategy',
                'analyzeFeatureImportance',
                'customFeaturesArea'
            ]
            
            print("\n📋 检查页面元素:")
            for element in feature_elements:
                if element in content:
                    print(f"   ✅ 找到元素: {element}")
                else:
                    print(f"   ❌ 缺失元素: {element}")
            
            # 检查特征选择策略选项
            strategies = [
                'minimal',
                'recommended',
                'enhanced',
                'top_importance',
                'custom'
            ]
            
            print("\n📋 检查特征选择策略:")
            for strategy in strategies:
                if f'value="{strategy}"' in content:
                    print(f"   ✅ 找到策略: {strategy}")
                else:
                    print(f"   ❌ 缺失策略: {strategy}")
            
            # 检查JavaScript函数
            js_functions = [
                'toggleEnhancedFeaturesDetails',
                'handleFeatureStrategyChange',
                'getEnhancedFeaturesConfig'
            ]
            
            print("\n📋 检查JavaScript函数:")
            for func in js_functions:
                if func in content:
                    print(f"   ✅ 找到函数: {func}")
                else:
                    print(f"   ❌ 缺失函数: {func}")
            
            return True
            
        else:
            print(f"❌ 访问推理页面失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def compare_with_training_page():
    """与模型训练页面对比"""
    print('\n🔍 与模型训练页面对比')
    print('=' * 60)
    
    session = requests.Session()
    try:
        # 登录
        session.post('http://127.0.0.1:5000/login', 
                    data={'username': 'admin', 'password': 'admin123'})
        
        # 获取训练页面
        training_response = session.get('http://127.0.0.1:5000/model-training')
        # 获取推理页面
        inference_response = session.get('http://127.0.0.1:5000/model-inference')
        
        if training_response.status_code == 200 and inference_response.status_code == 200:
            training_content = training_response.text
            inference_content = inference_response.text
            
            # 提取特征策略选项
            import re
            
            # 从训练页面提取策略
            training_pattern = r'<option value="([^"]+)"[^>]*>([^<]+)</option>'
            training_matches = re.findall(training_pattern, training_content)
            training_strategies = {value: text.strip() for value, text in training_matches 
                                 if any(keyword in text.lower() for keyword in ['特征', '指标', 'feature'])}
            
            # 从推理页面提取策略
            inference_matches = re.findall(training_pattern, inference_content)
            inference_strategies = {value: text.strip() for value, text in inference_matches 
                                  if any(keyword in text.lower() for keyword in ['特征', '指标', 'feature'])}
            
            print("📊 特征策略对比:")
            print(f"   训练页面策略数量: {len(training_strategies)}")
            print(f"   推理页面策略数量: {len(inference_strategies)}")
            
            # 检查一致性
            all_strategies = set(training_strategies.keys()) | set(inference_strategies.keys())
            
            consistent = True
            for strategy in sorted(all_strategies):
                training_text = training_strategies.get(strategy, '❌ 缺失')
                inference_text = inference_strategies.get(strategy, '❌ 缺失')
                
                if training_text == inference_text and training_text != '❌ 缺失':
                    print(f"   ✅ {strategy}: 一致")
                else:
                    print(f"   ❌ {strategy}:")
                    print(f"      训练页面: {training_text}")
                    print(f"      推理页面: {inference_text}")
                    consistent = False
            
            if consistent:
                print("\n🎉 特征策略完全一致！")
            else:
                print("\n⚠️ 发现不一致问题")
            
            return consistent
            
        else:
            print("❌ 无法获取页面内容进行对比")
            return False
            
    except Exception as e:
        print(f"❌ 对比异常: {e}")
        return False

def test_feature_config_functionality():
    """测试特征配置功能"""
    print('\n🧪 测试特征配置功能')
    print('=' * 60)
    
    # 这里可以添加更多的功能测试
    # 比如模拟用户选择不同的特征策略
    
    test_configs = [
        {
            'name': '最小特征集测试',
            'strategy': 'minimal',
            'expected_features': 10
        },
        {
            'name': '推荐特征集测试',
            'strategy': 'recommended',
            'expected_features': 26
        },
        {
            'name': '增强特征集测试',
            'strategy': 'enhanced',
            'expected_features': 52
        },
        {
            'name': '重要性特征测试',
            'strategy': 'top_importance',
            'expected_features': 15
        }
    ]
    
    print("📋 特征配置测试用例:")
    for config in test_configs:
        print(f"   {config['name']}: {config['strategy']} ({config['expected_features']}个特征)")
    
    print("\n💡 功能测试说明:")
    print("   - 用户可以选择与训练时相同的特征策略")
    print("   - 支持自定义特征选择")
    print("   - 提供特征重要性分析选项")
    print("   - 配置会传递到后端推理逻辑")
    
    return True

def provide_usage_guide():
    """提供使用指南"""
    print('\n📖 使用指南')
    print('=' * 60)
    
    guide = """
🎯 AI推理交易页面增强特征使用指南:

1. 📊 启用增强特征:
   - 勾选"增强特征"开关
   - 展开详细配置选项

2. 🔧 选择特征策略:
   - 最小特征集: 适合快速推理
   - 推荐特征集: 平衡性能和效率
   - 增强特征集: 最完整的特征集
   - 重要性前15个: 基于重要性分析
   - 自定义选择: 手动选择特定特征

3. ⚠️ 重要提醒:
   - 必须选择与模型训练时相同的特征策略
   - 特征数量和类型不匹配会导致推理失败
   - 建议记录训练时使用的特征策略

4. 🔍 特征重要性分析:
   - 可选择启用特征重要性分析
   - 分析各特征对预测结果的贡献度
   - 帮助优化特征选择策略

5. 🎨 自定义特征选择:
   - 选择"自定义特征选择"策略
   - 手动勾选需要的技术指标
   - 适合高级用户和特定需求
"""
    
    print(guide)

def main():
    """主函数"""
    print('🔧 AI推理交易页面增强特征配置测试')
    print('=' * 80)
    
    print("🎯 测试目标:")
    print("   验证AI推理交易页面的增强特征配置是否完整")
    print("   确保与模型训练页面的特征选择策略一致")
    
    # 测试推理页面特征配置
    inference_test = test_inference_page_features()
    
    # 与训练页面对比
    consistency_test = compare_with_training_page()
    
    # 测试功能
    functionality_test = test_feature_config_functionality()
    
    # 提供使用指南
    provide_usage_guide()
    
    print(f"\n🎯 测试结果:")
    print(f"   推理页面特征配置: {'✅ 通过' if inference_test else '❌ 失败'}")
    print(f"   与训练页面一致性: {'✅ 通过' if consistency_test else '❌ 失败'}")
    print(f"   功能测试: {'✅ 通过' if functionality_test else '❌ 失败'}")
    
    if all([inference_test, consistency_test, functionality_test]):
        print(f"\n🎉 所有测试通过！")
        print(f"💡 AI推理交易页面的增强特征配置已完善:")
        print(f"   - 提供了完整的特征选择策略选项")
        print(f"   - 与模型训练页面保持一致")
        print(f"   - 支持自定义特征选择")
        print(f"   - 包含特征重要性分析选项")
        
        print(f"\n🚀 现在用户可以:")
        print(f"   - 在推理时选择与训练时相同的特征策略")
        print(f"   - 避免特征维度不匹配的问题")
        print(f"   - 获得更准确的推理结果")
    else:
        print(f"\n⚠️ 部分测试失败，请检查相关问题")

if __name__ == "__main__":
    main()
