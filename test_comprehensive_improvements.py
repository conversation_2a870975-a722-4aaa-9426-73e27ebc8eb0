#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合测试AI推理交易和回测模块的改进
验证所有三个任务的完成情况
"""

import requests
from bs4 import BeautifulSoup
import re

def test_comprehensive_improvements():
    """综合测试所有改进"""
    print("🧪 综合测试AI推理交易和回测模块改进")
    print("=" * 80)
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 2. 测试AI推理交易页面改进
        print(f"\n📋 测试AI推理交易页面改进...")
        success_inference = test_inference_page_improvements(session)
        
        # 3. 测试AI推理回测页面功能
        print(f"\n📋 测试AI推理回测页面功能...")
        success_backtest = test_backtest_page_functionality(session)
        
        # 4. 测试导航栏更新
        print(f"\n📋 测试导航栏更新...")
        success_navigation = test_navigation_updates(session)
        
        return success_inference and success_backtest and success_navigation
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_inference_page_improvements(session):
    """测试AI推理交易页面改进"""
    print("🔍 测试任务1：删除快速操作功能区")
    
    response = session.get('http://127.0.0.1:5000/deep-learning/inference')
    
    if response.status_code != 200:
        print(f"❌ 访问AI推理交易页面失败: {response.status_code}")
        return False
    
    page_content = response.text
    
    # 检查快速操作功能区是否已删除
    quick_actions_indicators = [
        '快速操作',
        'quickActions',
        'loadLatestData',
        'exportResults',
        'compareModels'
    ]
    
    found_quick_actions = []
    for indicator in quick_actions_indicators:
        if indicator in page_content:
            found_quick_actions.append(indicator)
    
    if found_quick_actions:
        print("❌ 快速操作功能区未完全删除，仍包含:")
        for action in found_quick_actions:
            print(f"   • {action}")
        return False
    else:
        print("✅ 快速操作功能区已成功删除")
    
    # 检查回测相关代码是否已清理
    print("🔍 测试任务3：清理回测相关代码")
    
    backtest_indicators = [
        'startBacktest',
        'backtestConfig',
        'parameterOptimization',
        'optimizationResults'
    ]
    
    found_backtest_code = []
    for indicator in backtest_indicators:
        if indicator in page_content:
            found_backtest_code.append(indicator)
    
    if found_backtest_code:
        print("⚠️ 仍有部分回测相关代码未清理:")
        for code in found_backtest_code:
            print(f"   • {code}")
    else:
        print("✅ 回测相关代码已基本清理完成")
    
    return True

def test_backtest_page_functionality(session):
    """测试AI推理回测页面功能"""
    print("🔍 测试任务2：AI推理回测页面功能完善")
    
    response = session.get('http://127.0.0.1:5000/deep-learning/backtest')
    
    if response.status_code != 200:
        print(f"❌ 访问AI推理回测页面失败: {response.status_code}")
        return False
    
    print("✅ AI推理回测页面访问正常")
    
    # 解析HTML检查功能
    soup = BeautifulSoup(response.text, 'html.parser')
    page_content = response.text
    
    # 检查回测配置功能
    required_elements = [
        'backtestConfig',
        'backtestInitialBalance',
        'backtestLotSize',
        'backtestStopLoss',
        'backtestTakeProfit',
        'backtestMinConfidence',
        'enableTrailingStop',
        'backtestCliffBrake',
        'backtestPreset',
        'optimizationPeriod',
        'riskPreference'
    ]
    
    missing_elements = []
    for element in required_elements:
        if element not in page_content:
            missing_elements.append(element)
    
    if missing_elements:
        print("❌ 缺少以下回测配置元素:")
        for element in missing_elements:
            print(f"   • {element}")
        return False
    else:
        print("✅ 回测配置元素完整")
    
    # 检查操作按钮
    required_buttons = [
        'startBacktestBtn',
        'parameterOptimizationBtn',
        'loadSavedResultsBtn'
    ]
    
    missing_buttons = []
    for button in required_buttons:
        if button not in page_content:
            missing_buttons.append(button)
    
    if missing_buttons:
        print("❌ 缺少以下操作按钮:")
        for button in missing_buttons:
            print(f"   • {button}")
        return False
    else:
        print("✅ 操作按钮完整")
    
    # 检查JavaScript功能
    required_functions = [
        'startBacktest',
        'startParameterOptimization',
        'loadSavedOptimizationResults',
        'applyBacktestPreset',
        'toggleTrailingStopConfig'
    ]
    
    missing_functions = []
    for func in required_functions:
        if f'function {func}' not in page_content and f'{func}(' not in page_content:
            missing_functions.append(func)
    
    if missing_functions:
        print("❌ 缺少以下JavaScript函数:")
        for func in missing_functions:
            print(f"   • {func}")
        return False
    else:
        print("✅ JavaScript功能完整")
    
    # 检查外部JavaScript文件引用
    if 'backtest_functions.js' in page_content:
        print("✅ 外部JavaScript文件正确引用")
    else:
        print("⚠️ 外部JavaScript文件可能未正确引用")
    
    return True

def test_navigation_updates(session):
    """测试导航栏更新"""
    print("🔍 测试导航栏更新")
    
    response = session.get('http://127.0.0.1:5000/dashboard')
    
    if response.status_code != 200:
        print(f"❌ 访问首页失败: {response.status_code}")
        return False
    
    # 解析HTML
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # 查找导航栏
    sidebar = soup.find('nav', class_='sidebar')
    if not sidebar:
        print("❌ 未找到导航栏")
        return False
    
    # 提取所有导航链接
    nav_links = sidebar.find_all('a', class_='nav-link')
    
    # 查找AI推理相关的链接
    ai_inference_found = False
    ai_backtest_found = False
    
    for link in nav_links:
        link_text = link.get_text(strip=True)
        href = link.get('href', '')
        
        if 'AI推理交易' in link_text and '实盘' in link_text:
            ai_inference_found = True
            print(f"✅ 找到AI推理交易链接: {link_text} -> {href}")
        elif 'AI推理回测' in link_text and '回测' in link_text:
            ai_backtest_found = True
            print(f"✅ 找到AI推理回测链接: {link_text} -> {href}")
    
    if not ai_inference_found:
        print("❌ 未找到AI推理交易导航链接")
        return False
    
    if not ai_backtest_found:
        print("❌ 未找到AI推理回测导航链接")
        return False
    
    print("✅ 导航栏更新正确")
    return True

def main():
    """主函数"""
    print("🔧 AI推理交易和回测模块综合改进测试工具")
    print("=" * 80)
    
    print("📋 测试任务:")
    print("1. ❌ 删除AI推理交易页面的快速操作功能区")
    print("2. ✨ 完善AI推理回测页面功能（添加参数优化回测）")
    print("3. 🧹 继续清理AI推理交易页面中剩余的回测相关代码")
    print("4. 🎨 优化两个页面的UI设计和用户体验")
    print()
    
    success = test_comprehensive_improvements()
    
    if success:
        print("\n🎉 综合改进测试通过！")
        print("✅ 主要改进成果:")
        print("   • AI推理交易页面更加简洁专业")
        print("   • AI推理回测页面功能完整强大")
        print("   • 两个模块完全独立，功能清晰")
        print("   • 导航结构合理，用户体验优化")
        print()
        print("🎯 用户现在可以:")
        print("   • 在AI推理交易页面专注于实盘交易")
        print("   • 在AI推理回测页面进行策略验证和参数优化")
        print("   • 享受更清晰的界面和更好的用户体验")
    else:
        print("\n❌ 综合改进测试失败")
        print("🔧 请检查相关配置和实施情况")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
