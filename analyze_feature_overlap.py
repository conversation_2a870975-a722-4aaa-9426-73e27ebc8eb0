#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析基础特征和增强特征的重复情况
"""

import numpy as np
import pandas as pd
from services.enhanced_feature_engineering import EnhancedFeatureEngineering

def analyze_basic_features():
    """分析基础特征构成"""
    print('📊 基础特征分析')
    print('=' * 50)
    
    basic_features = {
        'price_features': [
            'open',      # 开盘价
            'high',      # 最高价  
            'low',       # 最低价
            'close',     # 收盘价
            'volume'     # 成交量
        ],
        'technical_indicators': [
            'sma_5',     # 5日简单移动平均
            'sma_20',    # 20日简单移动平均
            'rsi'        # RSI相对强弱指标
        ]
    }
    
    print("🔹 价格特征 (5个):")
    for feature in basic_features['price_features']:
        print(f"   - {feature}")
    
    print("\n🔹 技术指标 (3个):")
    for feature in basic_features['technical_indicators']:
        print(f"   - {feature}")
    
    print(f"\n📋 基础特征总数: {len(basic_features['price_features']) + len(basic_features['technical_indicators'])} 个")
    
    return basic_features

def analyze_enhanced_features():
    """分析增强特征构成"""
    print('\n🚀 增强特征分析')
    print('=' * 50)
    
    # 创建增强特征工程实例
    feature_engineer = EnhancedFeatureEngineering()
    
    # 获取推荐特征列表
    recommended_features = feature_engineer.get_recommended_features()
    
    print(f"📋 推荐增强特征总数: {len(recommended_features)} 个")
    
    # 按类别分组
    feature_categories = {
        'bollinger_bands': [],
        'atr': [],
        'stochastic': [],
        'combined': [],
        'market': [],
        'price': [],
        'momentum': []
    }
    
    for feature in recommended_features:
        if feature.startswith('bb_'):
            feature_categories['bollinger_bands'].append(feature)
        elif feature.startswith('atr_'):
            feature_categories['atr'].append(feature)
        elif feature.startswith('stoch_'):
            feature_categories['stochastic'].append(feature)
        elif feature.startswith('combined_'):
            feature_categories['combined'].append(feature)
        elif feature.startswith('market_'):
            feature_categories['market'].append(feature)
        elif 'close' in feature or 'price' in feature:
            feature_categories['price'].append(feature)
        elif 'momentum' in feature or 'trend' in feature:
            feature_categories['momentum'].append(feature)
    
    for category, features in feature_categories.items():
        if features:
            print(f"\n🔹 {category.replace('_', ' ').title()} ({len(features)}个):")
            for feature in features:
                print(f"   - {feature}")
    
    return recommended_features, feature_categories

def identify_overlaps(basic_features, enhanced_features):
    """识别重复的特征"""
    print('\n⚠️ 特征重复分析')
    print('=' * 50)
    
    # 基础技术指标
    basic_indicators = basic_features['technical_indicators']
    
    # 可能的重复
    potential_overlaps = []
    
    # 检查RSI重复
    if 'rsi' in basic_indicators:
        rsi_in_enhanced = [f for f in enhanced_features if 'rsi' in f.lower()]
        if rsi_in_enhanced:
            potential_overlaps.append({
                'type': 'RSI指标',
                'basic': 'rsi (简化版)',
                'enhanced': rsi_in_enhanced,
                'severity': 'medium'
            })
    
    # 检查移动平均重复
    sma_in_basic = [f for f in basic_indicators if 'sma' in f]
    sma_in_enhanced = [f for f in enhanced_features if 'sma' in f.lower() or 'ma' in f.lower()]
    if sma_in_basic and sma_in_enhanced:
        potential_overlaps.append({
            'type': '移动平均',
            'basic': sma_in_basic,
            'enhanced': sma_in_enhanced,
            'severity': 'low'
        })
    
    # 检查价格相关重复
    price_in_enhanced = [f for f in enhanced_features if 'close' in f or 'price' in f]
    if price_in_enhanced:
        potential_overlaps.append({
            'type': '价格特征',
            'basic': basic_features['price_features'],
            'enhanced': price_in_enhanced,
            'severity': 'low'
        })
    
    if potential_overlaps:
        print("🔍 发现的潜在重复:")
        for overlap in potential_overlaps:
            severity_icon = '🔴' if overlap['severity'] == 'high' else '🟡' if overlap['severity'] == 'medium' else '🟢'
            print(f"\n{severity_icon} {overlap['type']} (严重程度: {overlap['severity']})")
            print(f"   基础特征: {overlap['basic']}")
            print(f"   增强特征: {overlap['enhanced']}")
    else:
        print("✅ 未发现明显的特征重复")
    
    return potential_overlaps

def recommend_solutions(overlaps):
    """推荐解决方案"""
    print('\n🔧 解决方案建议')
    print('=' * 50)
    
    if not overlaps:
        print("✅ 当前特征配置良好，无需特殊处理")
        return
    
    print("📋 建议的处理策略:")
    
    for i, overlap in enumerate(overlaps, 1):
        print(f"\n{i}. {overlap['type']}重复处理:")
        
        if overlap['type'] == 'RSI指标':
            print("   💡 建议方案:")
            print("   - 保持基础特征中的简化RSI（兼容性）")
            print("   - 增强特征中使用更精确的RSI计算")
            print("   - 在特征选择时避免同时使用两个RSI")
            
        elif overlap['type'] == '移动平均':
            print("   💡 建议方案:")
            print("   - 基础特征保留SMA_5, SMA_20")
            print("   - 增强特征使用EMA或其他周期的MA")
            print("   - 通过特征重要性分析选择最优组合")
            
        elif overlap['type'] == '价格特征':
            print("   💡 建议方案:")
            print("   - 基础特征保留原始OHLCV")
            print("   - 增强特征使用价格的衍生指标（如收益率、位置等）")
            print("   - 两者互补而非重复")

def create_optimized_feature_config():
    """创建优化的特征配置"""
    print('\n🎯 优化特征配置建议')
    print('=' * 50)
    
    optimized_config = {
        'basic_features': {
            'description': '8个基础特征（必选，保证兼容性）',
            'features': [
                'open', 'high', 'low', 'close', 'volume',  # 5个价格特征
                'sma_5', 'sma_20', 'rsi'                   # 3个技术指标
            ],
            'always_included': True
        },
        'enhanced_features': {
            'description': '增强特征（可选，避免与基础特征重复）',
            'recommended_strategy': {
                'name': '推荐增强特征（去重版）',
                'features': [
                    # 布林带特征（基础特征中没有）
                    'bb_percent_b', 'bb_band_width', 'bb_squeeze', 'bb_breakout',
                    
                    # ATR特征（基础特征中没有）
                    'atr_atr', 'atr_ratio', 'atr_low_volatility', 'atr_high_volatility',
                    
                    # 随机指标（基础特征中没有）
                    'stoch_stoch_k', 'stoch_stoch_d', 'stoch_overbought', 'stoch_oversold',
                    
                    # 组合信号（基础特征中没有）
                    'combined_squeeze_low_vol', 'combined_breakout_confirmed',
                    'combined_bullish_confluence', 'combined_bearish_confluence',
                    
                    # 市场状态（基础特征中没有）
                    'market_trend_strength', 'market_trending_market',
                    
                    # 价格衍生特征（与基础价格特征互补）
                    'close_returns', 'close_position', 'price_gap',
                    
                    # 动量特征（与基础SMA互补）
                    'momentum_5', 'momentum_10', 'momentum_20', 'trend_strength'
                ],
                'excluded_features': [
                    # 排除与基础特征重复的指标
                    'rsi',  # 基础特征中已有
                    'sma_5', 'sma_20',  # 基础特征中已有
                    'close', 'open', 'high', 'low', 'volume'  # 基础特征中已有
                ]
            }
        },
        'combination_strategy': {
            'mode_1_basic_only': {
                'description': '仅使用基础特征（兼容现有模型）',
                'feature_count': 8,
                'use_enhanced_features': False
            },
            'mode_2_basic_plus_enhanced': {
                'description': '基础特征 + 去重增强特征',
                'feature_count': '8 + 26 = 34',
                'use_enhanced_features': True,
                'strategy': 'recommended_deduplicated'
            },
            'mode_3_enhanced_only': {
                'description': '仅使用增强特征（新模型）',
                'feature_count': 52,
                'use_enhanced_features': True,
                'strategy': 'all',
                'note': '不包含基础特征，需要重新训练'
            }
        }
    }
    
    print("📊 推荐的特征配置策略:")
    
    for mode, config in optimized_config['combination_strategy'].items():
        print(f"\n🔹 {config['description']}")
        print(f"   特征数量: {config['feature_count']}")
        if 'note' in config:
            print(f"   注意: {config['note']}")
    
    print(f"\n💡 最佳实践建议:")
    print(f"   1. 新用户: 使用模式2（基础+增强去重）")
    print(f"   2. 现有模型: 继续使用模式1（仅基础）")
    print(f"   3. 高级用户: 尝试模式3（仅增强）")
    
    return optimized_config

def main():
    """主函数"""
    print('🔍 基础特征与增强特征重复分析')
    print('=' * 80)
    
    # 分析基础特征
    basic_features = analyze_basic_features()
    
    # 分析增强特征
    enhanced_features, enhanced_categories = analyze_enhanced_features()
    
    # 识别重复
    overlaps = identify_overlaps(basic_features, enhanced_features)
    
    # 推荐解决方案
    recommend_solutions(overlaps)
    
    # 创建优化配置
    optimized_config = create_optimized_feature_config()
    
    print(f"\n✅ 分析完成！")
    print(f"📋 关键发现:")
    print(f"   - 基础特征: 8个（5个价格 + 3个技术指标）")
    print(f"   - 增强特征: {len(enhanced_features)}个（推荐集）")
    print(f"   - 潜在重复: {len(overlaps)}个")
    print(f"   - 建议策略: 基础特征作为必选项，增强特征去重后可选")

if __name__ == "__main__":
    main()
