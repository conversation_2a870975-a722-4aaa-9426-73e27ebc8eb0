#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新训练增强特征模型
使用修复后的推理逻辑和正确的特征配置
"""

import os
import sys
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_enhanced_training_config():
    """创建增强特征训练配置"""
    
    print("🚀 创建增强特征模型训练配置")
    print("=" * 60)
    
    # 训练配置
    training_config = {
        "model_name": "XAU-H1-2Y-增强特征-修复版",
        "model_type": "cnn_lstm",  # CNN-LSTM混合模型
        "symbol": "XAUUSD",
        "timeframe": "1h",
        
        # 数据配置
        "data_config": {
            "start_date": "2023-08-01",  # 2年数据
            "end_date": "2025-08-01",
            "data_points": 12000,  # 足够的数据点
            "validation_split": 0.2,
            "test_split": 0.1
        },
        
        # 增强特征配置
        "feature_config": {
            "use_enhanced_features": True,
            "feature_selection_strategy": "recommended",  # 使用推荐的特征集
            "include_basic_features": True,  # 包含基础特征
            "selected_features": None,  # 使用推荐特征，不手动选择
            
            # 特征工程参数
            "technical_indicators": {
                "bollinger_bands": True,
                "atr": True,
                "stochastic": True,
                "rsi": True,
                "macd": True,
                "ema": True,
                "sma": True,
                "williams_r": True,
                "cci": True,
                "momentum": True
            }
        },
        
        # 模型架构配置
        "model_config": {
            "sequence_length": 120,  # 序列长度
            "hidden_size": 256,      # 隐藏层大小
            "num_layers": 3,         # 层数
            "dropout": 0.3,          # Dropout率
            "learning_rate": 0.001,  # 学习率
            "batch_size": 32,        # 批次大小
            "epochs": 100,           # 训练轮数
            "early_stopping": True,  # 早停
            "patience": 15           # 早停耐心值
        },
        
        # 训练配置
        "training_config": {
            "use_gpu": True,
            "mixed_precision": True,  # 混合精度训练
            "gradient_clipping": True,
            "weight_decay": 0.0001,
            "scheduler": "cosine",    # 学习率调度器
            "warmup_epochs": 10
        },
        
        # 验证配置
        "validation_config": {
            "metrics": ["accuracy", "precision", "recall", "f1", "auc"],
            "cross_validation": False,  # 时间序列不适合交叉验证
            "validation_frequency": 5   # 每5个epoch验证一次
        }
    }
    
    print("📋 训练配置:")
    print(f"   模型名称: {training_config['model_name']}")
    print(f"   模型类型: {training_config['model_type']}")
    print(f"   交易品种: {training_config['symbol']}")
    print(f"   时间框架: {training_config['timeframe']}")
    print(f"   使用增强特征: {training_config['feature_config']['use_enhanced_features']}")
    print(f"   特征策略: {training_config['feature_config']['feature_selection_strategy']}")
    print(f"   序列长度: {training_config['model_config']['sequence_length']}")
    print(f"   隐藏层大小: {training_config['model_config']['hidden_size']}")
    print(f"   训练轮数: {training_config['model_config']['epochs']}")
    
    return training_config

def save_training_config(config):
    """保存训练配置到文件"""
    
    config_file = "enhanced_model_training_config.json"
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 训练配置已保存到: {config_file}")
        return config_file
        
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return None

def generate_training_script():
    """生成训练脚本"""
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动训练增强特征模型脚本
"""

import json
import sys
import os
from services.deep_learning_service import DeepLearningService

def main():
    """主函数"""
    print("🚀 开始训练增强特征模型")
    
    # 加载配置
    with open('enhanced_model_training_config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"📋 加载配置: {config['model_name']}")
    
    # 创建深度学习服务
    dl_service = DeepLearningService()
    
    # 开始训练
    result = dl_service.train_model(
        user_id=1,  # 默认用户ID
        model_name=config['model_name'],
        model_type=config['model_type'],
        symbol=config['symbol'],
        timeframe=config['timeframe'],
        start_date=config['data_config']['start_date'],
        end_date=config['data_config']['end_date'],
        data_points=config['data_config']['data_points'],
        sequence_length=config['model_config']['sequence_length'],
        hidden_size=config['model_config']['hidden_size'],
        num_layers=config['model_config']['num_layers'],
        dropout=config['model_config']['dropout'],
        learning_rate=config['model_config']['learning_rate'],
        batch_size=config['model_config']['batch_size'],
        epochs=config['model_config']['epochs'],
        use_gpu=config['training_config']['use_gpu'],
        use_enhanced_features=config['feature_config']['use_enhanced_features'],
        feature_selection_strategy=config['feature_config']['feature_selection_strategy'],
        selected_features=config['feature_config']['selected_features']
    )
    
    if result['success']:
        print(f"✅ 模型训练成功!")
        print(f"📋 模型ID: {result['model_id']}")
        print(f"📊 训练结果: {result}")
    else:
        print(f"❌ 模型训练失败: {result['error']}")
        return False
    
    return True

if __name__ == "__main__":
    main()
'''
    
    script_file = "train_enhanced_model.py"
    
    try:
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"✅ 训练脚本已生成: {script_file}")
        return script_file
        
    except Exception as e:
        print(f"❌ 生成训练脚本失败: {e}")
        return None

def main():
    """主函数"""
    print("🎯 重新训练增强特征模型")
    print("=" * 80)
    
    print("📋 步骤:")
    print("   1. 创建训练配置")
    print("   2. 保存配置文件")
    print("   3. 生成训练脚本")
    print("   4. 提供训练指导")
    
    # 1. 创建配置
    config = create_enhanced_training_config()
    
    # 2. 保存配置
    config_file = save_training_config(config)
    if not config_file:
        return False
    
    # 3. 生成训练脚本
    script_file = generate_training_script()
    if not script_file:
        return False
    
    # 4. 提供指导
    print(f"\n🎉 准备工作完成!")
    print(f"📋 生成的文件:")
    print(f"   - 配置文件: {config_file}")
    print(f"   - 训练脚本: {script_file}")
    
    print(f"\n🚀 开始训练:")
    print(f"   方法1 (推荐): 通过Web界面训练")
    print(f"      1. 打开浏览器访问 http://localhost:5000")
    print(f"      2. 进入 '模型训练' 页面")
    print(f"      3. 使用以下配置:")
    print(f"         - 模型名称: {config['model_name']}")
    print(f"         - 模型类型: {config['model_type'].upper()}")
    print(f"         - 启用增强特征: ✅")
    print(f"         - 特征策略: {config['feature_config']['feature_selection_strategy']}")
    
    print(f"\n   方法2: 使用命令行")
    print(f"      python {script_file}")
    
    print(f"\n💡 重要提醒:")
    print(f"   1. 这次训练将使用修复后的推理逻辑")
    print(f"   2. 准确率可能会比之前的94%低，这是正常的")
    print(f"   3. 关注模型的综合性能，不只是准确率")
    print(f"   4. 训练完成后进行充分的回测验证")
    
    return True

if __name__ == "__main__":
    main()
