#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单数据库检查
"""

import sqlite3
import json

def simple_check():
    """简单检查"""
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找最新的增强特征模型
        cursor.execute('''
            SELECT id, name, config
            FROM deep_learning_models
            WHERE status = 'completed' AND name LIKE '%增强%'
            ORDER BY created_at DESC
            LIMIT 1
        ''')
        
        result = cursor.fetchone()
        if result:
            model_id, name, config_str = result
            print(f"模型: {name}")
            print(f"ID: {model_id}")
            
            if config_str:
                try:
                    config = json.loads(config_str)
                    print(f"特征策略: {config.get('feature_selection_strategy', 'unknown')}")
                    print(f"输入特征数: {config.get('input_size', 'unknown')}")
                    print(f"使用增强特征: {config.get('use_enhanced_features', False)}")
                except:
                    print(f"配置解析失败")
            else:
                print("没有配置信息")
        else:
            print("没有找到增强特征模型")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    simple_check()
