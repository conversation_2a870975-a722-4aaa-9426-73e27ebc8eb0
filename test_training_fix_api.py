#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试训练修复API
"""

import requests
import json

def test_check_status_api():
    """测试检查状态API"""
    print('🔍 测试训练状态检查API')
    print('=' * 60)
    
    try:
        # 测试API端点
        url = 'http://localhost:5000/api/training-fix/check-status'
        
        # 发送请求（注意：需要登录状态）
        response = requests.get(url)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API响应成功")
            print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success'):
                tasks = data.get('tasks', [])
                print(f"📊 发现 {len(tasks)} 个训练任务")
                
                for i, task in enumerate(tasks, 1):
                    print(f"\n任务 {i}:")
                    print(f"  ID: {task.get('id', 'N/A')}")
                    print(f"  名称: {task.get('name', 'N/A')}")
                    print(f"  进度: {task.get('progress', 0)}%")
                    print(f"  状态: {task.get('status', 'N/A')}")
                    print(f"  更新间隔: {task.get('update_interval', 0):.1f}秒")
            else:
                print(f"❌ API返回错误: {data.get('error', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_direct_database():
    """直接测试数据库"""
    print('\n🔍 直接测试数据库')
    print('=' * 60)
    
    try:
        import sqlite3
        from datetime import datetime
        
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取运行中的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("✅ 没有运行中的训练任务")
        else:
            print(f"📊 发现 {len(tasks)} 个运行中的任务:")
            
            current_time = datetime.now()
            
            for i, task in enumerate(tasks, 1):
                task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at = task
                
                print(f"\n任务 {i}:")
                print(f"  ID: {task_id}")
                print(f"  模型ID: {model_id}")
                print(f"  状态: {status}")
                print(f"  进度: {progress}%")
                print(f"  轮次: {current_epoch}/{total_epochs}")
                print(f"  创建时间: {created_at}")
                print(f"  更新时间: {updated_at}")
                
                # 计算更新间隔
                if updated_at:
                    try:
                        update_time = datetime.fromisoformat(updated_at)
                        interval = (current_time - update_time).total_seconds()
                        print(f"  更新间隔: {interval:.1f}秒")
                        
                        if interval > 300:
                            print(f"  🔴 严重卡住")
                        elif interval > 120:
                            print(f"  ⚠️ 可能卡住")
                        else:
                            print(f"  ✅ 正常")
                    except:
                        print(f"  ❌ 时间解析失败")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")

def simulate_api_response():
    """模拟API响应格式"""
    print('\n🔍 模拟API响应格式')
    print('=' * 60)
    
    # 模拟正常响应
    normal_response = {
        "success": True,
        "tasks": [
            {
                "id": "d9ae387c-03f3-46de-b2b3-a3a32d465390",
                "name": "训练任务_d9ae387c",
                "model_name": "模型_ce0acc37",
                "status": "running",
                "progress": 35.0,
                "current_epoch": 1,
                "total_epochs": 200,
                "created_at": "2025-08-03 01:51:50",
                "updated_at": "2025-08-03T11:57:01.479019",
                "update_interval": 30.5
            },
            {
                "id": "9e19170a-0966-4cef-9379-62b52da54118",
                "name": "训练任务_9e19170a",
                "model_name": "模型_9ecd9eec",
                "status": "running",
                "progress": 35.0,
                "current_epoch": 1,
                "total_epochs": 100,
                "created_at": "2025-08-03 03:47:19",
                "updated_at": "2025-08-03T11:57:01.525841",
                "update_interval": 25.2
            }
        ],
        "count": 2
    }
    
    print("📊 正常响应格式:")
    print(json.dumps(normal_response, indent=2, ensure_ascii=False))
    
    # 模拟错误响应
    error_response = {
        "success": False,
        "error": "数据库连接失败",
        "tasks": []
    }
    
    print("\n❌ 错误响应格式:")
    print(json.dumps(error_response, indent=2, ensure_ascii=False))
    
    # 模拟空响应
    empty_response = {
        "success": True,
        "tasks": [],
        "count": 0
    }
    
    print("\n✅ 空响应格式:")
    print(json.dumps(empty_response, indent=2, ensure_ascii=False))

def main():
    """主函数"""
    print('🔧 训练修复API测试工具')
    print('=' * 80)
    
    # 1. 直接测试数据库
    test_direct_database()
    
    # 2. 模拟API响应
    simulate_api_response()
    
    # 3. 测试API（如果Flask应用正在运行）
    print('\n💡 如果Flask应用正在运行，可以测试API:')
    print('   python -c "import test_training_fix_api; test_training_fix_api.test_check_status_api()"')
    
    print(f"\n🎯 测试总结")
    print('=' * 80)
    print(f"✅ 数据库结构检查完成")
    print(f"✅ API响应格式确认")
    print(f"💡 前端JavaScript应该能正确处理这些数据格式")
    
    print(f"\n🚀 使用建议:")
    print(f"1. 确保Flask应用正在运行")
    print(f"2. 在浏览器中打开训练修复工具")
    print(f"3. 检查浏览器控制台的调试日志")
    print(f"4. 观察API返回的数据格式是否正确")

if __name__ == "__main__":
    main()
