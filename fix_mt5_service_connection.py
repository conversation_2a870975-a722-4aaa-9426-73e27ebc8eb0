#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复MT5服务连接问题
解决MT5服务连接状态异常导致的订单执行和持仓显示问题
"""

import time
from datetime import datetime

def diagnose_mt5_service_issue():
    """诊断MT5服务问题"""
    print("🔍 诊断MT5服务问题")
    print("=" * 60)
    
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5
        
        # 1. 检查MT5库状态
        print("1. 检查MT5库状态")
        if mt5.initialize():
            print("   ✅ MT5库初始化成功")
            
            account_info = mt5.account_info()
            if account_info:
                print(f"   ✅ 账户: {account_info.login} ({account_info.server})")
                print(f"   余额: ${account_info.balance:.2f}")
            
            terminal_info = mt5.terminal_info()
            if terminal_info:
                print(f"   ✅ 终端连接: {'是' if terminal_info.connected else '否'}")
                print(f"   交易允许: {'是' if terminal_info.trade_allowed else '否'}")
        else:
            print("   ❌ MT5库初始化失败")
            return False
        
        # 2. 检查MT5服务状态
        print("\n2. 检查MT5服务状态")
        print(f"   服务连接状态: {mt5_service.connected}")
        
        # 3. 检查服务内部状态
        print("\n3. 检查服务内部状态")
        try:
            status = mt5_service.get_connection_status()
            print(f"   连接状态: {status.get('connected')}")
            print(f"   重连服务: {status.get('reconnect_service_active')}")
            
            if 'last_error' in status:
                print(f"   最后错误: {status['last_error']}")
        except Exception as e:
            print(f"   ❌ 获取状态异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断异常: {e}")
        return False

def fix_mt5_service_connection():
    """修复MT5服务连接"""
    print("\n🔧 修复MT5服务连接")
    print("=" * 60)
    
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5
        
        # 1. 强制断开现有连接
        print("1. 断开现有连接")
        try:
            mt5_service.disconnect()
            print("   ✅ 服务连接已断开")
        except Exception as e:
            print(f"   ⚠️ 断开连接异常: {e}")
        
        # 2. 重新初始化MT5库
        print("\n2. 重新初始化MT5库")
        mt5.shutdown()
        time.sleep(1)
        
        if mt5.initialize():
            print("   ✅ MT5库重新初始化成功")
        else:
            print("   ❌ MT5库重新初始化失败")
            return False
        
        # 3. 重新连接服务
        print("\n3. 重新连接MT5服务")
        success = mt5_service.connect()
        
        if success:
            print("   ✅ MT5服务重新连接成功")
            
            # 验证连接
            print("\n4. 验证连接状态")
            if mt5_service.is_connected():
                print("   ✅ 连接验证成功")
                
                # 测试基本功能
                print("\n5. 测试基本功能")
                
                # 测试获取持仓
                positions = mt5_service.get_positions()
                print(f"   持仓获取: {'✅ 成功' if positions is not None else '❌ 失败'}")
                if positions:
                    print(f"   持仓数量: {len(positions)}")
                
                # 测试获取账户信息
                try:
                    account_info = mt5.account_info()
                    if account_info:
                        print(f"   账户信息: ✅ 成功 (余额: ${account_info.balance:.2f})")
                    else:
                        print(f"   账户信息: ❌ 失败")
                except Exception as e:
                    print(f"   账户信息: ❌ 异常 {e}")
                
                return True
            else:
                print("   ❌ 连接验证失败")
                return False
        else:
            print("   ❌ MT5服务重新连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 修复连接异常: {e}")
        return False

def test_order_execution():
    """测试订单执行"""
    print("\n🧪 测试订单执行")
    print("=" * 60)
    
    try:
        from services.mt5_service import mt5_service
        
        if not mt5_service.is_connected():
            print("❌ MT5服务未连接，无法测试")
            return False
        
        # 构建测试订单
        test_params = {
            'symbol': 'XAUUSD',
            'order_type': 'buy',
            'lot_size': 0.01,
            'comment': 'MT5服务连接测试'
        }
        
        print(f"📊 测试参数: {test_params}")
        
        # 执行测试订单
        print("🔄 执行测试订单...")
        result = mt5_service.place_order(
            symbol=test_params['symbol'],
            order_type=test_params['order_type'],
            lot_size=test_params['lot_size'],
            comment=test_params['comment']
        )
        
        if result and result.get('success'):
            print(f"✅ 测试订单执行成功: 订单ID {result.get('order_id')}")
            
            # 验证订单是否在MT5中
            import MetaTrader5 as mt5
            time.sleep(1)  # 等待1秒
            
            positions = mt5.positions_get()
            if positions:
                for pos in positions:
                    if pos.comment and '测试' in pos.comment:
                        print(f"✅ 在MT5中找到测试订单: 票号 {pos.ticket}")
                        
                        # 立即平仓测试订单
                        close_result = mt5_service.close_position(pos.ticket)
                        if close_result and close_result.get('success'):
                            print(f"✅ 测试订单已平仓")
                        else:
                            print(f"⚠️ 测试订单平仓失败: {close_result.get('error') if close_result else '未知错误'}")
                        break
            
            return True
        else:
            error_msg = result.get('error') if result else '未知错误'
            print(f"❌ 测试订单执行失败: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ 测试订单执行异常: {e}")
        return False

def verify_position_apis():
    """验证持仓API一致性"""
    print("\n🔍 验证持仓API一致性")
    print("=" * 60)
    
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5
        import requests
        
        # 1. MT5直接查询
        direct_positions = mt5.positions_get()
        direct_count = len(direct_positions) if direct_positions else 0
        print(f"1. MT5直接查询: {direct_count} 个持仓")
        
        # 2. MT5服务查询
        service_positions = mt5_service.get_positions()
        service_count = len(service_positions) if service_positions else 0
        print(f"2. MT5服务查询: {service_count} 个持仓")
        
        # 3. API查询
        try:
            response = requests.get('http://127.0.0.1:5000/api/mt5/positions')
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    api_positions = result.get('positions', [])
                    api_count = len(api_positions)
                    print(f"3. MT5持仓API: {api_count} 个持仓")
                else:
                    print(f"3. MT5持仓API: 失败 - {result.get('error')}")
                    api_count = -1
            else:
                print(f"3. MT5持仓API: HTTP {response.status_code}")
                api_count = -1
        except Exception as e:
            print(f"3. MT5持仓API: 异常 - {e}")
            api_count = -1
        
        # 4. 比较结果
        print(f"\n📊 一致性检查:")
        if direct_count == service_count == api_count:
            print(f"✅ 所有查询结果一致: {direct_count} 个持仓")
            return True
        else:
            print(f"❌ 查询结果不一致:")
            print(f"   直接查询: {direct_count}")
            print(f"   服务查询: {service_count}")
            print(f"   API查询: {api_count}")
            return False
            
    except Exception as e:
        print(f"❌ 验证持仓API异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复MT5服务连接问题")
    print("=" * 80)
    
    print("📋 修复目标:")
    print("1. 修复MT5服务连接状态异常")
    print("2. 确保订单能正确发送到MT5")
    print("3. 解决持仓显示不一致问题")
    print()
    
    # 1. 诊断问题
    if not diagnose_mt5_service_issue():
        print("❌ 诊断失败，无法继续")
        return
    
    # 2. 修复连接
    if fix_mt5_service_connection():
        print("\n✅ MT5服务连接修复成功")
        
        # 3. 测试订单执行
        if test_order_execution():
            print("\n✅ 订单执行测试成功")
        else:
            print("\n❌ 订单执行测试失败")
        
        # 4. 验证持仓API
        if verify_position_apis():
            print("\n✅ 持仓API一致性验证成功")
        else:
            print("\n❌ 持仓API一致性验证失败")
        
        print(f"\n🎉 MT5服务连接问题修复完成!")
        print("💡 建议:")
        print("   • 重新测试手动下单功能")
        print("   • 刷新AI推理交易页面")
        print("   • 检查持仓显示是否正常")
        
    else:
        print("\n❌ MT5服务连接修复失败")
        print("💡 建议:")
        print("   • 重启MT5终端")
        print("   • 重启应用程序")
        print("   • 检查MT5设置中的自动交易选项")
    
    return 0

if __name__ == "__main__":
    main()
