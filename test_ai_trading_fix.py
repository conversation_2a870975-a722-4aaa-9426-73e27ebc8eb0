#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI推理交易修复
验证AI推理交易是否能正确使用深度学习推理API并产生订单
"""

import requests
import json
import time
from datetime import datetime

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post('http://127.0.0.1:5000/login', data=login_data)
    if response.status_code == 200:
        print("✅ 登录成功")
        return session
    else:
        print(f"❌ 登录失败: {response.status_code}")
        return None

def test_deep_learning_inference_api(session):
    """测试深度学习推理API"""
    print("\n🧠 测试深度学习推理API")
    print("=" * 60)
    
    try:
        # 首先获取可用的模型
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        if models_response.status_code != 200:
            print("❌ 无法获取模型列表")
            return False
            
        models_data = models_response.json()
        if not models_data.get('success') or not models_data.get('models'):
            print("❌ 没有可用的模型")
            return False
            
        # 找到第一个完成训练的模型
        completed_models = [m for m in models_data['models'] if m.get('status') == 'completed']
        if not completed_models:
            print("❌ 没有完成训练的模型")
            return False
            
        test_model = completed_models[0]
        print(f"📊 使用模型: {test_model['name']} ({test_model['id'][:8]}...)")
        
        # 测试推理API
        inference_data = {
            'model_id': test_model['id'],
            'symbol': test_model['symbol'],
            'timeframe': test_model['timeframe'],
            'inference_mode': 'realtime',
            'data_points': 100,
            'use_gpu': True,
            'show_confidence': True,
            'trade_config': {
                'min_confidence': 0.3,
                'lot_size': 0.01,
                'max_lot_size': 0.05,
                'account_type': 'demo'
            }
        }
        
        print(f"🔮 发送推理请求...")
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference', 
                               json=inference_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('results'):
                latest_result = result['results'][-1]
                print(f"✅ 推理成功:")
                print(f"   预测: {latest_result.get('prediction')}")
                print(f"   置信度: {latest_result.get('confidence', 0)*100:.1f}%")
                print(f"   价格: {latest_result.get('current_price', 0):.5f}")
                
                # 检查是否满足交易条件
                if latest_result.get('confidence', 0) >= 0.3 and latest_result.get('prediction') in ['BUY', 'SELL']:
                    print(f"   ✅ 满足交易条件")
                    return True, test_model, latest_result
                else:
                    print(f"   ❌ 不满足交易条件")
                    return False, test_model, latest_result
            else:
                print(f"❌ 推理失败: {result.get('error')}")
                return False, None, None
        else:
            print(f"❌ 推理请求失败: {response.status_code}")
            return False, None, None
            
    except Exception as e:
        print(f"❌ 测试推理API异常: {e}")
        return False, None, None

def test_ai_trading_execution(session, model, inference_result):
    """测试AI交易执行"""
    print("\n💰 测试AI交易执行")
    print("=" * 60)
    
    try:
        # 构建交易数据
        trade_data = {
            'symbol': model['symbol'],
            'action': inference_result['prediction'],
            'lot_size': 0.01,
            'stop_loss_pips': 10,
            'take_profit_pips': 20,
            'inference_result': inference_result,
            'trading_config': {
                'min_confidence': 0.3,
                'lot_size': 0.01
            }
        }
        
        print(f"📊 交易数据:")
        print(f"   品种: {trade_data['symbol']}")
        print(f"   方向: {trade_data['action']}")
        print(f"   手数: {trade_data['lot_size']}")
        print(f"   置信度: {inference_result.get('confidence', 0)*100:.1f}%")
        
        # 执行交易
        print(f"🚀 发送交易执行请求...")
        response = session.post('http://127.0.0.1:5000/api/deep-learning/execute-trade', 
                               json=trade_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 交易执行成功:")
                print(f"   订单ID: {result.get('order_id')}")
                print(f"   入场价: {result.get('entry_price')}")
                print(f"   消息: {result.get('message')}")
                return True
            else:
                print(f"❌ 交易执行失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 交易请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试交易执行异常: {e}")
        return False

def check_trading_statistics(session):
    """检查交易统计"""
    print("\n📊 检查交易统计")
    print("=" * 60)
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/trading-statistics')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('statistics', {})
                print(f"✅ 交易统计:")
                print(f"   当前持仓: {stats.get('current_positions', 0)}")
                print(f"   今日交易: {stats.get('today_trades', 0)}")
                print(f"   总盈亏: {stats.get('total_profit', 0):.2f}")
                return True
            else:
                print(f"❌ 获取统计失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 统计请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查统计异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 测试AI推理交易修复")
    print("=" * 80)
    
    print("📋 测试目标:")
    print("1. 验证深度学习推理API正常工作")
    print("2. 验证AI推理结果能触发交易执行")
    print("3. 验证交易订单能成功创建")
    print("4. 验证交易统计正确更新")
    print()
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 测试推理API
    inference_success, model, inference_result = test_deep_learning_inference_api(session)
    if not inference_success:
        print("\n❌ 推理API测试失败，无法继续")
        return
    
    # 3. 测试交易执行
    if model and inference_result:
        trade_success = test_ai_trading_execution(session, model, inference_result)
        if trade_success:
            print("\n✅ 交易执行测试成功")
            
            # 4. 检查统计
            time.sleep(2)  # 等待数据更新
            check_trading_statistics(session)
        else:
            print("\n❌ 交易执行测试失败")
    
    # 5. 总结
    print(f"\n📊 测试结果总结")
    print("=" * 80)
    
    if inference_success and trade_success:
        print("✅ AI推理交易修复成功!")
        print("💡 现在AI推理交易应该能正常工作:")
        print("   1. 使用真实的深度学习推理API")
        print("   2. 基于真实的AI推理结果执行交易")
        print("   3. 正确检查置信度和交易条件")
        print("   4. 成功创建交易订单")
    else:
        print("❌ AI推理交易仍有问题，需要进一步调试")
    
    return 0

if __name__ == "__main__":
    main()
