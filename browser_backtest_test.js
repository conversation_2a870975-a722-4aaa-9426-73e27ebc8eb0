
// AI推理回测显示修复测试脚本
console.log("🧪 开始测试AI推理回测显示修复...");

// 1. 检查修复函数是否存在
console.log("📋 检查修复函数:");
const functions = ['formatDateTime', 'formatTradeType', 'formatPrice', 'formatProfit'];
functions.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName}: 函数存在`);
    } else {
        console.log(`❌ ${funcName}: 函数不存在`);
    }
});

// 2. 测试日期格式化函数
console.log("📋 测试日期格式化:");
if (typeof formatDateTime === 'function') {
    const testDates = [
        '2024-01-15T10:30:00Z',
        '2024-01-15 10:30:00',
        1705312200000,  // 毫秒时间戳
        1705312200,     // 秒时间戳
        null,
        undefined,
        'invalid-date'
    ];
    
    testDates.forEach(date => {
        const result = formatDateTime(date);
        console.log(`   输入: ${date} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatDateTime函数不存在");
}

// 3. 测试交易类型格式化
console.log("📋 测试交易类型格式化:");
if (typeof formatTradeType === 'function') {
    const testTypes = ['BUY', 'SELL', 'buy', 'sell', 0, 1, '0', '1', null, undefined];
    
    testTypes.forEach(type => {
        const result = formatTradeType(type);
        console.log(`   输入: ${type} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatTradeType函数不存在");
}

// 4. 测试价格格式化
console.log("📋 测试价格格式化:");
if (typeof formatPrice === 'function') {
    const testPrices = [1.23456, 0, null, undefined, 'invalid', 1000.123456];
    
    testPrices.forEach(price => {
        const result = formatPrice(price);
        console.log(`   输入: ${price} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatPrice函数不存在");
}

// 5. 测试盈亏格式化
console.log("📋 测试盈亏格式化:");
if (typeof formatProfit === 'function') {
    const testProfits = [100.50, -50.25, 0, null, undefined, 'invalid'];
    
    testProfits.forEach(profit => {
        const result = formatProfit(profit);
        console.log(`   输入: ${profit} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatProfit函数不存在");
}

// 6. 创建模拟交易数据并测试显示
console.log("📋 测试交易详情显示:");
if (typeof displayTradeDetails === 'function') {
    const mockTrades = [
        {
            open_time: '2024-01-15T10:30:00Z',
            type: 'BUY',
            lot_size: 0.1,
            open_price: 1.23456,
            close_price: 1.23556,
            stop_loss: 1.23356,
            take_profit: 1.23656,
            profit: 10.50,
            confidence: 0.85
        },
        {
            open_time: 1705312200000,
            type: 'SELL',
            volume: 0.2,
            entry_price: 1.23456,
            exit_price: 1.23356,
            sl: 1.23556,
            tp: 1.23256,
            profit: -20.25,
            confidence: 0.75
        },
        {
            time: null,
            type: null,
            lot_size: null,
            open_price: null,
            close_price: null,
            stop_loss: null,
            take_profit: null,
            profit: null,
            confidence: null
        }
    ];
    
    console.log("🔄 显示模拟交易数据...");
    displayTradeDetails(mockTrades);
    console.log("✅ 交易详情显示完成");
} else {
    console.log("❌ displayTradeDetails函数不存在");
}

// 7. 检查页面元素
console.log("📋 检查页面元素:");
const elements = ['backtestResults', 'backtestStats', 'backtestCard'];
elements.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ ${id}: 元素存在`);
    } else {
        console.log(`❌ ${id}: 元素不存在`);
    }
});

console.log("🎉 AI推理回测显示修复测试完成！");
console.log("💡 如果看到'Invalid Date'或'undefined'错误，说明修复未完全生效");
console.log("💡 请刷新页面并重新运行测试");
