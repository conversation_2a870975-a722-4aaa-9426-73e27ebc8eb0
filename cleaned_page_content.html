<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI推理交易</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>
    <!-- 自定义CSS -->
    <link href="/static/css/custom.css" rel="stylesheet"></script>

    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
        }

        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }

        .profit-positive {
            color: #28a745;
        }

        .profit-negative {
            color: #dc3545;
        }
    </style>

    
<style>
.position-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e3e6f0;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.position-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #5a5c69;
}

.position-direction-buy {
    background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%);
    color: white;
}

.position-direction-sell {
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
    color: white;
}

.position-profit-positive {
    color: #1cc88a;
    font-weight: bold;
}

.position-profit-negative {
    color: #e74a3b;
    font-weight: bold;
}

.position-info-item {
    margin-bottom: 8px;
}

.position-info-label {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.position-info-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #5a5c69;
}

.position-header {
    border-bottom: 1px solid #e3e6f0;
    padding-bottom: 12px;
    margin-bottom: 16px;
}

.position-stats {
    background: #f8f9fc;
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
}

.badge-direction {
    font-size: 0.75rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
}

.time-badge {
    background: #e9ecef;
    color: #495057;
    font-size: 0.7rem;
    padding: 4px 8px;
    border-radius: 12px;
}

/* 交易条件分析样式 */
.condition-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 6px;
    border-radius: 6px;
    background: #f8f9fa;
    border-left: 4px solid #dee2e6;
    font-size: 0.85rem;
}

.condition-item.passed {
    background: #d4edda;
    border-left-color: #28a745;
    color: #155724;
}

.condition-item.failed {
    background: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

.condition-item.warning {
    background: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
}

.condition-label {
    font-weight: 500;
    flex: 1;
}

.condition-value {
    font-weight: 600;
    margin-left: 8px;
}

.condition-status {
    margin-left: 8px;
    font-size: 0.9rem;
}

.trading-decision {
    padding: 12px;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
    margin-top: 12px;
}

.trading-decision.execute {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.trading-decision.skip {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

.trading-decision.blocked {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}
</style>

</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-chart-line"></i>
                            MateTrade4
                        </h4>
                        <small class="text-white-50">AI自动交易系统</small>
                    </div>

                    <ul class="nav flex-column">
                        <!-- 用户信息显示 -->
                        <li class="nav-item mb-3">
                            <div class="text-center">
                                <div class="text-white">
                                    <i class="fas fa-user-circle fa-2x"></i>
                                </div>
                                <div class="text-white mt-2">
                                    <strong>admin</strong>
                                    
                                        <span class="badge bg-danger ms-1">管理员</span>
                                    
                                </div>
                            </div>
                        </li>

                        <!-- 首页仪表盘 - 所有用户都可访问 -->
                        <li class="nav-item">
                            <a class="nav-link "
                               href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i>
                                首页仪表盘
                            </a>
                        </li>

                        <!-- 交易管理 - 所有用户都可访问 -->
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>交易管理</span>
                            </h6>
                        </li>


                        <li class="nav-item">
                            <a class="nav-link "
                               href="/risk-events">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                风险事件
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link "
                               href="/pattern-monitoring">
                                <i class="fas fa-chart-line text-info"></i>
                                形态监测
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link "
                               href="/low-risk-trading">
                                <i class="fas fa-shield-alt text-success"></i>
                                低风险交易
                                <span class="badge bg-success ms-1">SAFE</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link "
                               href="/strategy-trading">
                                <i class="fas fa-robot text-primary"></i>
                                策略交易
                                <span class="badge bg-primary ms-1">AI</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link "
                               href="/strategy-backtest">
                                <i class="fas fa-chart-line text-info"></i>
                                策略回测
                                <span class="badge bg-info ms-1">验证</span>
                            </a>
                        </li>

                        <!-- AI推理交易 - 从深度学习模块移动到交易管理 -->
                        <li class="nav-item">
                            <a class="nav-link active"
                               href="/deep-learning/inference">
                                <i class="fas fa-magic text-warning"></i>
                                AI推理交易
                                <span class="badge bg-warning ms-1">实盘</span>
                            </a>
                        </li>

                        <!-- AI推理回测 - 历史数据验证 -->
                        <li class="nav-item">
                            <a class="nav-link "
                               href="/deep-learning/backtest">
                                <i class="fas fa-chart-line text-success"></i>
                                AI推理回测
                                <span class="badge bg-success ms-1">回测</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link "
                               href="/mt5-connection">
                                <i class="fas fa-plug"></i>
                                MT5连接
                            </a>
                        </li>


                        <!-- 分析工具 - 仅VIP和管理员可见 -->
                        
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>分析工具</span>
                                <span class="badge bg-warning">VIP</span>
                            </h6>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link "
                               href="/analysis/ai-process">
                                <i class="fas fa-brain"></i>
                                AI策略分析过程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link "
                               href="/analysis/ai-training">
                                <i class="fas fa-graduation-cap"></i>
                                训练AI策略
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link "
                               href="/charts">
                                <i class="fas fa-chart-candlestick"></i>
                                专业图表
                            </a>
                        </li>

                        <!-- 深度学习模块 -->
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>深度学习</span>
                                <span class="badge bg-info">GPU</span>
                            </h6>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link "
                               href="/deep-learning">
                                <i class="fas fa-tachometer-alt"></i>
                                深度学习仪表板
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link "
                               href="/deep-learning/training">
                                <i class="fas fa-dumbbell"></i>
                                模型训练
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link "
                               href="/deep-learning/management">
                                <i class="fas fa-database"></i>
                                模型管理
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link "
                               href="/deep-learning/gpu-monitor">
                                <i class="fas fa-microchip"></i>
                                GPU监控
                            </a>
                        </li>
                        
                        

                        

                        <!-- 用户管理和系统设置 - 仅管理员可见 -->
                        
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>用户管理</span>
                                <span class="badge bg-danger">管理员</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link "
                               href="/user-management">
                                <i class="fas fa-users"></i>
                                用户管理
                            </a>
                        </li>

                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-white-50">
                                <span>系统管理</span>
                                <span class="badge bg-danger">管理员</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link "
                               href="/settings">
                                <i class="fas fa-cog"></i>
                                系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link "
                               href="/ai-strategy-management">
                                <i class="fas fa-share-alt"></i>
                                AI策略分享管理
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="/logout">
                                <i class="fas fa-sign-out-alt"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">仪表盘</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <span class="navbar-text">
                                欢迎, admin
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Flash消息 -->
                
                    
                

                <!-- 页面内容 -->
                
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-magic text-warning me-2"></i>
                    AI推理交易
                    <small class="text-muted ms-2">基于深度学习模型的智能交易系统</small>
                </h1>
                <a href="/dashboard" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回首页
                </a>
            </div>
        </div>
    </div>

    <!-- MT5连接状态显示 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card border-left-primary" id="mt5StatusCard">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-plug fa-2x" id="mt5StatusIcon"></i>
                                </div>
                                <div>
                                    <div class="font-weight-bold" id="mt5StatusText">检查MT5连接状态...</div>
                                    <div class="small text-muted" id="mt5StatusDetails">正在获取连接信息</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <button type="button" class="btn btn-sm btn-outline-primary me-2" id="refreshMT5StatusBtn" onclick="checkMT5Status()">
                                <i class="fas fa-sync-alt me-1"></i>刷新状态
                            </button>
                            <button type="button" class="btn btn-sm btn-warning" id="reconnectMT5Btn" onclick="forceMT5Reconnect()" style="display: none;">
                                <i class="fas fa-plug me-1"></i>重新连接
                            </button>
                        </div>
                    </div>

                    <!-- 详细状态信息 -->
                    <div class="row mt-2" id="mt5DetailedStatus" style="display: none;">
                        <div class="col-12">
                            <div class="small">
                                <div class="row">
                                    <div class="col-md-3">
                                        <span class="text-muted">交易品种:</span>
                                        <span id="mt5SymbolsCount">-</span>
                                    </div>
                                    <div class="col-md-3">
                                        <span class="text-muted">价格数据:</span>
                                        <span id="mt5PriceStatus">-</span>
                                    </div>
                                    <div class="col-md-3">
                                        <span class="text-muted">自动重连:</span>
                                        <span id="mt5AutoReconnectStatus">-</span>
                                    </div>
                                    <div class="col-md-3">
                                        <span class="text-muted">推理就绪:</span>
                                        <span id="mt5InferenceReady">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 推理交易区域 -->
    <div class="row" id="tradingSection">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-robot me-2"></i>AI推理交易
                        <small class="text-muted ms-2">基于深度学习模型推理结果执行实时交易</small>
                    </h6>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-secondary me-2" id="mt5ConnectionStatus">MT5未连接</span>
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="checkMT5Connection()">
                            <i class="fas fa-sync-alt"></i> 检查连接
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success ms-1" onclick="autoConnectMT5()">
                            <i class="fas fa-plug"></i> 自动连接
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 交易配置 -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-cogs me-1"></i>交易配置
                            </h6>

                            <!-- 模型选择 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-brain me-1"></i>交易模型
                                    <small class="text-muted">(选择用于自动交易的深度学习模型)</small>
                                </label>
                                <select class="form-select" id="tradingModelSelect">
                                    <option value="">请选择交易模型...</option>
                                </select>
                                <div class="form-text" id="tradingModelInfo">
                                    <i class="fas fa-info-circle text-info"></i>
                                    选择一个训练完成的模型用于AI交易决策
                                </div>
                            </div>

                            <!-- 基础交易参数 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">交易手数</label>
                                    <input type="number" class="form-control" id="tradingLotSize"
                                           value="0.01" min="0.01" max="10" step="0.01">
                                    <div class="form-text">每次交易的手数大小</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">最大持仓数</label>
                                    <input type="number" class="form-control" id="maxPositions"
                                           value="4" min="1" max="10" step="1">
                                    <div class="form-text">同时持有的最大仓位数</div>
                                </div>
                            </div>

                            <!-- 风险管理参数 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">止损点数 (pips)</label>
                                    <input type="number" class="form-control" id="stopLossPips"
                                           value="1000" min="10" max="3000" step="10">
                                    <div class="form-text">自动止损距离 (黄金推荐1000+ pips)</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">止盈点数 (pips)</label>
                                    <input type="number" class="form-control" id="takeProfitPips"
                                           value="2000" min="10" max="5000" step="10">
                                    <div class="form-text">自动止盈距离 (黄金推荐2000+ pips)</div>
                                </div>
                            </div>

                            <!-- 推理交易条件 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">最低置信度</label>
                                    <input type="number" class="form-control" id="minConfidence"
                                           value="0.3" min="0.3" max="0.99" step="0.05">
                                    <div class="form-text">执行交易的最低置信度 (推荐: 0.3-0.6)</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">推理间隔</label>
                                    <select class="form-select" id="inferenceInterval">
                                        <option value="auto">自动适配 (推荐)</option>
                                        <option value="30">30秒 (高频)</option>
                                        <option value="60">1分钟</option>
                                        <option value="300">5分钟</option>
                                        <option value="900">15分钟</option>
                                        <option value="1800">30分钟</option>
                                        <option value="3600">1小时</option>
                                    </select>
                                    <div class="form-text" id="inferenceIntervalHelp">
                                        自动适配将根据模型时间框架智能设置间隔
                                    </div>
                                </div>
                            </div>

                            <!-- 交易时间限制 -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">交易开始时间</label>
                                    <input type="time" class="form-control" id="tradingStartTime" value="00:05">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">交易结束时间</label>
                                    <input type="time" class="form-control" id="tradingEndTime" value="23:55">
                                </div>
                            </div>

                            <!-- 高级选项 -->
                            <div class="mb-3">
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableAutoTrading" checked>
                                    <label class="form-check-label" for="enableAutoTrading">
                                        <strong>启用自动交易</strong>
                                        <small class="text-muted d-block">基于推理结果自动执行交易</small>
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableDynamicSL" checked>
                                    <label class="form-check-label" for="enableDynamicSL">
                                        <strong>动态止盈止损</strong>
                                        <small class="text-muted d-block">根据市场波动性和置信度自动调整止盈止损</small>
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableInferenceTrailingStop" checked onchange="toggleInferenceTrailingStopConfig()">
                                    <label class="form-check-label" for="enableInferenceTrailingStop">
                                        移动止损
                                        <small class="text-muted d-block">盈利时自动调整止损位置</small>
                                    </label>
                                </div>

                                <!-- 移动止损详细配置 -->
                                <div class="mb-3" id="inferenceTrailingStopConfig">
                                    <div class="card border-info">
                                        <div class="card-header bg-light py-2">
                                            <h6 class="mb-0 text-info">
                                                <i class="fas fa-chart-line me-2"></i>移动止损配置
                                            </h6>
                                        </div>
                                        <div class="card-body py-3">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label class="form-label">止损距离 (pips)</label>
                                                    <input type="number" class="form-control" id="inferenceTrailingStopDistance"
                                                           value="80" min="10" max="500" step="5">
                                                    <div class="form-text">当前价格与止损价格的距离 (黄金推荐80+ pips)</div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">止损步长 (pips)</label>
                                                    <input type="number" class="form-control" id="inferenceTrailingStopStep"
                                                           value="10" min="1" max="50" step="1">
                                                    <div class="form-text">价格移动多少点后调整止损</div>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    移动止损会在价格朝有利方向移动时自动调整止损位置，帮助锁定利润
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableNewsFilter" checked>
                                    <label class="form-check-label" for="enableNewsFilter">
                                        新闻过滤
                                        <small class="text-muted d-block">重要新闻时段暂停交易</small>
                                    </label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="enableCliffBrake">
                                    <label class="form-check-label" for="enableCliffBrake">
                                        <strong>悬崖勒马</strong>
                                        <small class="text-muted d-block">连续2单亏损时，根据价格趋势反转交易方向</small>
                                    </label>
                                </div>
                            </div>

                            <!-- 配置预设 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-magic me-1"></i><strong>配置预设</strong>
                                </label>
                                <select class="form-select" id="tradingPreset" onchange="applyTradingPreset()">
                                    <option value="custom">自定义配置</option>
                                    <option value="conservative">保守型 (置信度60%, 止损30pips)</option>
                                    <option value="balanced" selected>平衡型 (置信度30%, 止损50pips)</option>
                                    <option value="aggressive">激进型 (置信度30%, 止损80pips)</option>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>快速应用专业推荐的交易配置
                                </div>
                            </div>
                        </div>

                        <!-- 交易状态和控制 -->
                        <div class="col-md-6">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-chart-bar me-1"></i>交易状态
                            </h6>

                            <!-- 实时市场数据 -->
                            <div class="card bg-light mb-3">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-2">
                                        <i class="fas fa-chart-line text-info me-1"></i>实时市场数据
                                    </h6>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="text-muted small">买价</div>
                                            <div class="fw-bold text-success" id="currentBid">--</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-muted small">卖价</div>
                                            <div class="fw-bold text-danger" id="currentAsk">--</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-muted small">点差</div>
                                            <div class="fw-bold text-info" id="currentSpread">--</div>
                                        </div>
                                    </div>
                                    <div class="text-center mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            更新时间: <span id="marketDataTime">--</span>
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- 手动下单控制 -->
                            <div class="card bg-warning bg-opacity-10 mb-3">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-2">
                                        <i class="fas fa-hand-paper text-warning me-1"></i>手动下单
                                        <small class="text-muted">(使用AI交易配置)</small>
                                    </h6>
                                    <div class="row g-2 mb-2">
                                        <div class="col-6">
                                            <button class="btn btn-success w-100" id="manualBuyBtn" onclick="executeManualTrade('BUY')" disabled>
                                                <i class="fas fa-arrow-up me-1"></i>买入 (BUY)
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <button class="btn btn-danger w-100" id="manualSellBtn" onclick="executeManualTrade('SELL')" disabled>
                                                <i class="fas fa-arrow-down me-1"></i>卖出 (SELL)
                                            </button>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            手动下单将使用当前AI交易配置参数，包括手数、止损、止盈、移动止损等设置
                                        </small>
                                    </div>
                                    <!-- 手动下单状态显示 -->
                                    <div id="manualTradeStatus" class="mt-2" style="display: none;">
                                        <div class="alert alert-info mb-0 py-2">
                                            <small id="manualTradeStatusText">准备下单...</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 最新推理结果 -->
                            <div class="card bg-light mb-3">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-2">
                                        <i class="fas fa-brain text-warning me-1"></i>最新推理结果
                                    </h6>
                                    <div id="latestInferenceResult">
                                        <div class="text-muted text-center">暂无推理结果</div>
                                    </div>

                                    <!-- 交易条件分析 -->
                                    <div id="tradingConditionsAnalysis" style="display: none;">
                                        <hr class="my-3">
                                        <h6 class="mb-2">
                                            <i class="fas fa-check-circle me-1"></i>交易条件分析
                                        </h6>
                                        <div id="conditionsCheckResults">
                                            <!-- 条件检查结果将在这里显示 -->
                                        </div>
                                        <div id="tradingDecision" class="mt-2">
                                            <!-- 交易决策将在这里显示 -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 交易统计 -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body p-2 text-center">
                                            <div class="small">今日交易</div>
                                            <div class="h5 mb-0" id="todayTrades">0</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-success text-white">
                                        <div class="card-body p-2 text-center">
                                            <div class="small">当前持仓</div>
                                            <div class="h5 mb-0" id="currentPositions">0</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 交易状态指示器 -->
                            <div class="alert alert-info mb-3" id="tradingStatusIndicator">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <div>
                                        <div class="fw-bold" id="tradingStatusText">交易状态：未启动</div>
                                        <div class="small text-muted" id="tradingStatusDetails">点击"开始AI交易"启动自动交易</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 控制按钮 -->
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-success" id="startTradingBtn" onclick="startAutoTrading()">
                                    <i class="fas fa-play me-1"></i>开始AI交易
                                </button>
                                <button type="button" class="btn btn-danger" id="stopTradingBtn" onclick="stopAutoTrading()" style="display: none;">
                                    <i class="fas fa-stop me-1"></i>停止AI交易
                                </button>
                                <button type="button" class="btn btn-warning" onclick="closeAllPositions()">
                                    <i class="fas fa-times-circle me-1"></i>平仓所有持仓
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 当前持仓展示区域 - 重新设计 -->
<div class="container-fluid mt-4" id="currentPositionsSection">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line me-2"></i>当前持仓
                        <span class="badge bg-info ms-2" id="realTimePositionCount">0</span>
                    </h6>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshPositions()" id="refreshPositionsBtn">
                            <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="closeAllPositions()" id="closeAllBtn" style="display: none;">
                            <i class="fas fa-times-circle me-1"></i>全部平仓
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 加载状态 -->
                    <div id="positionsLoadingState" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在获取持仓数据...</p>
                    </div>

                    <!-- 错误状态 -->
                    <div id="positionsErrorState" class="text-center py-4" style="display: none;">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                        <p class="text-muted mb-3" id="positionsErrorMessage">获取持仓数据失败</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshPositions()">
                            <i class="fas fa-redo me-1"></i>重试
                        </button>
                    </div>

                    <!-- 无持仓状态 -->
                    <div id="noPositionsState" class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无持仓</h5>
                        <p class="text-muted mb-0">当前没有开仓的交易订单</p>
                    </div>

                    <!-- 持仓列表 -->
                    <div id="positionsListContainer" style="display: none;">
                        <div class="row" id="positionCardsContainer">
                            <!-- 持仓卡片将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let selectedModel = null;
let inferenceResults = null;

// 交易相关全局变量
let autoTradingActive = false;
let tradingInterval = null;
let mt5Connected = false;
let currentMarketData = null;
let selectedTradingModel = null;
let tradingStatistics = {
    todayTrades: 0,
    currentPositions: 0,
    totalProfit: 0
};

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 AI推理交易页面开始初始化...');

    // 基础功能初始化
    loadAvailableModels();
    loadTradingModels(); // 加载交易模型
    initializeDefaultTimeRange(); // 初始化默认时间范围
    toggleTimeRangeVisibility(); // 初始化时间范围显示状态
    checkMT5Connection(); // 检查MT5连接状态

    // 延迟恢复自动交易状态，确保DOM完全准备好
    setTimeout(function() {
        console.log('🔄 开始恢复自动交易状态...');
        restoreAutoTradingState();

        // 启动状态监控
        startAutoTradingStatusMonitoring();

        // 启动心跳检测
        startHeartbeat();

        // 自动加载保存的优化结果
        loadSavedOptimizationResults();
    }, 2000);

    // 页面加载时自动应用保守型预设
    setTimeout(function() {
        if (document.getElementById('tradingPreset').value === 'conservative') {
            applyTradingPreset();
        }
    }, 1000);

    // 监听推理间隔选择变化
    document.getElementById('inferenceInterval').addEventListener('change', function() {
        updateInferenceIntervalForModel();
    });

    // 监听交易模型选择变化
    document.getElementById('tradingModelSelect').addEventListener('change', onTradingModelChange);

    // 监听模型选择变化
    null.addEventListener('change', function() {
        const modelId = this.value;
        updateInferenceIntervalForModel(); // 更新推理间隔
        if (modelId) {
            loadModelInfo(modelId);
        } else {
            clearModelInfo();
        }
    });

    // 检查是否需要显示增强特征提示
    setTimeout(function() {
        const hintDismissed = localStorage.getItem('enhancedFeaturesHintDismissed');
        const hint = document.getElementById('enhancedFeaturesHint');

        if (hintDismissed === 'true' && hint) {
            hint.style.display = 'none';
        }
    }, 1000);
});

// 加载可用模型
async function loadAvailableModels() {
    try {
        const response = await fetch('/api/deep-learning/models');
        const data = await response.json();
        
        if (data.success) {
            const modelSelect = null;
            modelSelect.innerHTML = '<option value="">请选择已训练的模型</option>';
            
            // 只显示训练完成的模型
            const completedModels = data.models.filter(model => model.status === 'completed');
            
            if (completedModels.length === 0) {
                modelSelect.innerHTML = '<option value="">暂无可用模型</option>';
                return;
            }
            
            completedModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = `${model.name} (${model.model_type.toUpperCase()})`;
                modelSelect.appendChild(option);
            });
            
            // 如果URL中有模型参数，自动选择
            const urlParams = new URLSearchParams(window.location.search);
            const modelParam = urlParams.get('model');
            if (modelParam) {
                modelSelect.value = modelParam;
                loadModelInfo(modelParam);
            }
        } else {
            showError('加载模型列表失败: ' + data.error);
        }
    } catch (error) {
        console.error('加载模型列表失败:', error);
        showError('加载模型列表失败: ' + error.message);
    }
}

// 加载模型信息
async function loadModelInfo(modelId) {
    try {
        const response = await fetch(`/api/deep-learning/models/${modelId}`);
        const data = await response.json();
        
        if (data.success) {
            selectedModel = data.model;
            displayModelInfo(selectedModel);
        } else {
            showError('加载模型信息失败: ' + data.error);
        }
    } catch (error) {
        console.error('加载模型信息失败:', error);
        showError('加载模型信息失败: ' + error.message);
    }
}

// 显示模型信息
function displayModelInfo(model) {
    const modelInfoElement = document.getElementById('modelInfo');
    
    const accuracy = model.performance?.accuracy ? (model.performance.accuracy * 100).toFixed(1) + '%' : 'N/A';
    const precision = model.performance?.precision ? (model.performance.precision * 100).toFixed(1) + '%' : 'N/A';
    
    modelInfoElement.innerHTML = `
        <div class="mb-3">
            <h6 class="text-primary">${model.name}</h6>
            <small class="text-muted">${model.id}</small>
        </div>
        
        <div class="row mb-2">
            <div class="col-6">
                <small class="text-muted">模型类型:</small><br>
                <span class="badge bg-info">${model.model_type.toUpperCase()}</span>
            </div>
            <div class="col-6">
                <small class="text-muted">交易品种:</small><br>
                <strong>${model.symbol}</strong>
            </div>
        </div>
        
        <div class="row mb-2">
            <div class="col-6">
                <small class="text-muted">时间框架:</small><br>
                <strong>${model.timeframe}</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">准确率:</small><br>
                <strong class="text-success">${accuracy}</strong>
            </div>
        </div>
        
        <div class="row">
            <div class="col-6">
                <small class="text-muted">精确率:</small><br>
                <strong>${precision}</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">创建时间:</small><br>
                <small>${formatDate(model.created_at)}</small>
            </div>
        </div>
    `;
    
    // 自动设置匹配的交易品种和时间框架
    'XAUUSD' = model.symbol;
    '1h' = model.timeframe;
}

// 清除模型信息
function clearModelInfo() {
    selectedModel = null;
    document.getElementById('modelInfo').innerHTML = '<p class="text-muted text-center py-3">请先选择一个模型</p>';
}

// 提交推理表单
document.getElementById('inferenceForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!selectedModel) {
        showError('请先选择一个模型');
        return;
    }
    
    const inferenceMode = 'realtime';

    // 获取推理配置（包含增强特征和动态风险管理）
    const inferenceConfig = getInferenceConfig();
    const enhancedConfig = getEnhancedFeaturesConfig();
    const riskConfig = getDynamicRiskConfig();

    console.log('推理配置:', inferenceConfig);
    console.log('增强特征配置:', enhancedConfig);
    console.log('动态风险管理配置:', riskConfig);

    // 验证推理配置
    if (!validateInferenceConfig(inferenceConfig)) {
        return;
    }

    const formData = {
        model_id: selectedModel.id,
        symbol: 'XAUUSD',
        timeframe: '1h',
        data_points: parseInt(100),
        inference_mode: inferenceMode,
        use_gpu: true,
        show_confidence: true,
        // 添加交易配置
        trade_config: inferenceConfig,
        // 添加增强特征配置
        ...enhancedConfig,
        // 添加动态风险管理配置
        ...riskConfig
    };

    // 只有非实时推理模式才需要时间范围参数
    if (inferenceMode !== 'realtime') {
        formData.start_date = null.value;
        formData.end_date = null.value;
    }
    
    try {
        const startBtn = null;
        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>推理中...';
        
        // 显示推理状态
        showInferenceStatus('running');
        
        const response = await fetch('/api/deep-learning/inference', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            inferenceResults = result;  // result本身就包含了完整的推理结果
            displayInferenceResults(result);  // 传递完整的result对象
            showInferenceStatus('completed');

            showSuccess('推理完成！');
        } else {
            showError('推理失败: ' + result.error);
            showInferenceStatus('failed');
        }
    } catch (error) {
        console.error('推理失败:', error);
        showError('推理失败: ' + error.message);
        showInferenceStatus('failed');
    } finally {
        const startBtn = null;
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-play me-1"></i>开始推理';
    }
});

// 显示推理状态
function showInferenceStatus(status) {
    const statusCard = document.getElementById('inferenceStatusCard');
    const statusElement = document.getElementById('inferenceStatus');

    // 添加空值检查
    if (!statusCard || !statusElement) {
        console.warn('⚠️ 推理状态元素未找到，跳过状态更新');
        return;
    }

    statusCard.style.display = 'block';

    const statusInfo = {
        'running': {
            icon: 'fas fa-spinner fa-spin text-primary',
            text: '处理中...',
            class: 'text-primary'
        },
        'completed': {
            icon: 'fas fa-check-circle text-success',
            text: '处理完成',
            class: 'text-success'
        },
        'failed': {
            icon: 'fas fa-times-circle text-danger',
            text: '处理失败',
            class: 'text-danger'
        },
        'error': {
            icon: 'fas fa-exclamation-triangle text-warning',
            text: '处理错误',
            class: 'text-warning'
        }
    };

    const info = statusInfo[status] || statusInfo['running'];

    statusElement.innerHTML = `
        <div class="text-center">
            <i class="${info.icon} fa-2x mb-2"></i>
            <p class="${info.class}">${info.text}</p>
        </div>
    `;
}

// 显示推理结果
function displayInferenceResults(results) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsElement = document.getElementById('inferenceResults');
    const tradingSection = document.getElementById('tradingSection');

    resultsCard.style.display = 'block';

    if (results && results.results && results.results.length > 0) {
        // 显示交易配置信息
        let resultsHtml = '';

        if (results.trade_config) {
            const config = results.trade_config;
            const modeText = {
                'signal_only': '仅信号提示',
                'auto_trade': '自动交易',
                'semi_auto': '半自动交易'
            };

            resultsHtml += `
                <div class="alert alert-info mb-3">
                    <h6 class="mb-2"><i class="fas fa-cog me-2"></i>交易配置</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">交易手数:</small> <strong>${config.trade_size}</strong><br>
                            <small class="text-muted">最低置信度:</small> <strong>${(config.min_confidence * 100).toFixed(0)}%</strong><br>
                            <small class="text-muted">交易模式:</small> <strong>${modeText[config.trade_mode] || config.trade_mode}</strong>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">止损:</small> <strong>${config.stop_loss_pips} pips</strong><br>
                            <small class="text-muted">止盈:</small> <strong>${config.take_profit_pips} pips</strong><br>
                            <small class="text-muted">动态止损:</small> <strong>${config.dynamic_sl ? '启用' : '禁用'}</strong>
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示推理结果
        resultsHtml += '<div class="mb-3">';

        results.results.forEach((result, index) => {
            const predictionClass = result.prediction === 'BUY' ? 'text-success' :
                                  result.prediction === 'SELL' ? 'text-danger' : 'text-warning';

            // 为HOLD预测提供特殊的显示逻辑
            const targetDisplay = result.prediction === 'HOLD' ?
                `当前: ${result.current_price} (观望)` :
                `目标: ${result.price_target}`;

            const predictionIcon = result.prediction === 'BUY' ? '📈' :
                                 result.prediction === 'SELL' ? '📉' : '⏸️';

            resultsHtml += `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge bg-secondary">#${index + 1}</span>
                            <strong class="${predictionClass}">
                                ${predictionIcon} ${result.prediction}
                            </strong>
                        </div>
                        <div class="text-end">
                            <div class="small text-muted">${result.timestamp.substring(0, 19)}</div>
                            <div class="fw-bold">${targetDisplay}</div>
                        </div>
                    </div>
                    ${result.confidence ? `
                        <div class="mt-1">
                            <small class="text-muted">置信度: </small>
                            <span class="badge ${result.confidence > 0.8 ? 'bg-success' : result.confidence > 0.6 ? 'bg-warning' : 'bg-secondary'}">
                                ${(result.confidence * 100).toFixed(1)}%
                            </span>
                        </div>
                    ` : ''}
                    ${result.analysis && result.analysis.reason ? `
                        <div class="mt-1">
                            <small class="text-muted d-block">${result.analysis.reason}</small>
                        </div>
                    ` : ''}
                </div>
            `;
        });

        resultsHtml += '</div>';

        // 显示特征重要性分析结果（如果有）
        if (results.feature_importance) {
            resultsHtml += displayFeatureImportanceHTML(results.feature_importance);
        }

        // 显示增强特征配置信息（如果启用）
        if (results.use_enhanced_features) {
            resultsHtml += displayEnhancedFeaturesInfo(results);
        }

        // 显示动态风险管理信息（如果启用）
        if (results.enable_dynamic_risk_management) {
            resultsHtml += displayDynamicRiskInfo(results);
        }

        resultsElement.innerHTML = resultsHtml;

        // 更新最新推理结果到交易区域
        updateLatestInferenceResult(results.results[results.results.length - 1]);

    } else {
        resultsElement.innerHTML = `
            <div class="alert alert-warning">
                <h6>推理完成</h6>
                <p>未获得有效的推理结果，请检查模型状态或重新尝试。</p>
            </div>
        `;
    }
}

// 应用时间范围预设


// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 初始化默认时间范围
function initializeDefaultTimeRange() {
    const now = new Date();
    const endDate = new Date(now);
    const startDate = new Date(now);
    startDate.setMonth(now.getMonth() - 1); // 默认最近一个月

    null.value = startDate.toISOString().split('T')[0];
    null.value = endDate.toISOString().split('T')[0];
}

// 根据推理模式切换时间范围区域的显示


// 重置表单


// 加载示例数据




// 导出结果
function exportResults() {
    // 检查是否有推理结果
    if (inferenceResults) {
        // 如果有推理结果，导出推理结果
        exportInferenceResults();
        return;
    }

    // 检查是否有参数优化结果
    const backtestCard = document.getElementById('backtestCard');
    if (backtestCard && backtestCard.style.display !== 'none') {
        // 如果有参数优化结果，导出优化结果
        exportOptimizationResults();
        return;
    }

    // 如果都没有，提示用户
    showError('没有可导出的结果，请先执行模型推理或参数优化');
}

// 导出推理结果
function exportInferenceResults() {
    try {
        if (!inferenceResults) {
            showError('没有可导出的推理结果');
            return;
        }

        showInfo('正在准备导出推理结果...');

        // 准备CSV数据
        const csvData = [];

        // 添加标题行
        csvData.push([
            '时间', '模型ID', '交易品种', '预测方向', '置信度(%)',
            '当前价格', '建议操作', '风险等级', '预测理由'
        ]);

        // 添加推理结果数据
        const timestamp = new Date().toLocaleString('zh-CN');
        const selectedModel = getSelectedModel();
        const symbol = 'XAUUSD' || 'XAUUSD';

        csvData.push([
            timestamp,
            selectedModel ? selectedModel.id : '未知',
            symbol,
            inferenceResults.prediction === 1 ? '上涨' : '下跌',
            (inferenceResults.confidence * 100).toFixed(1),
            inferenceResults.current_price || '未知',
            inferenceResults.recommendation || '持有',
            inferenceResults.risk_level || '中等',
            inferenceResults.reasoning || '基于深度学习模型分析'
        ]);

        // 转换为CSV格式
        const csvContent = csvData.map(row =>
            row.map(field => `"${field}"`).join(',')
        ).join('\n');

        // 创建并下载文件
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `模型推理结果_${symbol}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
        document.body.appendChild(a);
        a.click();

        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        showSuccess('推理结果已成功导出');

    } catch (error) {
        console.error('❌ 导出推理结果失败:', error);
        showError('导出失败，请重试');
    }
}



// 显示成功消息
function showSuccess(message) {
    alert('✅ ' + message);
}

// 显示错误消息
function showError(message) {
    alert('❌ ' + message);
}

// 显示信息消息
function showInfo(message) {
    alert('ℹ️ ' + message);
}

// ==================== 回测相关函数 ====================

// 获取表单数据
function getFormData() {
    try {
        const symbol = document.getElementById('symbol')?.value;
        const timeframe = document.getElementById('timeframe')?.value;
        const startDate = null?.value;
        const endDate = null?.value;

        console.log('获取表单数据:', { symbol, timeframe, startDate, endDate });

        if (!symbol || !timeframe) {
            console.error('❌ 缺少必需的表单字段');
            return null;
        }

        return {
            symbol: symbol,
            timeframe: timeframe,
            start_date: startDate || '2024-07-01',
            end_date: endDate || '2024-07-29'
        };
    } catch (error) {
        console.error('❌ 获取表单数据失败:', error);
        return null;
    }
}

// 获取选择的模型
function getSelectedModel() {
    try {
        const modelSelect = null;
        if (!modelSelect || !modelSelect.value) {
            console.error('❌ 没有选择模型');
            return null;
        }

        const selectedOption = modelSelect.options[modelSelect.selectedIndex];
        if (!selectedOption) {
            console.error('❌ 选择的模型选项无效');
            return null;
        }

        const modelData = {
            id: modelSelect.value,
            name: selectedOption.text,
            symbol: selectedOption.dataset.symbol || '',
            timeframe: selectedOption.dataset.timeframe || ''
        };

        console.log('选择的模型:', modelData);
        return modelData;
    } catch (error) {
        console.error('❌ 获取选择模型失败:', error);
        return null;
    }
}

// 切换推理配置面板


// 应用推理预设配置


// 获取推理配置
function getInferenceConfig() {
    // 现在使用交易配置的参数
    return {
        trade_size: parseFloat(document.getElementById('tradingLotSize').value || '0.01'),
        min_confidence: parseFloat(document.getElementById('minConfidence').value || '0.7'),
        stop_loss_pips: parseInt(document.getElementById('stopLossPips').value || '50'),
        take_profit_pips: parseInt(document.getElementById('takeProfitPips').value || '100'),
        trade_mode: 'auto',
        dynamic_sl: document.getElementById('enableDynamicSL').checked,
        trailing_stop: document.getElementById('enableTrailingStop').checked
    };
}

// 验证推理配置
function validateInferenceConfig(config) {
    if (config.trade_size < 0.01 || config.trade_size > 10) {
        showError('交易手数必须在0.01-10之间');
        return false;
    }

    if (config.min_confidence < 0.05 || config.min_confidence > 0.99) {
        showError('最低置信度必须在0.05-0.99之间');
        return false;
    }



    if (config.take_profit_pips < 10 || config.take_profit_pips > 1000) {
        showError('止盈必须在10-1000 pips之间');
        return false;
    }

    return true;
}

// 应用交易预设配置
function applyTradingPreset() {
    const preset = document.getElementById('tradingPreset').value;

    switch (preset) {
        case 'conservative':
            // 保守型：高置信度，黄金适配止损，小手数
            document.getElementById('tradingLotSize').value = 0.01;
            document.getElementById('minConfidence').value = 0.6;
            document.getElementById('stopLossPips').value = 800;   // 黄金保守型8美元
            document.getElementById('takeProfitPips').value = 1600; // 黄金保守型16美元
            document.getElementById('maxPositions').value = 2;
            document.getElementById('enableDynamicSL').checked = true;
            document.getElementById('enableInferenceTrailingStop').checked = true; // 启用移动止损
            document.getElementById('inferenceTrailingStopDistance').value = 60; // 黄金保守型止损距离
            document.getElementById('inferenceTrailingStopStep').value = 5; // 保守型较小的止损步长
            document.getElementById('enableAutoTrading').checked = true; // 启用自动交易
            document.getElementById('enableCliffBrake').checked = false; // 保守型不启用悬崖勒马
            document.getElementById('enableNewsFilter').checked = true; // 保守型启用新闻过滤
            toggleInferenceTrailingStopConfig(); // 更新移动止损配置显示
            showSuccess('已应用保守型配置：高置信度(60%)，小止损(30pips)，关闭悬崖勒马，开启新闻过滤');
            break;

        case 'balanced':
            // 平衡型：中等置信度，黄金适配止损，标准手数
            document.getElementById('tradingLotSize').value = 0.01;
            document.getElementById('minConfidence').value = 0.3;
            document.getElementById('stopLossPips').value = 1000;  // 黄金推荐10美元止损
            document.getElementById('takeProfitPips').value = 2000; // 黄金推荐20美元止盈
            document.getElementById('maxPositions').value = 4;
            document.getElementById('enableDynamicSL').checked = true;
            document.getElementById('enableInferenceTrailingStop').checked = true;
            document.getElementById('inferenceTrailingStopDistance').value = 80; // 黄金适配止损距离
            document.getElementById('inferenceTrailingStopStep').value = 10; // 平衡型标准止损步长
            document.getElementById('enableAutoTrading').checked = true; // 半自动
            document.getElementById('enableCliffBrake').checked = true; // 平衡型默认开启悬崖勒马
            document.getElementById('enableNewsFilter').checked = false; // 平衡型默认关闭新闻过滤
            toggleInferenceTrailingStopConfig(); // 更新移动止损配置显示
            showSuccess('已应用平衡型配置：中等置信度(30%)，标准止损(50pips)，开启悬崖勒马，关闭新闻过滤');
            break;

        case 'aggressive':
            // 激进型：低置信度，黄金适配大止损，大手数
            document.getElementById('tradingLotSize').value = 0.02;
            document.getElementById('minConfidence').value = 0.3;
            document.getElementById('stopLossPips').value = 1500;  // 黄金激进型15美元
            document.getElementById('takeProfitPips').value = 3000; // 黄金激进型30美元
            document.getElementById('maxPositions').value = 6;
            document.getElementById('enableDynamicSL').checked = true;
            document.getElementById('enableInferenceTrailingStop').checked = true;
            document.getElementById('inferenceTrailingStopDistance').value = 120; // 黄金激进型止损距离
            document.getElementById('inferenceTrailingStopStep').value = 15; // 激进型较大的止损步长
            document.getElementById('enableAutoTrading').checked = true; // 全自动
            document.getElementById('enableCliffBrake').checked = true; // 激进型启用悬崖勒马
            document.getElementById('enableNewsFilter').checked = false; // 激进型关闭新闻过滤
            toggleInferenceTrailingStopConfig(); // 更新移动止损配置显示
            showSuccess('已应用激进型配置：置信度(30%)，大止损(80pips)，开启悬崖勒马，关闭新闻过滤');
            break;

        case 'custom':
            showInfo('已切换到自定义配置模式，请手动调整各项参数');
            break;
    }
}



// 切换移动止损配置显示
function toggleTrailingStopConfig() {
    const checkbox = document.getElementById('enableTrailingStop');
    const configPanel = document.getElementById('trailingStopConfig');

    if (checkbox.checked) {
        configPanel.style.display = 'block';
        configPanel.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    } else {
        configPanel.style.display = 'none';
    }
}

// 切换AI推理交易移动止损配置显示
function toggleInferenceTrailingStopConfig() {
    const checkbox = document.getElementById('enableInferenceTrailingStop');
    const configPanel = document.getElementById('inferenceTrailingStopConfig');

    if (checkbox && configPanel) {
        if (checkbox.checked) {
            configPanel.style.display = 'block';
        } else {
            configPanel.style.display = 'none';
        }
    }
}

// 应用回测预设配置
function applyBacktestPreset() {
    const preset = document.getElementById('backtestPreset').value;

    switch (preset) {
        case 'conservative':
            document.getElementById('backtestMinConfidence').value = 0.2;
            document.getElementById('backtestStopLoss').value = 800;   // 黄金保守型8美元
            document.getElementById('backtestTakeProfit').value = 1600; // 黄金保守型16美元
            document.getElementById('backtestLotSize').value = 0.01;
            document.getElementById('enableTrailingStop').checked = true;  // 保守型默认开启移动止损
            document.getElementById('trailingStopDistance').value = 15;    // 保守型较小的触发距离
            document.getElementById('trailingStopStep').value = 5;         // 保守型较小的跟踪步长
            document.getElementById('backtestCliffBrake').checked = false;   // 保守型默认关闭悬崖勒马
            toggleTrailingStopConfig(); // 更新移动止损配置显示
            showInfo('已应用保守型配置：高置信度，小止损，启用保守移动止损');
            break;
        case 'balanced':
            document.getElementById('backtestMinConfidence').value = 0.3;
            document.getElementById('backtestStopLoss').value = 1000;  // 黄金适配
            document.getElementById('backtestTakeProfit').value = 2000; // 黄金适配
            document.getElementById('backtestLotSize').value = 0.01;
            document.getElementById('enableTrailingStop').checked = true;  // 平衡型默认开启移动止损
            document.getElementById('trailingStopDistance').value = 20;    // 设置默认触发距离
            document.getElementById('trailingStopStep').value = 10;        // 设置默认跟踪步长
            document.getElementById('backtestCliffBrake').checked = true;    // 平衡型默认开启悬崖勒马
            toggleTrailingStopConfig(); // 更新移动止损配置显示
            showInfo('已应用平衡型配置：中等置信度(30%)，标准止损，启用移动止损和悬崖勒马');
            break;
        case 'aggressive':
            document.getElementById('backtestMinConfidence').value = 0.05;
            document.getElementById('backtestStopLoss').value = 1500;  // 黄金激进型15美元
            document.getElementById('backtestTakeProfit').value = 3000; // 黄金激进型30美元
            document.getElementById('backtestLotSize').value = 0.02;
            document.getElementById('enableTrailingStop').checked = true;  // 激进型默认开启移动止损
            document.getElementById('trailingStopDistance').value = 30;    // 激进型更大的触发距离
            document.getElementById('trailingStopStep').value = 15;        // 激进型更大的跟踪步长
            document.getElementById('backtestCliffBrake').checked = true;    // 激进型默认开启悬崖勒马
            toggleTrailingStopConfig(); // 更新移动止损配置显示
            showInfo('已应用激进型配置：低置信度，大止损，启用所有高级功能');
            break;
        case 'custom':
        default:
            showInfo('已切换到自定义配置模式，请手动调整各项参数');
            break;
    }
}





// 开始交易回测
async function startBacktest() {
    console.log('🔄 startBacktest 函数被调用');

    const selectedModel = getSelectedModel();
    console.log('选择的模型:', selectedModel);

    if (!selectedModel) {
        console.error('❌ 没有选择模型');
        showError('请先选择一个模型');
        return;
    }

    const formData = getFormData();
    console.log('表单数据:', formData);

    if (!formData) {
        console.error('❌ 表单数据不完整');
        showError('请填写完整的推理参数');
        return;
    }

    // 获取回测配置
    const backtestConfig = getBacktestConfig();
    console.log('回测配置:', backtestConfig);

    // 验证回测配置
    if (!validateBacktestConfig(backtestConfig)) {
        return;
    }

    try {
        showInferenceStatus('running');
        document.getElementById('startBacktestBtn').disabled = true;

        console.log('🔄 开始交易回测...');

        const backtestData = {
            model_id: selectedModel.id,
            symbol: formData.symbol,
            timeframe: formData.timeframe,
            start_date: formData.start_date,
            end_date: formData.end_date,
            initial_balance: backtestConfig.initial_balance,
            lot_size: backtestConfig.lot_size,
            stop_loss_pips: backtestConfig.stop_loss_pips,
            take_profit_pips: backtestConfig.take_profit_pips,
            min_confidence: backtestConfig.min_confidence,
            cliff_brake_enabled: backtestConfig.cliff_brake_enabled,
            trailing_stop_enabled: backtestConfig.trailing_stop_enabled,
            trailing_stop_distance: backtestConfig.trailing_stop_distance,
            trailing_stop_step: backtestConfig.trailing_stop_step
        };

        const response = await fetch('/api/deep-learning/inference-backtest', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(backtestData)
        });

        const result = await response.json();

        console.log('回测API响应:', result);

        if (result.success) {
            console.log('回测统计:', result.statistics);
            console.log('交易记录数量:', result.trades ? result.trades.length : 0);

            displayBacktestResults(result);
            showInferenceStatus('completed');
            showSuccess('回测完成！');
        } else {
            console.error('回测失败:', result.error);
            showError(`回测失败: ${result.error}`);
            showInferenceStatus('error');
        }

    } catch (error) {
        console.error('回测失败:', error);
        showError(`回测失败: ${error.message}`);
        showInferenceStatus('error');
    } finally {
        document.getElementById('startBacktestBtn').disabled = false;
    }
}

// 加载保存的参数优化结果
async function loadSavedOptimizationResults() {
    if (!selectedModel) {
        return;
    }

    try {
        const riskPreference = document.getElementById('riskPreference').value;

        const response = await fetch(`/api/deep-learning/saved-optimization-results?model_id=${selectedModel.id}&symbol=${selectedModel.symbol}&timeframe=${selectedModel.timeframe}&risk_preference=${riskPreference}`);
        const result = await response.json();

        if (result.success) {
            console.log('✅ 找到保存的优化结果:', result);
            showInfo(`加载了保存的参数优化结果 (${new Date(result.created_at).toLocaleString()})`);

            // 显示保存的结果
            displayOptimizationResults(result);

            // 显示加载提示
            const backtestCard = document.getElementById('backtestCard');
            if (backtestCard) {
                const loadedBadge = document.createElement('div');
                loadedBadge.className = 'alert alert-success alert-dismissible fade show mt-2';
                loadedBadge.innerHTML = `
                    <i class="fas fa-history me-2"></i>
                    <strong>已加载保存的优化结果</strong> - 创建时间: ${new Date(result.created_at).toLocaleString()}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                backtestCard.insertBefore(loadedBadge, backtestCard.firstChild);
            }
        } else {
            console.log('ℹ️ 未找到保存的优化结果');
        }
    } catch (error) {
        console.error('❌ 加载保存的优化结果失败:', error);
    }
}

// 切换排序偏好
async function changeSortPreference() {
    const selectedModel = getSelectedModel();
    if (!selectedModel) {
        return;
    }

    const sortPreference = document.getElementById('resultsSortPreference').value;
    console.log('🔄 切换排序偏好:', sortPreference);

    try {
        showInfo('正在重新排序参数优化结果...');

        // 使用快速重新排序（不重新执行回测）
        const response = await fetch('/api/deep-learning/parameter-optimization', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model_id: selectedModel.id,
                symbol: selectedModel.symbol,
                timeframe: selectedModel.timeframe,
                optimization_period: document.getElementById('optimizationPeriod').value,
                risk_preference: sortPreference,
                resort_only: true  // 关键：只重新排序，不重新执行回测
            })
        });

        const result = await response.json();

        if (result.success) {
            console.log('✅ 重新排序成功');
            displayOptimizationResults(result);

            // 更新风险偏好选择器
            document.getElementById('riskPreference').value = sortPreference;

            if (result.is_resorted) {
                showSuccess(`已快速重新排序参数 (${getSortPreferenceName(sortPreference)})`);
            } else {
                showSuccess(`已按${getSortPreferenceName(sortPreference)}重新排序参数`);
            }
        } else {
            showError(`重新排序失败: ${result.error}`);
        }
    } catch (error) {
        console.error('❌ 切换排序偏好失败:', error);
        showError('切换排序偏好失败，请重试');
    }
}

// 获取排序偏好名称
function getSortPreferenceName(preference) {
    const names = {
        'balanced': '平衡模式',
        'high_return_high_risk': '高收益高风险',
        'medium_return_low_risk': '中等收益低风险',
        'low_return_ultra_low_risk': '低收益超低风险'
    };
    return names[preference] || preference;
}

// 导出参数优化结果
async function exportOptimizationResults() {
    const selectedModel = getSelectedModel();
    if (!selectedModel) {
        showError('请先选择一个模型');
        return;
    }

    // 检查是否有优化结果
    const backtestCard = document.getElementById('backtestCard');
    if (!backtestCard || backtestCard.style.display === 'none') {
        showError('没有可导出的参数优化结果，请先执行参数优化');
        return;
    }

    try {
        showInfo('正在准备导出数据...');

        const riskPreference = document.getElementById('riskPreference').value;
        const optimizationPeriod = document.getElementById('optimizationPeriod').value;

        // 请求导出数据
        const response = await fetch('/api/deep-learning/export-optimization-results', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model_id: selectedModel.id,
                symbol: selectedModel.symbol,
                timeframe: selectedModel.timeframe,
                optimization_period: optimizationPeriod,
                risk_preference: riskPreference
            })
        });

        if (response.ok) {
            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'parameter_optimization_results.csv';
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // 下载文件
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showSuccess('参数优化结果已成功导出');
        } else {
            const errorData = await response.json();
            showError(`导出失败: ${errorData.error || '未知错误'}`);
        }
    } catch (error) {
        console.error('❌ 导出参数优化结果失败:', error);
        showError('导出失败，请重试');
    }
}

// 开始参数优化
async function startParameterOptimization() {
    console.log('🔍 开始参数优化');

    // 获取表单数据
    const selectedModel = getSelectedModel();
    if (!selectedModel) {
        showError('请先选择一个训练完成的模型');
        return;
    }

    // 获取优化周期和风险偏好
    const optimizationPeriod = document.getElementById('optimizationPeriod').value;
    const riskPreference = document.getElementById('riskPreference').value;

    try {
        // 更新按钮状态
        const optimizationBtn = document.getElementById('parameterOptimizationBtn');
        optimizationBtn.disabled = true;
        optimizationBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>优化中...';

        showInfo('正在进行参数优化，这可能需要几分钟时间...');
        showInferenceStatus('running');

        // 发送优化请求
        const response = await fetch('/api/deep-learning/parameter-optimization', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model_id: selectedModel.id,
                symbol: selectedModel.symbol,
                timeframe: selectedModel.timeframe,
                optimization_period: optimizationPeriod,
                risk_preference: riskPreference
            })
        });

        const result = await response.json();

        if (result.success) {
            console.log('✅ 参数优化完成:', result);
            showSuccess(`参数优化完成！测试了 ${result.total_combinations} 个参数组合，成功 ${result.successful_combinations} 个`);

            // 显示优化结果
            displayOptimizationResults(result);
            showInferenceStatus('completed');
        } else {
            console.error('❌ 参数优化失败:', result.error);
            showError('参数优化失败: ' + result.error);
            showInferenceStatus('error');
        }

    } catch (error) {
        console.error('❌ 参数优化失败:', error);
        showError('参数优化失败: ' + error.message);
        showInferenceStatus('error');
    } finally {
        // 恢复按钮状态
        const optimizationBtn = document.getElementById('parameterOptimizationBtn');
        optimizationBtn.disabled = false;
        optimizationBtn.innerHTML = '<i class="fas fa-cogs me-1"></i>参数优化';
    }
}

// 显示回测结果
function displayBacktestResults(results) {
    console.log('开始显示回测结果:', results);

    const backtestCard = document.getElementById('backtestCard');
    const backtestStats = document.getElementById('backtestStats');
    const backtestResults = document.getElementById('backtestResults');

    console.log('回测卡片元素:', backtestCard);
    console.log('统计元素:', backtestStats);
    console.log('结果元素:', backtestResults);

    // 添加空值检查
    if (!backtestCard || !backtestStats || !backtestResults) {
        console.error('❌ 回测结果显示元素未找到');
        console.error('backtestCard:', backtestCard);
        console.error('backtestStats:', backtestStats);
        console.error('backtestResults:', backtestResults);
        showError('回测结果显示失败：页面元素未找到');
        return;
    }

    // 显示回测卡片
    backtestCard.style.display = 'block';

    // 滚动到回测结果
    backtestCard.scrollIntoView({ behavior: 'smooth' });

    // 显示详细统计信息
    const stats = results.statistics;
    backtestStats.innerHTML = `
        <!-- 第一行：主要指标 -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">总收益率</div>
                                <div class="h5 mb-0">${stats.total_return.toFixed(2)}%</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">净盈利</div>
                                <div class="h5 mb-0">$${stats.net_profit.toFixed(2)}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-dollar-sign fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">胜率</div>
                                <div class="h5 mb-0">${stats.win_rate.toFixed(1)}%</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-trophy fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">总交易数</div>
                                <div class="h5 mb-0">${stats.total_trades}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exchange-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行：盈亏详情 -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">盈利单数</div>
                                <div class="h5 mb-0">${stats.winning_trades} 单</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-arrow-up fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">亏损单数</div>
                                <div class="h5 mb-0">${stats.losing_trades} 单</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-arrow-down fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">总盈利</div>
                                <div class="h5 mb-0">$${stats.gross_profit.toFixed(2)}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-plus fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">总亏损</div>
                                <div class="h5 mb-0">$${stats.gross_loss.toFixed(2)}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-minus fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第三行：高级统计 -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="card bg-secondary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">平均盈利</div>
                                <div class="h5 mb-0">$${stats.average_win.toFixed(2)}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-bar fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-secondary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">平均亏损</div>
                                <div class="h5 mb-0">$${stats.average_loss.toFixed(2)}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-chart-bar fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">盈利因子</div>
                                <div class="h5 mb-0">${stats.profit_factor === Infinity ? '∞' : stats.profit_factor.toFixed(2)}</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calculator fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <div class="small">最大回撤</div>
                                <div class="h5 mb-0">${stats.max_drawdown.toFixed(2)}%</div>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细统计表格 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-table me-2"></i>详细统计分析</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>初始资金</strong></td>
                                        <td>$${stats.initial_balance.toFixed(2)}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>最终余额</strong></td>
                                        <td>$${stats.final_balance.toFixed(2)}</td>
                                    </tr>
                                    <tr class="${stats.net_profit >= 0 ? 'table-success' : 'table-danger'}">
                                        <td><strong>净盈利 (盈利-亏损)</strong></td>
                                        <td><strong>$${stats.net_profit.toFixed(2)}</strong></td>
                                    </tr>
                                    <tr>
                                        <td><strong>盈利单数</strong></td>
                                        <td class="text-success">${stats.winning_trades} 单</td>
                                    </tr>
                                    <tr>
                                        <td><strong>亏损单数</strong></td>
                                        <td class="text-danger">${stats.losing_trades} 单</td>
                                    </tr>
                                    <tr>
                                        <td><strong>平仓单数</strong></td>
                                        <td class="text-muted">${stats.break_even_trades || 0} 单</td>
                                    </tr>
                                    <tr>
                                        <td><strong>最大单笔盈利</strong></td>
                                        <td class="text-success">$${stats.max_win.toFixed(2)}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>最大单笔亏损</strong></td>
                                        <td class="text-danger">$${Math.abs(stats.max_loss).toFixed(2)}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>胜率</strong></td>
                                        <td>${stats.win_rate.toFixed(1)}%</td>
                                    </tr>
                                    <tr>
                                        <td><strong>平均盈利</strong></td>
                                        <td class="text-success">$${stats.average_win.toFixed(2)}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>平均亏损</strong></td>
                                        <td class="text-danger">$${stats.average_loss.toFixed(2)}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>盈亏比</strong></td>
                                        <td>${stats.reward_risk_ratio === Infinity ? '∞' : stats.reward_risk_ratio.toFixed(2)}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>盈利因子</strong></td>
                                        <td>${stats.profit_factor === Infinity ? '∞' : stats.profit_factor.toFixed(2)}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>最大连续盈利</strong></td>
                                        <td class="text-success">${stats.max_consecutive_wins || 0} 单</td>
                                    </tr>
                                    <tr>
                                        <td><strong>最大连续亏损</strong></td>
                                        <td class="text-danger">${stats.max_consecutive_losses || 0} 单</td>
                                    </tr>
                                    <tr>
                                        <td><strong>最大回撤</strong></td>
                                        <td class="text-warning">${stats.max_drawdown.toFixed(2)}%</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- 盈亏分布图表 -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>盈亏分布</h6>
                                <div class="progress mb-2" style="height: 30px;">
                                    <div class="progress-bar bg-success" role="progressbar"
                                         style="width: ${stats.total_trades > 0 ? (stats.winning_trades / stats.total_trades * 100) : 0}%">
                                        盈利: ${stats.winning_trades}单
                                    </div>
                                    <div class="progress-bar bg-danger" role="progressbar"
                                         style="width: ${stats.total_trades > 0 ? (stats.losing_trades / stats.total_trades * 100) : 0}%">
                                        亏损: ${stats.losing_trades}单
                                    </div>
                                    ${stats.break_even_trades > 0 ? `
                                    <div class="progress-bar bg-secondary" role="progressbar"
                                         style="width: ${stats.total_trades > 0 ? (stats.break_even_trades / stats.total_trades * 100) : 0}%">
                                        平仓: ${stats.break_even_trades}单
                                    </div>
                                    ` : ''}
                                </div>
                                <small class="text-muted">
                                    盈利占比: ${stats.total_trades > 0 ? (stats.winning_trades / stats.total_trades * 100).toFixed(1) : 0}% |
                                    亏损占比: ${stats.total_trades > 0 ? (stats.losing_trades / stats.total_trades * 100).toFixed(1) : 0}%
                                    ${stats.break_even_trades > 0 ? ` | 平仓占比: ${(stats.break_even_trades / stats.total_trades * 100).toFixed(1)}%` : ''}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 显示详细交易记录
    let tradesHtml = `
        <h6 class="mb-3">交易记录</h6>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>预测</th>
                        <th>置信度</th>
                        <th>手数</th>
                        <th>入场价</th>
                        <th>出场价</th>
                        <th>盈亏</th>
                        <th>累计余额</th>
                    </tr>
                </thead>
                <tbody>
    `;

    results.trades.forEach(trade => {
        const profitClass = trade.profit > 0 ? 'text-success' : trade.profit < 0 ? 'text-danger' : 'text-muted';
        const predictionClass = trade.prediction === 'BUY' ? 'text-success' : 'text-danger';

        tradesHtml += `
            <tr>
                <td>${trade.timestamp.substring(0, 19)}</td>
                <td><span class="badge bg-secondary ${predictionClass}">${trade.prediction}</span></td>
                <td>${(trade.confidence * 100).toFixed(1)}%</td>
                <td>${trade.lot_size || 0.01}</td>
                <td>${trade.entry_price.toFixed(5)}</td>
                <td>${trade.exit_price.toFixed(5)}</td>
                <td class="${profitClass}">$${trade.profit.toFixed(2)}</td>
                <td>$${trade.balance.toFixed(2)}</td>
            </tr>
        `;
    });

    tradesHtml += `
                </tbody>
            </table>
        </div>
    `;

    backtestResults.innerHTML = tradesHtml;
}

// 显示参数优化结果
function displayOptimizationResults(results) {
    console.log('📊 显示参数优化结果:', results);

    // 获取结果显示区域
    const backtestCard = document.getElementById('backtestCard');
    const backtestStats = document.getElementById('backtestStats');
    const backtestResults = document.getElementById('backtestResults');

    // 添加空值检查
    if (!backtestCard || !backtestStats || !backtestResults) {
        console.error('❌ 参数优化结果显示元素未找到');
        console.error('backtestCard:', backtestCard);
        console.error('backtestStats:', backtestStats);
        console.error('backtestResults:', backtestResults);
        showError('参数优化结果显示失败：页面元素未找到');
        return;
    }

    // 显示回测卡片
    backtestCard.style.display = 'block';

    // 滚动到结果
    backtestCard.scrollIntoView({ behavior: 'smooth' });

    // 设置排序偏好选择器的值
    if (results.risk_preference) {
        const sortSelector = document.getElementById('resultsSortPreference');
        if (sortSelector) {
            sortSelector.value = results.risk_preference;
        }
    }

    // 显示优化摘要
    const report = results.optimization_report;
    const bestParams = results.best_parameters;

    backtestStats.innerHTML = `
        <!-- 优化摘要 -->
        <div class="row mb-3">
            <div class="col-md-8">
                <h5 class="text-primary">
                    <i class="fas fa-trophy me-2"></i>参数优化结果
                </h5>
                <p class="text-muted">
                    优化周期: ${results.optimization_period === 'week' ? '一周' : '一个月'}
                    (${results.date_range.start_date} 至 ${results.date_range.end_date})
                </p>
            </div>
            <div class="col-md-4">
                <div class="card border-info">
                    <div class="card-header bg-info text-white py-2">
                        <h6 class="mb-0"><i class="fas fa-sliders-h me-1"></i>排序方式</h6>
                    </div>
                    <div class="card-body py-2">
                        <select class="form-select form-select-sm mb-2" id="resultsSortPreference" onchange="changeSortPreference()">
                            <option value="balanced">平衡模式</option>
                            <option value="high_return_high_risk">高收益高风险</option>
                            <option value="medium_return_low_risk">中等收益低风险</option>
                            <option value="low_return_ultra_low_risk">低收益超低风险</option>
                        </select>
                        <div class="form-text mb-2">选择参数排序偏好</div>
                        <button class="btn btn-sm btn-success w-100" onclick="exportOptimizationResults()">
                            <i class="fas fa-download me-1"></i>导出结果
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最佳参数 -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">🏆 最佳参数组合</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>手数:</strong> ${bestParams.lot_size}<br>
                                <strong>止损:</strong> ${bestParams.stop_loss_pips} pips<br>
                                <strong>止盈:</strong> ${bestParams.take_profit_pips} pips<br>
                                <strong>置信度:</strong> ${(bestParams.min_confidence * 100).toFixed(0)}%
                            </div>
                            <div class="col-6">
                                <strong>悬崖勒马:</strong> ${bestParams.cliff_brake_enabled ? '启用' : '禁用'}<br>
                                <strong>移动止损:</strong> ${bestParams.trailing_stop_enabled ? '启用' : '禁用'}<br>
                                <strong>止损距离:</strong> ${bestParams.trailing_stop_distance} pips<br>
                                <strong>止损步长:</strong> ${bestParams.trailing_stop_step} pips
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">📈 最佳表现</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>综合评分:</strong> ${report.summary.best_score}/100<br>
                                <strong>收益率:</strong> ${report.summary.best_return.toFixed(2)}%<br>
                                <strong>胜率:</strong> ${report.summary.best_win_rate.toFixed(1)}%
                            </div>
                            <div class="col-6">
                                <strong>测试组合:</strong> ${report.summary.total_combinations_tested}<br>
                                <strong>平均收益:</strong> ${report.summary.average_return.toFixed(2)}%<br>
                                <strong>平均胜率:</strong> ${report.summary.average_win_rate.toFixed(1)}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 显示排名表格
    backtestResults.innerHTML = `
        <div class="row">
            <div class="col-md-12">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-list-ol me-2"></i>参数组合排名 (前10名)
                </h6>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>排名</th>
                                <th>评分</th>
                                <th>收益率</th>
                                <th>胜率</th>
                                <th>盈利单</th>
                                <th>亏损单</th>
                                <th>手数</th>
                                <th>止损</th>
                                <th>止盈</th>
                                <th>置信度</th>
                                <th>悬崖勒马</th>
                                <th>移动止损</th>
                                <th>止损距离</th>
                                <th>止损步长</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${results.optimization_results.slice(0, 10).map(result => `
                                <tr ${result.rank === 1 ? 'class="table-success"' : ''}>
                                    <td>
                                        ${result.rank === 1 ? '🏆' : result.rank === 2 ? '🥈' : result.rank === 3 ? '🥉' : result.rank}
                                    </td>
                                    <td><strong>${result.score}</strong></td>
                                    <td class="${result.total_return > 0 ? 'text-success' : 'text-danger'}">
                                        ${result.total_return.toFixed(2)}%
                                    </td>
                                    <td>${result.win_rate.toFixed(1)}%</td>
                                    <td class="text-success">
                                        <strong>${result.winning_trades || 0}</strong>
                                    </td>
                                    <td class="text-danger">
                                        <strong>${result.losing_trades || 0}</strong>
                                    </td>
                                    <td>${result.parameters.lot_size}</td>
                                    <td>${result.parameters.stop_loss_pips}</td>
                                    <td>${result.parameters.take_profit_pips}</td>
                                    <td>${(result.parameters.min_confidence * 100).toFixed(0)}%</td>
                                    <td>
                                        ${result.parameters.cliff_brake_enabled ?
                                            '<span class="badge bg-success">启用</span>' :
                                            '<span class="badge bg-secondary">禁用</span>'}
                                    </td>
                                    <td>
                                        ${result.parameters.trailing_stop_enabled ?
                                            '<span class="badge bg-success">启用</span>' :
                                            '<span class="badge bg-secondary">禁用</span>'}
                                    </td>
                                    <td>${result.parameters.trailing_stop_distance} pips</td>
                                    <td>${result.parameters.trailing_stop_step} pips</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary"
                                                onclick="applyOptimizedParameters(${JSON.stringify(result.parameters).replace(/"/g, '&quot;')})">
                                            应用
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 参数建议 -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-lightbulb me-2"></i>优化建议
                    </h6>
                    <ul class="mb-0">
                        ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            </div>
        </div>
    `;
}

// 应用优化后的参数
function applyOptimizedParameters(params) {
    console.log('📝 应用优化参数:', params);

    // 应用到回测配置
    document.getElementById('backtestLotSize').value = params.lot_size;
    document.getElementById('backtestStopLoss').value = params.stop_loss_pips;
    document.getElementById('backtestTakeProfit').value = params.take_profit_pips;
    document.getElementById('backtestMinConfidence').value = params.min_confidence;

    // 应用移动止损设置
    const trailingStopCheckbox = document.getElementById('enableTrailingStop');
    if (trailingStopCheckbox && params.trailing_stop_enabled !== undefined) {
        trailingStopCheckbox.checked = params.trailing_stop_enabled;

        if (params.trailing_stop_enabled) {
            document.getElementById('trailingStopDistance').value = params.trailing_stop_distance || 20;
            document.getElementById('trailingStopStep').value = params.trailing_stop_step || 10;
        }

        toggleTrailingStopConfig(); // 更新配置面板显示
    }

    // 应用悬崖勒马设置
    const cliffBrakeCheckbox = document.getElementById('backtestCliffBrake');
    if (cliffBrakeCheckbox) {
        cliffBrakeCheckbox.checked = params.cliff_brake_enabled;
    }

    showSuccess('参数已应用到回测配置中！包含移动止损和悬崖勒马设置');
}

// 格式化日期显示
function formatDate(dateString) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return dateString;
    }
}

// ==================== 交易相关函数 ====================

// 加载可用的交易模型
async function loadTradingModels() {
    try {
        const response = await fetch('/api/deep-learning/models');
        const data = await response.json();

        const tradingModelSelect = document.getElementById('tradingModelSelect');
        const tradingModelInfo = document.getElementById('tradingModelInfo');

        if (data.success && data.models) {
            // 清空现有选项
            tradingModelSelect.innerHTML = '<option value="">请选择交易模型...</option>';

            // 只显示训练完成的模型
            const completedModels = data.models.filter(model => model.status === 'completed');

            if (completedModels.length > 0) {
                completedModels.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = `${model.name} (${model.model_type.toUpperCase()}, ${model.symbol}, ${model.timeframe})`;
                    option.dataset.model = JSON.stringify(model);
                    tradingModelSelect.appendChild(option);
                });

                tradingModelInfo.innerHTML = `
                    <i class="fas fa-check-circle text-success"></i>
                    找到 ${completedModels.length} 个可用的交易模型
                `;
                tradingModelInfo.className = 'form-text text-success';
            } else {
                tradingModelInfo.innerHTML = `
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    没有找到训练完成的模型，请先训练模型
                `;
                tradingModelInfo.className = 'form-text text-warning';
            }
        } else {
            tradingModelInfo.innerHTML = `
                <i class="fas fa-times-circle text-danger"></i>
                加载模型失败: ${data.error || '未知错误'}
            `;
            tradingModelInfo.className = 'form-text text-danger';
        }

    } catch (error) {
        console.error('加载交易模型失败:', error);
        document.getElementById('tradingModelInfo').innerHTML = `
            <i class="fas fa-times-circle text-danger"></i>
            加载模型失败: ${error.message}
        `;
        document.getElementById('tradingModelInfo').className = 'form-text text-danger';
    }
}

// 处理交易模型选择变化
function onTradingModelChange() {
    const tradingModelSelect = document.getElementById('tradingModelSelect');
    const selectedOption = tradingModelSelect.options[tradingModelSelect.selectedIndex];

    if (selectedOption.value && selectedOption.dataset.model) {
        selectedTradingModel = JSON.parse(selectedOption.dataset.model);

        // 更新模型信息显示
        updateTradingModelInfo(selectedTradingModel);

        // 更新推理间隔适配
        updateInferenceIntervalForTradingModel();

        // 更新手动下单按钮状态
        updateManualTradeButtonsStatus();

        console.log('选择交易模型:', selectedTradingModel);
    } else {
        selectedTradingModel = null;
        resetTradingModelInfo();

        // 更新手动下单按钮状态
        updateManualTradeButtonsStatus();
    }
}

// 更新交易模型信息显示
function updateTradingModelInfo(model) {
    const tradingModelInfo = document.getElementById('tradingModelInfo');

    tradingModelInfo.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-robot text-primary me-2"></i>
            <div>
                <div class="fw-bold text-primary">${model.name}</div>
                <div class="small text-muted">
                    ${model.model_type.toUpperCase()} | ${model.symbol} | ${model.timeframe} |
                    创建于 ${formatDate(model.created_at)}
                </div>
            </div>
        </div>
    `;
    tradingModelInfo.className = 'form-text';
}

// 重置交易模型信息
function resetTradingModelInfo() {
    const tradingModelInfo = document.getElementById('tradingModelInfo');
    tradingModelInfo.innerHTML = `
        <i class="fas fa-info-circle text-info"></i>
        选择一个训练完成的模型用于AI交易决策
    `;
    tradingModelInfo.className = 'form-text text-muted';
}

// 为交易模型更新推理间隔适配
function updateInferenceIntervalForTradingModel() {
    if (!selectedTradingModel) return;

    const intervalSelect = document.getElementById('inferenceInterval');
    const intervalHelp = document.getElementById('inferenceIntervalHelp');

    if (intervalSelect.value === 'auto') {
        const timeframe = selectedTradingModel.timeframe;
        const recommendedInterval = getRecommendedInterval(timeframe);

        // 更新帮助文本显示推荐间隔
        intervalHelp.innerHTML = `
            <i class="fas fa-magic text-primary"></i>
            自动适配: ${timeframe} 模型推荐间隔 ${formatInterval(recommendedInterval)}
        `;
        intervalHelp.className = 'form-text text-primary';
    }
}

// 更新最新推理结果到交易区域
function updateLatestInferenceResult(result) {
    const latestResultElement = document.getElementById('latestInferenceResult');

    if (result) {
        const predictionClass = result.prediction === 'BUY' ? 'text-success' :
                              result.prediction === 'SELL' ? 'text-danger' : 'text-warning';

        const predictionIcon = result.prediction === 'BUY' ? '📈' :
                             result.prediction === 'SELL' ? '📉' : '⏸️';

        // 为HOLD预测提供特殊的显示逻辑
        const targetDisplay = result.prediction === 'HOLD' ?
            `当前: ${result.current_price} (建议观望)` :
            `目标: ${result.price_target}`;

        // 添加交易建议
        const tradingAdvice = result.prediction === 'HOLD' ?
            '<div class="small text-muted mt-1">💡 市场趋势不明显，建议暂时观望</div>' :
            result.analysis?.reason ?
            `<div class="small text-muted mt-1">💡 ${result.analysis.reason}</div>` : '';

        // 格式化北京时间显示
        const beijingTime = new Date(result.timestamp).toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });

        latestResultElement.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="fw-bold ${predictionClass}">
                        ${predictionIcon} ${result.prediction}
                    </div>
                    <div class="small text-muted">
                        <i class="fas fa-clock me-1"></i>${beijingTime}
                    </div>
                </div>
                <div class="text-end">
                    <div class="fw-bold">${targetDisplay}</div>
                    ${result.confidence ? `
                        <div class="small">
                            <span class="badge ${result.confidence > 0.8 ? 'bg-success' : result.confidence > 0.6 ? 'bg-warning' : 'bg-secondary'}">
                                ${(result.confidence * 100).toFixed(1)}%
                            </span>
                        </div>
                    ` : ''}
                </div>
            </div>
            ${tradingAdvice}
        `;

        // 触发交易条件分析（仅显示分析，不执行交易）
        performTradingConditionsAnalysis(result).catch(error => {
            console.error('交易条件分析失败:', error);
        });
    } else {
        latestResultElement.innerHTML = '<div class="text-muted">暂无推理结果</div>';
        // 隐藏交易条件分析
        const analysisSection = document.getElementById('tradingConditionsAnalysis');
        if (analysisSection) {
            analysisSection.style.display = 'none';
        }
    }
}

// 恢复自动交易状态
async function restoreAutoTradingState() {
    try {
        console.log('🔄 检查自动交易状态...');

        const response = await fetch('/api/deep-learning/auto-trading/status');
        const result = await response.json();

        console.log('📊 自动交易状态响应:', result);

        if (result.success && result.active) {
            console.log('✅ 发现活跃的自动交易会话，正在恢复...');

            // 恢复交易状态变量
            autoTradingActive = true;

            // 恢复交易模型选择
            if (result.model_info) {
                console.log('🧠 恢复交易模型:', result.model_info.name);

                // 等待交易模型下拉框加载完成
                const waitForModelSelect = () => {
                    const modelSelect = document.getElementById('tradingModelSelect');
                    if (modelSelect && modelSelect.options.length > 1) {
                        modelSelect.value = result.model_info.id;
                        selectedTradingModel = result.model_info;
                        onTradingModelChange(); // 触发模型选择事件
                        console.log('✅ 交易模型已恢复');
                    } else {
                        // 如果模型列表还没加载完，继续等待
                        setTimeout(waitForModelSelect, 500);
                    }
                };
                waitForModelSelect();
            }

            // 更新UI状态
            updateAutoTradingUI(true, result.model_info);

            // 重新启动交易循环
            if (!tradingInterval) {
                startTradingLoop();
                console.log('🔄 交易循环已重新启动');
            }

            showSuccess('自动交易状态已恢复');

            // 更新状态显示
            updateTradingStatistics();

        } else {
            console.log('ℹ️ 没有活跃的自动交易会话');
            // 确保UI状态正确
            updateAutoTradingUI(false);
        }

    } catch (error) {
        console.error('❌ 恢复自动交易状态失败:', error);
        // 确保UI状态正确
        updateAutoTradingUI(false);
    }
}

// 统一更新自动交易UI状态
function updateAutoTradingUI(isActive, modelInfo = null) {
    try {
        const startBtn = document.getElementById('startTradingBtn');
        const stopBtn = document.getElementById('stopTradingBtn');
        const enableAutoTrading = document.getElementById('enableAutoTrading');
        const statusIndicator = document.getElementById('tradingStatusIndicator');
        const statusText = document.getElementById('tradingStatusText');
        const statusDetails = document.getElementById('tradingStatusDetails');

        if (startBtn && stopBtn && enableAutoTrading) {
            if (isActive) {
                startBtn.style.display = 'none';
                stopBtn.style.display = 'block';
                enableAutoTrading.checked = true;

                // 更新状态指示器
                if (statusIndicator && statusText && statusDetails) {
                    statusIndicator.className = 'alert alert-success mb-3';
                    statusText.textContent = '交易状态：运行中';

                    if (modelInfo) {
                        statusDetails.textContent = `使用模型：${modelInfo.name} | 品种：${modelInfo.symbol}`;
                    } else {
                        statusDetails.textContent = 'AI自动交易正在运行，实时监控市场变化';
                    }
                }

                console.log('✅ UI状态已更新为：交易中');
            } else {
                startBtn.style.display = 'block';
                stopBtn.style.display = 'none';
                enableAutoTrading.checked = false;

                // 更新状态指示器
                if (statusIndicator && statusText && statusDetails) {
                    statusIndicator.className = 'alert alert-info mb-3';
                    statusText.textContent = '交易状态：未启动';
                    statusDetails.textContent = '点击"开始AI交易"启动自动交易';
                }

                console.log('✅ UI状态已更新为：未交易');
            }
        } else {
            console.warn('⚠️ 无法找到UI元素，跳过状态更新');
        }
    } catch (error) {
        console.error('❌ 更新UI状态失败:', error);
    }
}

// 更新交易统计信息
async function updateTradingStatistics() {
    try {
        const response = await fetch('/api/deep-learning/trading-statistics');
        const result = await response.json();

        if (result.success) {
            const stats = result.statistics;

            // 更新全局变量（用于交易条件检查）
            tradingStatistics = {
                todayTrades: stats.todayTrades || stats.today_trades || 0,
                currentPositions: stats.currentPositions || stats.current_positions || 0,
                totalProfit: stats.totalProfit || stats.total_profit || 0
            };

            // 更新统计显示
            const todayTradesElement = document.getElementById('todayTrades');
            const currentPositionsElement = document.getElementById('currentPositions');

            if (todayTradesElement) {
                todayTradesElement.textContent = tradingStatistics.todayTrades;
            }

            if (currentPositionsElement) {
                currentPositionsElement.textContent = tradingStatistics.currentPositions;
            }

            console.log('📊 交易统计数据:', tradingStatistics);

            // 刷新持仓显示
            console.log('🔄 刷新持仓显示...');
            refreshPositions();

            console.log('✅ 交易统计已更新');
        } else {
            console.error('❌ 获取交易统计失败:', result.error);
        }
    } catch (error) {
        console.error('❌ 更新交易统计失败:', error);
    }
}

// 自动交易状态监控
let autoTradingStatusInterval = null;

// 启动自动交易状态监控
function startAutoTradingStatusMonitoring() {
    // 清除现有的监控
    if (autoTradingStatusInterval) {
        clearInterval(autoTradingStatusInterval);
    }

    console.log('🔄 启动自动交易状态监控...');

    // 每30秒检查一次状态
    autoTradingStatusInterval = setInterval(async () => {
        try {
            const response = await fetch('/api/deep-learning/auto-trading/status');
            const result = await response.json();

            if (result.success) {
                // 检查状态是否与当前UI一致
                const currentUIActive = document.getElementById('stopTradingBtn').style.display !== 'none';

                if (result.active !== currentUIActive) {
                    console.log(`🔄 检测到状态不一致，正在同步... 后端:${result.active}, 前端:${currentUIActive}`);

                    // 同步状态
                    autoTradingActive = result.active;
                    updateAutoTradingUI(result.active, result.model_info);

                    if (result.active && !tradingInterval) {
                        // 如果后端显示活跃但前端没有交易循环，重新启动
                        startTradingLoop();
                        console.log('🔄 重新启动交易循环');
                    } else if (!result.active && tradingInterval) {
                        // 如果后端显示停止但前端还在运行，停止交易循环
                        clearInterval(tradingInterval);
                        tradingInterval = null;
                        console.log('🛑 停止交易循环');
                    }
                }

                // 更新交易统计
                updateTradingStatistics();
            }
        } catch (error) {
            console.error('❌ 状态监控检查失败:', error);
        }
    }, 30000); // 30秒检查一次
}

// 停止自动交易状态监控
function stopAutoTradingStatusMonitoring() {
    if (autoTradingStatusInterval) {
        clearInterval(autoTradingStatusInterval);
        autoTradingStatusInterval = null;
        console.log('🛑 自动交易状态监控已停止');
    }
}

// 页面可见性变化时检查状态
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        console.log('🔄 页面重新可见，检查交易状态...');

        // 延迟一下再检查，确保页面完全激活
        setTimeout(async () => {
            try {
                const response = await fetch('/api/deep-learning/auto-trading/status');
                const result = await response.json();

                if (result.success) {
                    const currentUIActive = document.getElementById('stopTradingBtn').style.display !== 'none';

                    if (result.active !== currentUIActive) {
                        console.log('🔄 页面激活时发现状态不一致，正在同步...');
                        autoTradingActive = result.active;
                        updateAutoTradingUI(result.active, result.model_info);

                        if (result.active && !tradingInterval) {
                            startTradingLoop();
                        } else if (!result.active && tradingInterval) {
                            clearInterval(tradingInterval);
                            tradingInterval = null;
                        }
                    }

                    // 更新统计信息
                    updateTradingStatistics();
                }
            } catch (error) {
                console.error('❌ 页面激活状态检查失败:', error);
            }
        }, 1000);
    }
});

// 添加网络重连检测
window.addEventListener('online', function() {
    console.log('🌐 网络重新连接，检查交易状态...');
    setTimeout(() => {
        restoreAutoTradingState();
    }, 2000);
});

// 添加焦点恢复检测
window.addEventListener('focus', function() {
    console.log('🔍 窗口重新获得焦点，检查交易状态...');
    setTimeout(async () => {
        try {
            const response = await fetch('/api/deep-learning/auto-trading/status');
            const result = await response.json();

            if (result.success) {
                const currentUIActive = document.getElementById('stopTradingBtn').style.display !== 'none';

                if (result.active !== currentUIActive) {
                    console.log('🔄 窗口焦点恢复时发现状态不一致，正在同步...');
                    autoTradingActive = result.active;
                    updateAutoTradingUI(result.active, result.model_info);
                }
            }
        } catch (error) {
            console.error('❌ 窗口焦点恢复状态检查失败:', error);
        }
    }, 500);
});

// 心跳检测机制
let heartbeatInterval = null;
let lastHeartbeatTime = Date.now();

function startHeartbeat() {
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
    }

    heartbeatInterval = setInterval(async () => {
        try {
            const startTime = Date.now();
            const response = await fetch('/api/deep-learning/auto-trading/status');
            const endTime = Date.now();

            if (response.ok) {
                lastHeartbeatTime = Date.now();
                const responseTime = endTime - startTime;

                // 如果响应时间过长，可能网络有问题
                if (responseTime > 5000) {
                    console.warn(`⚠️ 心跳响应时间过长: ${responseTime}ms`);
                }
            } else {
                console.error(`❌ 心跳检测失败: HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('❌ 心跳检测异常:', error);

            // 如果心跳失败超过2分钟，尝试恢复状态
            const timeSinceLastHeartbeat = Date.now() - lastHeartbeatTime;
            if (timeSinceLastHeartbeat > 120000) { // 2分钟
                console.log('🔄 心跳失败超过2分钟，尝试恢复状态...');
                setTimeout(() => {
                    restoreAutoTradingState();
                }, 5000);
                lastHeartbeatTime = Date.now(); // 重置时间，避免频繁恢复
            }
        }
    }, 60000); // 每分钟检查一次
}

function stopHeartbeat() {
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
    }
}

// 检查MT5连接状态
async function checkMT5Connection() {
    try {
        const response = await fetch('/api/mt5/connection-status');
        const data = await response.json();

        const statusElement = document.getElementById('mt5ConnectionStatus');

        if (data.success && data.connected) {
            mt5Connected = true;
            statusElement.textContent = 'MT5已连接';
            statusElement.className = 'badge bg-success me-2';

            // 开始获取实时市场数据
            startMarketDataUpdates();

            // 更新手动下单按钮状态
            updateManualTradeButtonsStatus();
        } else {
            mt5Connected = false;
            statusElement.textContent = 'MT5未连接';
            statusElement.className = 'badge bg-danger me-2';

            // 停止市场数据更新
            stopMarketDataUpdates();

            // 更新手动下单按钮状态
            updateManualTradeButtonsStatus();
        }

    } catch (error) {
        console.error('检查MT5连接失败:', error);
        mt5Connected = false;
        document.getElementById('mt5ConnectionStatus').textContent = 'MT5连接错误';
        document.getElementById('mt5ConnectionStatus').className = 'badge bg-danger me-2';
    }
}

// 自动连接MT5
async function autoConnectMT5() {
    const statusElement = document.getElementById('mt5ConnectionStatus');

    try {
        // 显示连接中状态
        statusElement.textContent = 'MT5连接中...';
        statusElement.className = 'badge bg-warning me-2';

        console.log('🔌 开始自动连接MT5...');

        // 调用自动连接API
        const response = await fetch('/api/mt5/auto-connect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            console.log('✅ MT5自动连接成功');
            showSuccess('MT5自动连接成功！');

            // 等待一下再检查连接状态
            setTimeout(() => {
                checkMT5Connection();
            }, 1000);

        } else {
            console.error('❌ MT5自动连接失败:', data.error);
            showError(`MT5自动连接失败: ${data.error}`);

            statusElement.textContent = 'MT5连接失败';
            statusElement.className = 'badge bg-danger me-2';
        }

    } catch (error) {
        console.error('❌ 自动连接MT5异常:', error);
        showError(`自动连接MT5异常: ${error.message}`);

        statusElement.textContent = 'MT5连接异常';
        statusElement.className = 'badge bg-danger me-2';
    }
}

// 开始市场数据更新
function startMarketDataUpdates() {
    if (!mt5Connected) return;

    // 立即获取一次数据
    updateMarketData();

    // 每5秒更新一次市场数据
    if (window.marketDataInterval) {
        clearInterval(window.marketDataInterval);
    }

    window.marketDataInterval = setInterval(updateMarketData, 5000);
}

// 停止市场数据更新
function stopMarketDataUpdates() {
    if (window.marketDataInterval) {
        clearInterval(window.marketDataInterval);
        window.marketDataInterval = null;
    }
}

// 更新市场数据
async function updateMarketData() {
    if (!mt5Connected) return;

    try {
        // 优先使用交易模型的symbol，如果没有则使用推理模型的symbol，最后使用表单中的symbol
        let symbol = 'XAUUSD'; // 默认值

        if (selectedTradingModel && selectedTradingModel.symbol) {
            symbol = selectedTradingModel.symbol;
            console.log('🔄 使用交易模型symbol:', symbol);
        } else if (selectedModel && selectedModel.symbol) {
            symbol = selectedModel.symbol;
            console.log('🔄 使用推理模型symbol:', symbol);
        } else {
            const symbolInput = document.getElementById('symbol');
            if (symbolInput && symbolInput.value) {
                symbol = symbolInput.value;
                console.log('🔄 使用表单symbol:', symbol);
            } else {
                console.log('🔄 使用默认symbol:', symbol);
            }
        }

        const response = await fetch(`/api/mt5/market-data/${symbol}`);
        const data = await response.json();

        if (data.success && data.market_data) {
            currentMarketData = data.market_data;

            // 更新显示
            const bidElement = document.getElementById('currentBid');
            const askElement = document.getElementById('currentAsk');
            const spreadElement = document.getElementById('currentSpread');
            const timeElement = document.getElementById('marketDataTime');

            if (bidElement) bidElement.textContent = currentMarketData.bid.toFixed(5);
            if (askElement) askElement.textContent = currentMarketData.ask.toFixed(5);
            if (spreadElement) {
                const spread = ((currentMarketData.ask - currentMarketData.bid) * 100000).toFixed(1);
                spreadElement.textContent = spread + ' 点';
            }

            // 更新时间戳
            if (timeElement) {
                const timestamp = new Date().toLocaleString('zh-CN');
                timeElement.textContent = timestamp;
            }

            console.log('✅ 市场数据更新成功:', {
                symbol: symbol,
                bid: currentMarketData.bid,
                ask: currentMarketData.ask,
                spread: ((currentMarketData.ask - currentMarketData.bid) * 100000).toFixed(1)
            });

            // 更新手动下单按钮状态
            updateManualTradeButtonsStatus();

        } else {
            console.error('❌ 获取市场数据失败:', data.error);

            // 显示错误状态
            const bidElement = document.getElementById('currentBid');
            const askElement = document.getElementById('currentAsk');
            const spreadElement = document.getElementById('currentSpread');

            if (bidElement) bidElement.textContent = '--';
            if (askElement) askElement.textContent = '--';
            if (spreadElement) spreadElement.textContent = '--';

            // 更新手动下单按钮状态
            updateManualTradeButtonsStatus();
        }

    } catch (error) {
        console.error('❌ 更新市场数据异常:', error);

        // 显示错误状态
        const bidElement = document.getElementById('currentBid');
        const askElement = document.getElementById('currentAsk');
        const spreadElement = document.getElementById('currentSpread');

        if (bidElement) bidElement.textContent = '--';
        if (askElement) askElement.textContent = '--';
        if (spreadElement) spreadElement.textContent = '--';
    }
}

// 开始自动交易
async function startAutoTrading() {
    if (!mt5Connected) {
        showError('请先连接MT5');
        return;
    }

    if (!selectedTradingModel) {
        showError('请先选择一个交易模型');
        return;
    }

    const enableAutoTrading = document.getElementById('enableAutoTrading').checked;
    if (!enableAutoTrading) {
        showError('请先启用自动交易选项');
        return;
    }

    // 验证交易参数
    const tradingConfig = getTradingConfig();
    if (!validateTradingConfig(tradingConfig)) {
        return;
    }

    try {
        // 启动自动交易
        const response = await fetch('/api/deep-learning/auto-trading/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model_id: selectedTradingModel.id,
                trading_config: tradingConfig
            })
        });

        const result = await response.json();

        if (result.success) {
            autoTradingActive = true;

            // 更新UI状态
            updateAutoTradingUI(true);

            // 开始定期推理和交易检查
            startTradingLoop();

            showSuccess('AI自动交易已启动');
            console.log('✅ AI自动交易启动成功');
        } else {
            showError('启动自动交易失败: ' + result.error);
            console.error('❌ 启动自动交易失败:', result.error);
        }

    } catch (error) {
        console.error('启动自动交易失败:', error);
        showError('启动自动交易失败: ' + error.message);
    }
}

// 停止自动交易
async function stopAutoTrading() {
    try {
        const response = await fetch('/api/deep-learning/auto-trading/stop', {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            autoTradingActive = false;

            // 停止交易循环
            if (tradingInterval) {
                clearInterval(tradingInterval);
                tradingInterval = null;
            }

            // 更新UI状态
            updateAutoTradingUI(false);

            showSuccess('AI自动交易已停止');
            console.log('✅ AI自动交易停止成功');
        } else {
            showError('停止自动交易失败: ' + result.error);
            console.error('❌ 停止自动交易失败:', result.error);
        }

    } catch (error) {
        console.error('停止自动交易失败:', error);
        showError('停止自动交易失败: ' + error.message);
    }
}

// 更新推理间隔以适配模型时间框架
function updateInferenceIntervalForModel() {
    if (!selectedModel) return;

    const intervalSelect = document.getElementById('inferenceInterval');
    const intervalHelp = document.getElementById('inferenceIntervalHelp');

    if (intervalSelect.value === 'auto') {
        const timeframe = selectedModel.timeframe;
        const recommendedInterval = getRecommendedInterval(timeframe);

        // 更新帮助文本显示推荐间隔
        intervalHelp.innerHTML = `
            <i class="fas fa-magic text-primary"></i>
            自动适配: ${timeframe} 模型推荐间隔 ${formatInterval(recommendedInterval)}
        `;
        intervalHelp.className = 'form-text text-primary';
    } else {
        intervalHelp.innerHTML = '自动适配将根据模型时间框架智能设置间隔';
        intervalHelp.className = 'form-text text-muted';
    }
}

// 根据时间框架获取推荐的推理间隔
function getRecommendedInterval(timeframe) {
    const intervalMap = {
        '1m': 30,      // 1分钟K线 → 30秒推理
        '5m': 150,     // 5分钟K线 → 2.5分钟推理 (K线一半时间)
        '15m': 450,    // 15分钟K线 → 7.5分钟推理
        '30m': 900,    // 30分钟K线 → 15分钟推理
        '1h': 1800,    // 1小时K线 → 30分钟推理
        '4h': 7200,    // 4小时K线 → 2小时推理
        '1d': 21600    // 1天K线 → 6小时推理
    };

    return intervalMap[timeframe] || 300; // 默认5分钟
}

// 格式化间隔时间显示
function formatInterval(seconds) {
    if (seconds < 60) {
        return `${seconds}秒`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const remainingMinutes = Math.floor((seconds % 3600) / 60);
        return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`;
    }
}

// 获取实际使用的推理间隔
function getActualInferenceInterval() {
    const intervalSelect = document.getElementById('inferenceInterval');

    if (intervalSelect.value === 'auto' && selectedModel) {
        return getRecommendedInterval(selectedModel.timeframe);
    } else {
        return parseInt(intervalSelect.value) || 300;
    }
}

// 获取交易配置
function getTradingConfig() {
    // 获取并验证lot_size
    const lotSizeElement = document.getElementById('tradingLotSize');
    let lotSize = parseFloat(lotSizeElement?.value || '0.01');

    // 确保lot_size有效
    if (isNaN(lotSize) || lotSize <= 0) {
        lotSize = 0.01; // 默认值
        console.warn('⚠️ lot_size无效，使用默认值: 0.01');
        if (lotSizeElement) {
            lotSizeElement.value = '0.01';
        }
    }

    // 限制lot_size范围
    if (lotSize > 10) {
        lotSize = 10;
        console.warn('⚠️ lot_size超出最大值，调整为: 10');
        if (lotSizeElement) {
            lotSizeElement.value = '10';
        }
    }

    const config = {
        lot_size: lotSize,
        max_positions: parseInt(document.getElementById('maxPositions')?.value || '4'),
        stop_loss_pips: parseInt(document.getElementById('stopLossPips')?.value || '1000'),
        take_profit_pips: parseInt(document.getElementById('takeProfitPips')?.value || '2000'),
        min_confidence: parseFloat(document.getElementById('minConfidence')?.value || '0.8'),
        inference_interval: getActualInferenceInterval(), // 使用智能间隔
        trading_start_time: document.getElementById('tradingStartTime')?.value || '09:00',
        trading_end_time: document.getElementById('tradingEndTime')?.value || '17:00',
        enable_auto_trading: document.getElementById('enableAutoTrading')?.checked || false,
        enable_dynamic_sl: document.getElementById('enableDynamicSL')?.checked || false,
        enable_trailing_stop: document.getElementById('enableInferenceTrailingStop')?.checked || false,
        trailing_stop_distance: parseInt(document.getElementById('inferenceTrailingStopDistance')?.value || '50'),
        trailing_stop_step: parseInt(document.getElementById('inferenceTrailingStopStep')?.value || '10'),
        enable_news_filter: document.getElementById('enableNewsFilter')?.checked || false,
        enable_cliff_brake: document.getElementById('enableCliffBrake')?.checked || false
    };

    // 验证配置
    console.log('📊 交易配置:', config);
    return config;
}

// 验证交易参数
function validateTradeParameters(tradeData) {
    const errors = [];

    // 验证symbol
    if (!tradeData.symbol) {
        errors.push('交易品种不能为空');
    }

    // 验证action
    if (!tradeData.action || !['BUY', 'SELL'].includes(tradeData.action)) {
        errors.push('交易方向必须是BUY或SELL');
    }

    // 验证lot_size
    if (!tradeData.lot_size || isNaN(tradeData.lot_size) || tradeData.lot_size <= 0) {
        errors.push('交易手数必须大于0');
    }

    if (tradeData.lot_size > 10) {
        errors.push('交易手数不能超过10');
    }

    // 验证止损止盈
    if (tradeData.stop_loss_pips && (isNaN(tradeData.stop_loss_pips) || tradeData.stop_loss_pips < 0)) {
        errors.push('止损点数必须大于等于0');
    }

    if (tradeData.take_profit_pips && (isNaN(tradeData.take_profit_pips) || tradeData.take_profit_pips < 0)) {
        errors.push('止盈点数必须大于等于0');
    }

    return {
        valid: errors.length === 0,
        errors: errors
    };
}

// 修复交易参数
function fixTradeParameters(tradeData) {
    const fixed = { ...tradeData };

    // 修复lot_size
    if (!fixed.lot_size || isNaN(fixed.lot_size) || fixed.lot_size <= 0) {
        fixed.lot_size = 0.01;
        console.warn('⚠️ 修复lot_size为默认值: 0.01');
    }

    if (fixed.lot_size > 10) {
        fixed.lot_size = 10;
        console.warn('⚠️ 修复lot_size为最大值: 10');
    }

    // 修复其他参数
    fixed.stop_loss_pips = fixed.stop_loss_pips || 50;
    fixed.take_profit_pips = fixed.take_profit_pips || 100;

    return fixed;
}

// 验证交易配置
function validateTradingConfig(config) {
    if (config.lot_size <= 0 || config.lot_size > 10) {
        showError('交易手数必须在0.01-10之间');
        return false;
    }

    if (config.max_positions <= 0 || config.max_positions > 10) {
        showError('最大持仓数必须在1-10之间');
        return false;
    }

    if (config.min_confidence < 0.3 || config.min_confidence > 0.99) {
        showError('最低置信度必须在0.3-0.99之间（推荐0.3-0.6）');
        return false;
    }

    return true;
}

// 开始交易循环
function startTradingLoop() {
    const intervalSeconds = getActualInferenceInterval();

    console.log(`🔄 启动交易循环，推理间隔: ${formatInterval(intervalSeconds)}`);

    tradingInterval = setInterval(async () => {
        if (!autoTradingActive) return;

        // 检查交易时间
        if (!isWithinTradingHours()) {
            console.log('当前不在交易时间内');
            return;
        }

        // 执行推理
        await performAutoInference();

        // 更新交易统计
        updateTradingStatistics();

    }, intervalSeconds * 1000);
}

// 检查是否在交易时间内
function isWithinTradingHours() {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    const startTime = document.getElementById('tradingStartTime').value.split(':');
    const endTime = document.getElementById('tradingEndTime').value.split(':');

    const startMinutes = parseInt(startTime[0]) * 60 + parseInt(startTime[1]);
    const endMinutes = parseInt(endTime[0]) * 60 + parseInt(endTime[1]);

    return currentTime >= startMinutes && currentTime <= endMinutes;
}

// 执行自动推理
async function performAutoInference() {
    if (!selectedTradingModel) return;

    try {
        // 获取推理配置
        const inferenceConfig = getInferenceConfig();
        console.log('自动交易推理配置:', inferenceConfig);

        // 验证推理配置
        if (!validateInferenceConfig(inferenceConfig)) {
            console.error('推理配置验证失败');
            return;
        }

        const formData = {
            model_id: selectedTradingModel.id,
            symbol: selectedTradingModel.symbol,
            timeframe: selectedTradingModel.timeframe,
            inference_mode: 'realtime',
            data_points: 100,
            use_gpu: true,
            show_confidence: true,
            // 添加交易配置
            trade_config: inferenceConfig
        };

        const response = await fetch('/api/deep-learning/inference', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success && result.results && result.results.length > 0) {
            const latestResult = result.results[result.results.length - 1];

            // 更新最新推理结果显示
            updateLatestInferenceResult(latestResult);

            // 检查是否满足交易条件，传递交易配置
            await checkTradingConditions(latestResult, result.trade_config);
        }

    } catch (error) {
        console.error('自动推理失败:', error);
    }
}

// 检查交易条件并执行交易
async function checkTradingConditions(inferenceResult, tradeConfig) {
    // 🔧 修复：在检查交易条件前，先刷新持仓数据确保数据准确
    await refreshPositionsForTradingCheck();

    // 获取交易配置参数
    const minConfidence = tradeConfig ? tradeConfig.min_confidence : parseFloat(document.getElementById('minConfidence').value);
    const maxPositions = parseInt(document.getElementById('maxPositions').value);
    const autoTradingEnabled = document.getElementById('enableAutoTrading').checked;

    // 🔧 调试：记录持仓数检查详情
    console.log('📊 交易条件检查 - 持仓数详情:');
    console.log(`  当前持仓数: ${tradingStatistics.currentPositions}`);
    console.log(`  最大持仓数: ${maxPositions}`);
    console.log(`  页面显示持仓数: ${document.getElementById('currentPositions')?.textContent}`);
    console.log(`  实时持仓数: ${document.getElementById('realTimePositionCount')?.textContent}`);

    // 初始化条件检查结果
    const conditionsCheck = {
        autoTrading: {
            label: '自动交易开关',
            passed: autoTradingEnabled,
            value: autoTradingEnabled ? '已启用' : '已禁用',
            required: true
        },
        confidence: {
            label: '置信度检查',
            passed: inferenceResult.confidence && inferenceResult.confidence >= minConfidence,
            value: `${(inferenceResult.confidence * 100).toFixed(1)}% / ${(minConfidence * 100).toFixed(1)}%`,
            required: true
        },
        positions: {
            label: '持仓数限制',
            passed: tradingStatistics.currentPositions < maxPositions,
            value: `${tradingStatistics.currentPositions} / ${maxPositions}`,
            required: true
        },
        signal: {
            label: '交易信号',
            passed: inferenceResult.prediction !== 'HOLD',
            value: inferenceResult.prediction || 'UNKNOWN',
            required: true
        },
        mt5Connection: {
            label: 'MT5连接',
            passed: mt5Connected,
            value: mt5Connected ? '已连接' : '未连接',
            required: true
        }
    };

    // 检查交易时间（如果有配置）
    const tradingHours = isWithinTradingHours();
    conditionsCheck.tradingTime = {
        label: '交易时间',
        passed: tradingHours,
        value: tradingHours ? '交易时间内' : '非交易时间',
        required: false
    };

    // 计算总体结果
    const requiredConditions = Object.values(conditionsCheck).filter(c => c.required);
    const passedRequired = requiredConditions.filter(c => c.passed).length;
    const allConditionsPassed = requiredConditions.every(c => c.passed);

    // 显示条件检查结果
    displayTradingConditionsAnalysis(conditionsCheck, allConditionsPassed, inferenceResult);

    // 如果所有条件都满足，执行交易
    if (allConditionsPassed) {
        console.log('✅ 所有交易条件满足，执行交易');
        await executeTrade(inferenceResult, tradeConfig);
    } else {
        const failedConditions = requiredConditions.filter(c => !c.passed);
        console.log(`❌ 交易条件不满足，失败条件: ${failedConditions.map(c => c.label).join(', ')}`);
    }
}

// 执行交易
async function executeTrade(inferenceResult, tradeConfig) {
    // 使用传入的交易配置，如果没有则获取当前配置
    const tradingConfig = tradeConfig || getTradingConfig();

    try {
        let tradeData = {
            symbol: selectedTradingModel.symbol,
            action: inferenceResult.prediction, // BUY or SELL
            lot_size: tradingConfig.lot_size,
            stop_loss_pips: tradingConfig.stop_loss_pips,
            take_profit_pips: tradingConfig.take_profit_pips,
            inference_result: inferenceResult,
            trading_config: tradingConfig
        };

        // 验证交易参数
        const validation = validateTradeParameters(tradeData);
        if (!validation.valid) {
            console.error('❌ 交易参数验证失败:', validation.errors);
            showError('交易参数验证失败: ' + validation.errors.join(', '));
            return;
        }

        // 修复参数（如果需要）
        tradeData = fixTradeParameters(tradeData);
        console.log('📊 最终交易数据:', tradeData);

        const response = await fetch('/api/deep-learning/execute-trade', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(tradeData)
        });

        const result = await response.json();

        if (result.success) {
            console.log('交易执行成功:', result);
            tradingStatistics.todayTrades++;
            tradingStatistics.currentPositions++;

            // 更新统计显示
            updateTradingStatistics();

            // 显示交易通知（包含悬崖勒马信息）
            let successMessage = result.message || `AI交易执行成功: ${result.final_action || inferenceResult.prediction} ${tradingConfig.lot_size}手`;

            if (result.cliff_brake_triggered) {
                successMessage += ` 🚨`;
                showSuccess(successMessage, '悬崖勒马已触发，交易方向已反转');
                console.log(`🚨 悬崖勒马触发: ${result.original_action} -> ${result.final_action}`);
            } else {
                showSuccess(successMessage);
            }
        } else {
            // 检查是否是悬崖勒马跳过交易
            if (result.cliff_brake_skip) {
                showWarning(`悬崖勒马跳过交易: ${result.error}`);
                console.log('⏭️ 悬崖勒马跳过交易:', result.error);
            } else {
                console.error('交易执行失败:', result.error);
                showError(`交易执行失败: ${result.error}`);
            }
        }

    } catch (error) {
        console.error('执行交易失败:', error);
    }
}

// 删除重复的函数，功能已合并到上面的updateTradingStatistics函数中

// 平仓所有持仓
async function closeAllPositions() {
    if (!mt5Connected) {
        showError('MT5未连接');
        return;
    }

    if (!lastPositionsData || lastPositionsData.length === 0) {
        showError('没有持仓需要平仓');
        return;
    }

    if (!confirm(`确定要平仓所有 ${lastPositionsData.length} 个持仓吗？`)) {
        return;
    }

    try {
        // 显示加载状态
        showPositionsLoadingState();

        const response = await fetch('/api/mt5/close-all-positions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            showSuccess(`成功平仓 ${result.closed_count || 'all'} 个持仓`);
            // 刷新持仓显示
            refreshPositions();
            // 更新交易统计
            updateTradingStatistics();
        } else {
            showError('平仓失败: ' + result.error);
            // 恢复持仓显示
            displayCurrentPositions(lastPositionsData);
        }

    } catch (error) {
        console.error('平仓失败:', error);
        showError('平仓失败: ' + error.message);
        // 恢复持仓显示
        displayCurrentPositions(lastPositionsData);
    }
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    console.log('🔄 页面卸载，清理资源...');

    // 停止状态监控
    stopAutoTradingStatusMonitoring();

    // 停止心跳检测
    stopHeartbeat();

    // 清理其他定时器
    if (window.marketDataInterval) {
        clearInterval(window.marketDataInterval);
    }

    if (tradingInterval) {
        clearInterval(tradingInterval);
    }

    if (mt5StatusCheckInterval) {
        clearInterval(mt5StatusCheckInterval);
    }
});

// ==================== MT5连接状态管理 ====================

let mt5StatusCheckInterval = null;

// 检查MT5连接状态
async function checkMT5Status() {
    const statusCard = document.getElementById('mt5StatusCard');
    const statusIcon = document.getElementById('mt5StatusIcon');
    const statusText = document.getElementById('mt5StatusText');
    const statusDetails = document.getElementById('mt5StatusDetails');
    const detailedStatus = document.getElementById('mt5DetailedStatus');
    const reconnectBtn = document.getElementById('reconnectMT5Btn');
    const refreshBtn = document.getElementById('refreshMT5StatusBtn');

    // 显示检查中状态
    statusIcon.className = 'fas fa-spinner fa-spin fa-2x text-warning';
    statusText.textContent = '检查MT5连接状态...';
    statusDetails.textContent = '正在获取连接信息';
    refreshBtn.disabled = true;

    try {
        const response = await fetch('/api/mt5/inference-connection-status');
        const result = await response.json();

        if (result.success && result.connected) {
            // 连接正常
            statusCard.className = 'card border-left-success';
            statusIcon.className = 'fas fa-plug fa-2x text-success';
            statusText.textContent = 'MT5连接正常';

            let details = '连接状态良好';
            if (result.auto_reconnected) {
                details += ' (已自动重连)';
            }
            statusDetails.textContent = details;

            reconnectBtn.style.display = 'none';

            // 显示详细状态
            detailedStatus.style.display = 'block';
            document.getElementById('mt5SymbolsCount').innerHTML =
                `<span class="text-success">${result.symbols_count || 0} 个</span>`;
            document.getElementById('mt5PriceStatus').innerHTML =
                result.price_data_available ? '<span class="text-success">正常</span>' : '<span class="text-danger">异常</span>';
            document.getElementById('mt5AutoReconnectStatus').innerHTML =
                result.reconnect_service_active ? '<span class="text-success">启用</span>' : '<span class="text-muted">禁用</span>';
            document.getElementById('mt5InferenceReady').innerHTML =
                result.ready_for_inference ? '<span class="text-success">就绪</span>' : '<span class="text-warning">未就绪</span>';

        } else {
            // 连接异常
            statusCard.className = 'card border-left-danger';
            statusIcon.className = 'fas fa-exclamation-triangle fa-2x text-danger';
            statusText.textContent = 'MT5连接异常';
            statusDetails.textContent = result.error || '连接失败，请检查MT5是否运行';

            reconnectBtn.style.display = 'inline-block';

            // 隐藏详细状态或显示错误状态
            detailedStatus.style.display = 'block';
            document.getElementById('mt5SymbolsCount').innerHTML = '<span class="text-danger">-</span>';
            document.getElementById('mt5PriceStatus').innerHTML = '<span class="text-danger">-</span>';
            document.getElementById('mt5AutoReconnectStatus').innerHTML = '<span class="text-muted">-</span>';
            document.getElementById('mt5InferenceReady').innerHTML = '<span class="text-danger">未就绪</span>';
        }

    } catch (error) {
        // 请求异常
        statusCard.className = 'card border-left-warning';
        statusIcon.className = 'fas fa-question-circle fa-2x text-warning';
        statusText.textContent = 'MT5状态检查失败';
        statusDetails.textContent = `网络错误: ${error.message}`;

        reconnectBtn.style.display = 'inline-block';
        detailedStatus.style.display = 'none';
    }

    refreshBtn.disabled = false;
}

// 强制MT5重连
async function forceMT5Reconnect() {
    const reconnectBtn = document.getElementById('reconnectMT5Btn');
    const originalText = reconnectBtn.innerHTML;

    reconnectBtn.disabled = true;
    reconnectBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>重连中...';

    try {
        const response = await fetch('/api/mt5/force-reconnect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('MT5重连成功');
            // 延迟检查状态，给连接一些时间
            setTimeout(() => {
                checkMT5Status();
            }, 1000);
        } else {
            showError(`MT5重连失败: ${result.error}`);
        }

    } catch (error) {
        showError(`重连请求失败: ${error.message}`);
    }

    reconnectBtn.disabled = false;
    reconnectBtn.innerHTML = originalText;
}

// 启动MT5状态监控
function startMT5StatusMonitoring() {
    // 立即检查一次
    checkMT5Status();

    // 每30秒检查一次
    if (mt5StatusCheckInterval) {
        clearInterval(mt5StatusCheckInterval);
    }

    mt5StatusCheckInterval = setInterval(() => {
        checkMT5Status();
    }, 30000); // 30秒
}

// 停止MT5状态监控
function stopMT5StatusMonitoring() {
    if (mt5StatusCheckInterval) {
        clearInterval(mt5StatusCheckInterval);
        mt5StatusCheckInterval = null;
    }
}

// 页面加载时启动监控
document.addEventListener('DOMContentLoaded', function() {
    startMT5StatusMonitoring();
});

// 页面卸载时停止监控
window.addEventListener('beforeunload', function() {
    stopMT5StatusMonitoring();
    stopPositionsAutoRefresh();
});

// 当前持仓相关函数 - 重新设计
let positionsRefreshInterval = null;
let lastPositionsData = null;

// 🔧 新增：专门用于交易条件检查的持仓刷新函数
async function refreshPositionsForTradingCheck() {
    try {
        console.log('🔍 为交易条件检查刷新持仓数据...');
        const response = await fetch('/get_positions');
        const result = await response.json();

        if (result.success && result.positions) {
            const currentPositionCount = result.positions.length;

            // 更新tradingStatistics中的持仓数
            if (typeof tradingStatistics !== 'undefined') {
                tradingStatistics.currentPositions = currentPositionCount;
            }

            // 更新页面显示
            updatePositionCount(currentPositionCount);

            console.log(`✅ 交易条件检查：当前持仓数 = ${currentPositionCount}`);
            return currentPositionCount;
        } else {
            console.warn('⚠️ 获取持仓数据失败，使用缓存数据');
            return tradingStatistics?.currentPositions || 0;
        }
    } catch (error) {
        console.error('❌ 刷新持仓数据异常:', error);
        return tradingStatistics?.currentPositions || 0;
    }
}

// 刷新持仓数据
async function refreshPositions() {
    console.log('🔄 刷新持仓数据...');

    // 显示加载状态
    showPositionsLoadingState();

    try {
        // 使用MT5持仓API获取实时数据
        const response = await fetch('/api/mt5/positions');
        const result = await response.json();

        if (result.success) {
            const positions = result.positions || [];
            console.log(`📊 获取到 ${positions.length} 个持仓`);

            // 更新持仓显示
            displayCurrentPositions(positions);
            lastPositionsData = positions;

            // 更新持仓计数
            updatePositionCount(positions.length);

        } else {
            console.error('❌ 获取持仓失败:', result.error);
            showPositionsErrorState(result.error || '获取持仓数据失败');
        }
    } catch (error) {
        console.error('❌ 刷新持仓异常:', error);
        showPositionsErrorState('网络连接异常，请检查连接后重试');
    }
}

// 显示状态管理函数
function showPositionsLoadingState() {
    document.getElementById('positionsLoadingState').style.display = 'block';
    document.getElementById('positionsErrorState').style.display = 'none';
    document.getElementById('noPositionsState').style.display = 'none';
    document.getElementById('positionsListContainer').style.display = 'none';

    // 禁用刷新按钮
    const refreshBtn = document.getElementById('refreshPositionsBtn');
    if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
    }
}

function showPositionsErrorState(errorMessage) {
    document.getElementById('positionsLoadingState').style.display = 'none';
    document.getElementById('positionsErrorState').style.display = 'block';
    document.getElementById('noPositionsState').style.display = 'none';
    document.getElementById('positionsListContainer').style.display = 'none';

    // 显示错误信息
    const errorMsgElement = document.getElementById('positionsErrorMessage');
    if (errorMsgElement) {
        errorMsgElement.textContent = errorMessage;
    }

    // 恢复刷新按钮
    enableRefreshButton();
}

function showNoPositionsState() {
    document.getElementById('positionsLoadingState').style.display = 'none';
    document.getElementById('positionsErrorState').style.display = 'none';
    document.getElementById('noPositionsState').style.display = 'block';
    document.getElementById('positionsListContainer').style.display = 'none';

    // 隐藏全部平仓按钮
    const closeAllBtn = document.getElementById('closeAllBtn');
    if (closeAllBtn) {
        closeAllBtn.style.display = 'none';
    }

    // 恢复刷新按钮
    enableRefreshButton();
}

function showPositionsListState() {
    document.getElementById('positionsLoadingState').style.display = 'none';
    document.getElementById('positionsErrorState').style.display = 'none';
    document.getElementById('noPositionsState').style.display = 'none';
    document.getElementById('positionsListContainer').style.display = 'block';

    // 显示全部平仓按钮
    const closeAllBtn = document.getElementById('closeAllBtn');
    if (closeAllBtn) {
        closeAllBtn.style.display = 'inline-block';
    }

    // 恢复刷新按钮
    enableRefreshButton();
}

function enableRefreshButton() {
    const refreshBtn = document.getElementById('refreshPositionsBtn');
    if (refreshBtn) {
        refreshBtn.disabled = false;
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新';
    }
}

// 显示当前持仓
function displayCurrentPositions(positions) {
    if (!positions || positions.length === 0) {
        showNoPositionsState();
        return;
    }

    // 显示持仓列表状态
    showPositionsListState();

    // 生成持仓卡片
    const container = document.getElementById('positionCardsContainer');
    const cardsHtml = positions.map(position => createPositionCard(position)).join('');
    container.innerHTML = cardsHtml;

    console.log(`✅ 显示 ${positions.length} 个持仓`);
}

// 更新持仓计数
function updatePositionCount(count) {
    const countBadge = document.getElementById('realTimePositionCount');
    if (countBadge) {
        countBadge.textContent = count;
    }

    // 同时更新交易统计中的持仓数
    const currentPositionsElement = document.getElementById('currentPositions');
    if (currentPositionsElement) {
        currentPositionsElement.textContent = count;
    }

    // 🔧 修复：同步更新tradingStatistics中的持仓数，确保交易条件检查使用实时数据
    if (typeof tradingStatistics !== 'undefined') {
        tradingStatistics.currentPositions = count;
        console.log(`🔄 同步更新持仓数: ${count}`);
    }
}

// 创建持仓卡片 - 适配MT5数据格式
function createPositionCard(position) {
    // 适配MT5数据格式
    const openTime = new Date(position.time * 1000); // MT5时间戳是秒
    const now = new Date();
    const duration = Math.floor((now - openTime) / (1000 * 60)); // 分钟

    // 格式化持仓时长
    let durationText = '';
    if (duration < 60) {
        durationText = `${duration}分钟`;
    } else if (duration < 1440) {
        const hours = Math.floor(duration / 60);
        const minutes = duration % 60;
        durationText = `${hours}小时${minutes}分钟`;
    } else {
        const days = Math.floor(duration / 1440);
        const hours = Math.floor((duration % 1440) / 60);
        durationText = `${days}天${hours}小时`;
    }

    // 格式化北京时间
    const beijingTime = openTime.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });

    // 确定方向和颜色 (MT5: type=0是买入, type=1是卖出)
    const isBuy = position.type === 0;
    const directionText = isBuy ? '买入' : '卖出';
    const directionClass = isBuy ? 'position-direction-buy' : 'position-direction-sell';
    const directionIcon = isBuy ? 'fa-arrow-up' : 'fa-arrow-down';

    // 盈亏颜色 (使用MT5的profit字段)
    const currentProfit = position.profit || 0;
    const profitClass = currentProfit >= 0 ? 'position-profit-positive' : 'position-profit-negative';
    const profitIcon = currentProfit >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';

    return `
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="position-card p-3">
                <div class="position-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 fw-bold">${position.symbol}</h6>
                            <span class="badge badge-direction ${directionClass}">
                                <i class="fas ${directionIcon} me-1"></i>${directionText}
                            </span>
                        </div>
                        <div class="text-end">
                            <div class="${profitClass}">
                                <i class="fas ${profitIcon} me-1"></i>
                                $${currentProfit.toFixed(2)}
                            </div>
                            <div class="small text-muted">
                                ${position.swap ? `掉期: $${parseFloat(position.swap).toFixed(2)}` : ''}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-6">
                        <div class="position-info-item">
                            <div class="position-info-label">数量</div>
                            <div class="position-info-value">${position.volume} 手</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="position-info-item">
                            <div class="position-info-label">开仓价</div>
                            <div class="position-info-value">${parseFloat(position.price_open).toFixed(5)}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="position-info-item">
                            <div class="position-info-label">当前价</div>
                            <div class="position-info-value">${parseFloat(position.price_current).toFixed(5)}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="position-info-item">
                            <div class="position-info-label">持仓时长</div>
                            <div class="position-info-value">${durationText}</div>
                        </div>
                    </div>
                </div>

                <div class="position-stats">
                    <div class="row">
                        <div class="col-6">
                            <div class="position-info-item">
                                <div class="position-info-label">止损</div>
                                <div class="position-info-value text-danger">
                                    ${position.sl && position.sl > 0 ? parseFloat(position.sl).toFixed(5) : '未设置'}
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="position-info-item">
                                <div class="position-info-label">止盈</div>
                                <div class="position-info-value text-success">
                                    ${position.tp && position.tp > 0 ? parseFloat(position.tp).toFixed(5) : '未设置'}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="time-badge">
                                <i class="fas fa-clock me-1"></i>${beijingTime}
                            </span>
                            <button class="btn btn-sm btn-outline-danger" onclick="closeMT5Position('${position.ticket}')">
                                <i class="fas fa-times me-1"></i>平仓 #${position.ticket}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 启动持仓自动刷新
function startPositionsAutoRefresh() {
    // 停止之前的定时器
    if (positionsRefreshInterval) {
        clearInterval(positionsRefreshInterval);
    }

    // 每30秒自动刷新一次
    positionsRefreshInterval = setInterval(() => {
        if (document.visibilityState === 'visible') {
            refreshPositions();
        }
    }, 30000);

    console.log('✅ 持仓自动刷新已启动');
}

// 停止持仓自动刷新
function stopPositionsAutoRefresh() {
    if (positionsRefreshInterval) {
        clearInterval(positionsRefreshInterval);
        positionsRefreshInterval = null;
        console.log('⏹️ 持仓自动刷新已停止');
    }
}

// 平仓MT5持仓
async function closeMT5Position(ticket) {
    if (!confirm(`确定要平仓订单 #${ticket} 吗？`)) {
        return;
    }

    try {
        const response = await fetch('/api/mt5/close-position', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ticket: ticket })
        });

        const result = await response.json();

        if (result.success) {
            showSuccess(`订单 #${ticket} 已平仓`);
            // 刷新持仓显示
            refreshPositions();
            // 更新交易统计
            updateTradingStatistics();
        } else {
            showError(`平仓失败: ${result.error}`);
        }
    } catch (error) {
        console.error('平仓失败:', error);
        showError(`平仓失败: ${error.message}`);
    }
}

// 兼容旧的函数名
function hidePositionDetails() {
    showNoPositionsState();
}

// 平仓单个持仓
async function closePosition(positionId) {
    if (!confirm('确定要平仓这个持仓吗？')) {
        return;
    }

    try {
        const response = await fetch('/api/deep-learning/close-position', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ position_id: positionId })
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('持仓已平仓');
            // 刷新持仓详情和统计
            loadPositionDetails();
            updateTradingStatistics();
        } else {
            showError('平仓失败: ' + result.error);
        }
    } catch (error) {
        console.error('平仓失败:', error);
        showError('平仓失败: ' + error.message);
    }
}

// 显示交易条件分析结果
function displayTradingConditionsAnalysis(conditionsCheck, allConditionsPassed, inferenceResult) {
    const analysisSection = document.getElementById('tradingConditionsAnalysis');
    const resultsContainer = document.getElementById('conditionsCheckResults');
    const decisionContainer = document.getElementById('tradingDecision');

    // 显示分析区域
    analysisSection.style.display = 'block';

    // 生成条件检查结果HTML
    let resultsHtml = '';
    Object.entries(conditionsCheck).forEach(([key, condition]) => {
        const statusIcon = condition.passed ?
            '<i class="fas fa-check-circle text-success"></i>' :
            '<i class="fas fa-times-circle text-danger"></i>';

        const conditionClass = condition.passed ? 'passed' : 'failed';
        const requiredBadge = condition.required ?
            '<span class="badge bg-danger ms-1" style="font-size: 0.6rem;">必需</span>' :
            '<span class="badge bg-secondary ms-1" style="font-size: 0.6rem;">可选</span>';

        resultsHtml += `
            <div class="condition-item ${conditionClass}">
                <div class="condition-label">
                    ${condition.label}${requiredBadge}
                </div>
                <div class="condition-value">${condition.value}</div>
                <div class="condition-status">${statusIcon}</div>
            </div>
        `;
    });

    resultsContainer.innerHTML = resultsHtml;

    // 生成交易决策显示
    let decisionHtml = '';
    let decisionClass = '';

    if (allConditionsPassed) {
        decisionClass = 'execute';
        decisionHtml = `
            <div class="trading-decision ${decisionClass}">
                <i class="fas fa-rocket me-2"></i>
                <strong>执行交易</strong> - 所有条件满足，正在执行 ${inferenceResult.prediction} 订单
            </div>
        `;
    } else {
        const failedConditions = Object.values(conditionsCheck).filter(c => c.required && !c.passed);
        const failedReasons = failedConditions.map(c => c.label).join('、');

        decisionClass = 'blocked';
        decisionHtml = `
            <div class="trading-decision ${decisionClass}">
                <i class="fas fa-ban me-2"></i>
                <strong>跳过交易</strong> - 未满足条件：${failedReasons}
            </div>
        `;
    }

    decisionContainer.innerHTML = decisionHtml;

    // 添加详细建议
    if (!allConditionsPassed) {
        const suggestions = generateTradingSuggestions(conditionsCheck);
        if (suggestions) {
            decisionContainer.innerHTML += `
                <div class="mt-2 p-2 bg-light rounded">
                    <small class="text-muted">
                        <i class="fas fa-lightbulb me-1"></i>
                        <strong>建议：</strong>${suggestions}
                    </small>
                </div>
            `;
        }
    }
}

// 生成交易建议
function generateTradingSuggestions(conditionsCheck) {
    const suggestions = [];

    if (!conditionsCheck.autoTrading.passed) {
        suggestions.push('启用自动交易开关');
    }

    if (!conditionsCheck.confidence.passed) {
        const currentConfidence = parseFloat(conditionsCheck.confidence.value.split('%')[0]);
        const requiredConfidence = parseFloat(conditionsCheck.confidence.value.split('/')[1]);
        suggestions.push(`降低最小置信度要求至${currentConfidence.toFixed(1)}%以下`);
    }

    if (!conditionsCheck.positions.passed) {
        suggestions.push('增加最大持仓数限制或平仓部分现有持仓');
    }

    if (!conditionsCheck.signal.passed) {
        suggestions.push('等待明确的买入或卖出信号');
    }

    if (!conditionsCheck.mt5Connection.passed) {
        suggestions.push('检查并重新连接MT5终端');
    }

    if (!conditionsCheck.tradingTime.passed) {
        suggestions.push('等待交易时间段或调整交易时间配置');
    }

    return suggestions.length > 0 ? suggestions.join('；') : null;
}

// 执行交易条件分析（仅分析，不执行交易）
async function performTradingConditionsAnalysis(inferenceResult) {
    // 🔧 修复：先刷新持仓数据
    await refreshPositionsForTradingCheck();

    // 获取交易配置参数
    const minConfidenceElement = document.getElementById('minConfidence');
    const maxPositionsElement = document.getElementById('maxPositions');
    const autoTradingElement = document.getElementById('enableAutoTrading');

    if (!minConfidenceElement || !maxPositionsElement || !autoTradingElement) {
        console.log('交易配置元素未找到，跳过条件分析');
        return;
    }

    const minConfidence = parseFloat(minConfidenceElement.value);
    const maxPositions = parseInt(maxPositionsElement.value);
    const autoTradingEnabled = autoTradingElement.checked;

    // 初始化条件检查结果
    const conditionsCheck = {
        autoTrading: {
            label: '自动交易开关',
            passed: autoTradingEnabled,
            value: autoTradingEnabled ? '已启用' : '已禁用',
            required: true
        },
        confidence: {
            label: '置信度检查',
            passed: inferenceResult.confidence && inferenceResult.confidence >= minConfidence,
            value: `${(inferenceResult.confidence * 100).toFixed(1)}% / ${(minConfidence * 100).toFixed(1)}%`,
            required: true
        },
        positions: {
            label: '持仓数限制',
            passed: tradingStatistics.currentPositions < maxPositions,
            value: `${tradingStatistics.currentPositions} / ${maxPositions}`,
            required: true
        },
        signal: {
            label: '交易信号',
            passed: inferenceResult.prediction !== 'HOLD',
            value: inferenceResult.prediction || 'UNKNOWN',
            required: true
        },
        mt5Connection: {
            label: 'MT5连接',
            passed: mt5Connected,
            value: mt5Connected ? '已连接' : '未连接',
            required: true
        }
    };

    // 检查交易时间（如果有配置）
    const tradingHours = isWithinTradingHours();
    conditionsCheck.tradingTime = {
        label: '交易时间',
        passed: tradingHours,
        value: tradingHours ? '交易时间内' : '非交易时间',
        required: false
    };

    // 计算总体结果
    const requiredConditions = Object.values(conditionsCheck).filter(c => c.required);
    const allConditionsPassed = requiredConditions.every(c => c.passed);

    // 显示条件检查结果
    displayTradingConditionsAnalysis(conditionsCheck, allConditionsPassed, inferenceResult);
}

// 检查是否在交易时间内
function isWithinTradingHours() {
    const now = new Date();
    const beijingTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Shanghai"}));
    const hour = beijingTime.getHours();
    const day = beijingTime.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

    // 检查是否为工作日（周一到周五）
    if (day === 0 || day === 6) {
        return false; // 周末不交易
    }

    // 检查是否在交易时间内（这里可以根据实际需要调整）
    // 假设交易时间为：09:00-17:00
    if (hour >= 9 && hour < 17) {
        return true;
    }

    return false;
}

// 手动下单功能
async function executeManualTrade(action) {
    console.log(`🖱️ 手动下单: ${action}`);

    // 检查前置条件
    if (!mt5Connected) {
        showError('MT5未连接，无法执行手动下单');
        return;
    }

    if (!selectedTradingModel) {
        showError('请先选择交易模型');
        return;
    }

    // 显示下单状态
    showManualTradeStatus(`正在执行${action === 'BUY' ? '买入' : '卖出'}订单...`, 'info');

    // 禁用下单按钮并显示处理中状态
    setManualTradeButtonsEnabled(false, true);

    try {
        // 获取当前交易配置
        const tradingConfig = getTradingConfig();
        console.log('📊 手动下单使用的交易配置:', tradingConfig);

        // 构建交易数据，使用与AI自动交易相同的参数
        const tradeData = {
            symbol: selectedTradingModel.symbol,
            action: action, // BUY or SELL
            lot_size: tradingConfig.lot_size,
            stop_loss_pips: tradingConfig.stop_loss_pips,
            take_profit_pips: tradingConfig.take_profit_pips,
            trading_config: tradingConfig,
            manual_trade: true, // 标记为手动交易
            inference_result: {
                prediction: action,
                confidence: 1.0, // 手动交易置信度为100%
                current_price: currentMarketData ? (action === 'BUY' ? currentMarketData.ask : currentMarketData.bid) : 0,
                reasoning: `手动${action === 'BUY' ? '买入' : '卖出'}交易`
            }
        };

        console.log('🚀 发送手动交易请求:', tradeData);

        // 调用深度学习交易执行API
        const response = await fetch('/api/deep-learning/execute-trade', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(tradeData)
        });

        const result = await response.json();

        if (result.success) {
            console.log('✅ 手动下单成功:', result);
            showManualTradeStatus(`${action === 'BUY' ? '买入' : '卖出'}订单执行成功！订单ID: ${result.order_id}`, 'success');
            showSuccess(`手动${action === 'BUY' ? '买入' : '卖出'}订单执行成功！`);

            // 更新交易统计
            updateTradingStatistics();

            // 3秒后隐藏状态
            setTimeout(() => {
                hideManualTradeStatus();
            }, 3000);

        } else {
            console.error('❌ 手动下单失败:', result.error);
            showManualTradeStatus(`下单失败: ${result.error}`, 'danger');
            showError(`手动下单失败: ${result.error}`);
        }

    } catch (error) {
        console.error('❌ 手动下单异常:', error);
        showManualTradeStatus(`下单异常: ${error.message}`, 'danger');
        showError(`手动下单异常: ${error.message}`);
    } finally {
        // 重新检查并更新按钮状态
        setTimeout(() => {
            updateManualTradeButtonsStatus();
        }, 2000);
    }
}

// 显示手动下单状态
function showManualTradeStatus(message, type = 'info') {
    const statusDiv = document.getElementById('manualTradeStatus');
    const statusText = document.getElementById('manualTradeStatusText');

    if (statusDiv && statusText) {
        statusText.textContent = message;
        statusDiv.style.display = 'block';

        // 更新样式
        const alertDiv = statusDiv.querySelector('.alert');
        if (alertDiv) {
            alertDiv.className = `alert alert-${type} mb-0 py-2`;
        }
    }
}

// 隐藏手动下单状态
function hideManualTradeStatus() {
    const statusDiv = document.getElementById('manualTradeStatus');
    if (statusDiv) {
        statusDiv.style.display = 'none';
    }
}

// 设置手动下单按钮启用状态
function setManualTradeButtonsEnabled(enabled, isProcessing = false) {
    const buyBtn = document.getElementById('manualBuyBtn');
    const sellBtn = document.getElementById('manualSellBtn');

    console.log(`🔄 设置手动下单按钮状态: ${enabled ? '启用' : '禁用'}, 处理中: ${isProcessing}`);

    if (buyBtn) {
        buyBtn.disabled = !enabled;
        if (enabled) {
            buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
            buyBtn.className = 'btn btn-success w-100';
        } else if (isProcessing) {
            buyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
            buyBtn.className = 'btn btn-secondary w-100';
        } else {
            // 正常禁用状态，不显示"处理中"
            buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
            buyBtn.className = 'btn btn-outline-success w-100';
        }
    }

    if (sellBtn) {
        sellBtn.disabled = !enabled;
        if (enabled) {
            sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
            sellBtn.className = 'btn btn-danger w-100';
        } else if (isProcessing) {
            sellBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
            sellBtn.className = 'btn btn-secondary w-100';
        } else {
            // 正常禁用状态，不显示"处理中"
            sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
            sellBtn.className = 'btn btn-outline-danger w-100';
        }
    }
}

// 初始化手动下单按钮状态
function initializeManualTradeButtons() {
    console.log('🔄 初始化手动下单按钮状态');

    // 确保按钮元素存在
    const buyBtn = document.getElementById('manualBuyBtn');
    const sellBtn = document.getElementById('manualSellBtn');

    if (!buyBtn || !sellBtn) {
        console.log('⚠️ 手动下单按钮元素未找到，稍后重试');
        setTimeout(initializeManualTradeButtons, 500);
        return;
    }

    // 强制重置按钮状态为正常状态
    buyBtn.disabled = true;
    buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
    buyBtn.className = 'btn btn-success w-100';

    sellBtn.disabled = true;
    sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
    sellBtn.className = 'btn btn-danger w-100';

    console.log('✅ 手动下单按钮已重置为初始状态');

    // 然后根据实际条件更新状态
    setTimeout(updateManualTradeButtonsStatus, 100);
}

// 更新手动下单按钮状态
function updateManualTradeButtonsStatus() {
    // 确保按钮元素存在
    const buyBtn = document.getElementById('manualBuyBtn');
    const sellBtn = document.getElementById('manualSellBtn');

    if (!buyBtn || !sellBtn) {
        console.log('⚠️ 手动下单按钮元素未找到，稍后重试');
        setTimeout(updateManualTradeButtonsStatus, 500);
        return;
    }

    const canTrade = mt5Connected && selectedTradingModel && currentMarketData;

    console.log('🔍 检查手动下单条件:', {
        mt5Connected: mt5Connected,
        selectedTradingModel: !!selectedTradingModel,
        currentMarketData: !!currentMarketData,
        canTrade: canTrade
    });

    setManualTradeButtonsEnabled(canTrade, false); // 正常状态，不是处理中

    if (!canTrade) {
        let reason = '';
        if (!mt5Connected) reason = 'MT5未连接';
        else if (!selectedTradingModel) reason = '未选择交易模型';
        else if (!currentMarketData) reason = '无市场数据';

        console.log(`🔒 手动下单按钮已禁用: ${reason}`);
    } else {
        console.log('🔓 手动下单按钮已启用');
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 自动应用平衡型配置作为默认配置
    applyTradingPreset();

    // 初始化移动止损配置显示状态
    toggleInferenceTrailingStopConfig();

    // 初始化其他配置显示状态
    if (typeof toggleTrailingStopConfig === 'function') {
        toggleTrailingStopConfig();
    }

    // 初始化手动下单按钮状态 - 先重置再更新
    console.log('🔄 开始初始化手动下单按钮...');
    initializeManualTradeButtons();

    // 启动持仓自动刷新
    console.log('🔄 启动持仓自动刷新...');
    startPositionsAutoRefresh();

    console.log('✅ AI推理交易页面初始化完成，已应用平衡型默认配置');
});

// 页面可见性变化时重新检查按钮状态
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        console.log('🔄 页面重新可见，检查手动下单按钮状态');
        setTimeout(initializeManualTradeButtons, 100);
    }
});

// 窗口获得焦点时重新检查按钮状态
window.addEventListener('focus', function() {
    console.log('🔄 窗口获得焦点，检查手动下单按钮状态');
    setTimeout(initializeManualTradeButtons, 100);
});

// 增强特征配置相关函数
function toggleEnhancedFeaturesConfig() {
    const checkbox = document.getElementById('useEnhancedFeatures');
    const details = document.getElementById('enhancedFeaturesDetails');

    if (checkbox.checked) {
        details.style.display = 'block';
        console.log('✅ 启用增强特征配置');
    } else {
        details.style.display = 'none';
        console.log('❌ 禁用增强特征配置');
    }
}

// 动态风险管理配置相关函数
function toggleDynamicRiskConfig() {
    const checkbox = document.getElementById('enableDynamicRiskManagement');
    const details = document.getElementById('dynamicRiskDetails');

    if (checkbox.checked) {
        details.style.display = 'block';
        console.log('✅ 启用动态风险管理配置');
    } else {
        details.style.display = 'none';
        console.log('❌ 禁用动态风险管理配置');
    }
}

// 获取增强特征配置
function getEnhancedFeaturesConfig() {
    const useEnhanced = document.getElementById('useEnhancedFeatures').checked;

    if (!useEnhanced) {
        return {
            use_enhanced_features: false
        };
    }

    const strategy = document.getElementById('featureSelectionStrategy').value;
    const analyzeImportance = document.getElementById('analyzeFeatureImportance').checked;

    let selectedFeatures = null;

    if (strategy === 'custom') {
        // 收集自定义选择的特征
        selectedFeatures = [];
        const featureCheckboxes = document.querySelectorAll('[id^="feature_"]');
        featureCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const featureName = checkbox.id.replace('feature_', '');
                selectedFeatures.push(featureName);
            }
        });
    }

    return {
        use_enhanced_features: true,
        analyze_feature_importance: analyzeImportance,
        feature_selection_strategy: strategy,
        selected_features: selectedFeatures
    };
}

// 获取动态风险管理配置
function getDynamicRiskConfig() {
    const enabled = document.getElementById('enableDynamicRiskManagement').checked;

    if (!enabled) {
        return {
            enable_dynamic_risk_management: false
        };
    }

    return {
        enable_dynamic_risk_management: true,
        atr_multiplier: parseFloat(document.getElementById('atrMultiplier').value),
        risk_per_trade: parseFloat(document.getElementById('riskPerTrade').value) / 100,
        min_stop_pips: parseInt(document.getElementById('minStopLossPips').value),
        volatility_multipliers: {
            low: parseFloat(document.getElementById('lowVolatilityMultiplier').value),
            medium: parseFloat(document.getElementById('mediumVolatilityMultiplier').value),
            high: parseFloat(document.getElementById('highVolatilityMultiplier').value)
        }
    };
}

// 关闭增强特征提示
function dismissEnhancedFeaturesHint() {
    const hint = document.getElementById('enhancedFeaturesHint');
    if (hint) {
        hint.style.display = 'none';
        // 保存到localStorage，避免重复显示
        localStorage.setItem('enhancedFeaturesHintDismissed', 'true');
    }
}

// 显示特征重要性分析结果HTML
function displayFeatureImportanceHTML(importanceData) {
    if (!importanceData || Object.keys(importanceData).length === 0) {
        return '';
    }

    // 排序特征重要性
    const sortedFeatures = Object.entries(importanceData)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10); // 只显示前10个最重要的特征

    let html = `
        <div class="card border-info mt-3">
            <div class="card-header bg-info bg-opacity-10">
                <h6 class="mb-0 text-info">
                    <i class="fas fa-chart-bar me-2"></i>特征重要性分析 (前10)
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
    `;

    sortedFeatures.forEach(([feature, importance], index) => {
        const percentage = (importance * 100).toFixed(1);
        const progressClass = importance > 0.5 ? 'bg-success' : importance > 0.3 ? 'bg-warning' : 'bg-info';

        html += `
            <div class="col-md-6 mb-2">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <small class="fw-bold">${index + 1}. ${feature}</small>
                    <small class="text-muted">${importance.toFixed(4)}</small>
                </div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar ${progressClass}" style="width: ${percentage}%"></div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        重要性分数越高，该特征对预测结果的影响越大
                    </small>
                </div>
            </div>
        </div>
    `;

    return html;
}

// 显示增强特征配置信息
function displayEnhancedFeaturesInfo(results) {
    const strategy = results.feature_selection_strategy || 'recommended';
    const strategyNames = {
        'recommended': '推荐特征集',
        'all': '全部增强特征',
        'top_importance': '重要性前15个',
        'custom': '自定义选择'
    };

    return `
        <div class="card border-primary mt-3">
            <div class="card-header bg-primary bg-opacity-10">
                <h6 class="mb-0 text-primary">
                    <i class="fas fa-brain me-2"></i>增强特征配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">特征选择策略:</small> <strong>${strategyNames[strategy]}</strong><br>
                        <small class="text-muted">特征重要性分析:</small> <strong>${results.analyze_feature_importance ? '启用' : '禁用'}</strong>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">使用增强特征:</small> <strong class="text-success">是</strong><br>
                        <small class="text-muted">特征类型:</small> <strong>布林带、ATR、随机指标、组合信号</strong>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 显示动态风险管理信息
function displayDynamicRiskInfo(results) {
    return `
        <div class="card border-warning mt-3">
            <div class="card-header bg-warning bg-opacity-10">
                <h6 class="mb-0 text-warning">
                    <i class="fas fa-shield-alt me-2"></i>动态风险管理
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">ATR倍数:</small> <strong>${results.atr_multiplier || 2.0}</strong><br>
                        <small class="text-muted">风险比例:</small> <strong>${((results.risk_per_trade || 0.02) * 100).toFixed(1)}%</strong>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">最小止损:</small> <strong>${results.min_stop_pips || 10} pips</strong><br>
                        <small class="text-muted">波动性自适应:</small> <strong class="text-success">启用</strong>
                    </div>
                </div>
            </div>
        </div>
    `;
}

</script>

            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 全局状态管理器 -->
    <script src="/static/js/global_state_manager.js"></script>
    <!-- 自定义JS -->
    <script src="/static/js/common.js"></script>
    <!-- AI交易全局管理器 -->
    <script src="/static/js/ai_trading_manager.js"></script>

    
</body>
</html>