#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制完成第45轮训练
将卡住的训练标记为完成
"""

import sqlite3
from datetime import datetime

def force_complete_training():
    """强制完成训练"""
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找卡住的任务
        cursor.execute("""
            SELECT id FROM training_tasks 
            WHERE status = 'running' AND current_epoch >= total_epochs
            ORDER BY updated_at DESC LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            task_id = result[0]
            
            # 更新任务状态为completed
            cursor.execute("""
                UPDATE training_tasks 
                SET status = 'completed',
                    completed_at = ?,
                    updated_at = ?,
                    progress = 100.0
                WHERE id = ?
            """, (datetime.now().isoformat(), datetime.now().isoformat(), task_id))
            
            conn.commit()
            print(f"✅ 任务 {task_id} 已强制标记为完成")
        else:
            print("❌ 没有找到需要完成的任务")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 强制完成失败: {e}")

if __name__ == "__main__":
    force_complete_training()
