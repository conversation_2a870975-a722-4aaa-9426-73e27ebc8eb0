#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断训练进度卡住问题
"""

import sqlite3
import json
import os
import time
from datetime import datetime, timedelta

def get_current_training_tasks():
    """获取当前正在运行的训练任务"""
    print("🔍 查找当前运行中的训练任务...")
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找状态为running的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, logs, created_at, started_at, updated_at
            FROM training_tasks
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("ℹ️ 没有找到运行中的训练任务")
            return []
        
        print(f"📊 找到 {len(tasks)} 个运行中的训练任务")
        return tasks
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return []

def analyze_task_status(task_data):
    """分析单个任务的状态"""
    (task_id, model_id, status, progress, current_epoch, total_epochs,
     train_loss, val_loss, logs, created_at, started_at, updated_at) = task_data
    
    print(f"\n📋 任务详细分析: {task_id[:8]}...")
    print("=" * 50)
    
    print(f"📊 基本信息:")
    print(f"   任务ID: {task_id}")
    print(f"   模型ID: {model_id}")
    print(f"   状态: {status}")
    print(f"   进度: {progress}%")
    print(f"   当前轮次: {current_epoch}/{total_epochs}")
    print(f"   训练损失: {train_loss}")
    print(f"   验证损失: {val_loss}")
    
    print(f"\n⏰ 时间信息:")
    print(f"   创建时间: {created_at}")
    print(f"   开始时间: {started_at}")
    print(f"   更新时间: {updated_at}")
    
    # 计算运行时长
    if updated_at:
        try:
            updated_time = datetime.fromisoformat(updated_at.replace('T', ' '))
            now = datetime.now()
            duration = now - updated_time
            print(f"   距离上次更新: {duration}")
            
            # 如果超过10分钟没有更新，可能卡住了
            if duration > timedelta(minutes=10):
                print("   ⚠️ 警告: 超过10分钟没有更新，可能卡住了")
        except:
            print("   ❌ 时间解析失败")
    
    # 分析日志
    print(f"\n📝 日志分析:")
    if logs:
        try:
            log_data = json.loads(logs)
            stage = log_data.get('stage', 'unknown')
            message = log_data.get('message', '')
            
            print(f"   阶段: {stage}")
            print(f"   消息: {message}")
            
            # 检查是否有错误信息
            if 'error' in log_data:
                print(f"   ❌ 错误: {log_data['error']}")
            
            # 检查数据信息
            if 'data_info' in log_data:
                data_info = log_data['data_info']
                print(f"   📊 数据信息: {data_info}")
            
        except json.JSONDecodeError:
            print(f"   ❌ 日志解析失败: {logs[:100]}...")
    else:
        print("   ⚠️ 无日志信息")
    
    # 问题诊断
    print(f"\n🔍 问题诊断:")
    issues = []
    
    # 检查进度与轮次不匹配
    if progress > 0 and current_epoch == 0:
        issues.append("进度显示有值但轮次为0，可能进度更新机制有问题")
    
    # 检查损失为0
    if train_loss == 0.0 and val_loss == 0.0 and current_epoch == 0:
        issues.append("训练损失和验证损失都为0，训练可能未真正开始")
    
    # 检查长时间无更新
    if updated_at:
        try:
            updated_time = datetime.fromisoformat(updated_at.replace('T', ' '))
            if datetime.now() - updated_time > timedelta(minutes=10):
                issues.append("长时间无状态更新，训练可能卡住")
        except:
            pass
    
    if issues:
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. ⚠️ {issue}")
    else:
        print("   ✅ 未发现明显问题")
    
    return issues

def check_system_resources():
    """检查系统资源状态"""
    print(f"\n💻 系统资源检查:")
    print("=" * 30)
    
    try:
        import psutil
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"   CPU使用率: {cpu_percent}%")
        
        # 内存使用率
        memory = psutil.virtual_memory()
        print(f"   内存使用率: {memory.percent}%")
        print(f"   可用内存: {memory.available / 1024 / 1024 / 1024:.1f} GB")
        
        # 检查Python进程
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                if 'python' in proc.info['name'].lower():
                    python_processes.append(proc.info)
            except:
                pass
        
        if python_processes:
            print(f"   Python进程数: {len(python_processes)}")
            for proc in python_processes[:3]:  # 只显示前3个
                print(f"     PID {proc['pid']}: CPU {proc['cpu_percent']:.1f}%, 内存 {proc['memory_percent']:.1f}%")
        
    except ImportError:
        print("   ⚠️ psutil未安装，无法检查系统资源")
    except Exception as e:
        print(f"   ❌ 资源检查失败: {e}")

def suggest_solutions(issues):
    """根据问题提供解决方案"""
    print(f"\n🔧 解决方案建议:")
    print("=" * 30)
    
    if not issues:
        print("   ✅ 未发现问题，训练可能正常进行中")
        return
    
    solutions = []
    
    for issue in issues:
        if "进度更新机制" in issue:
            solutions.append("检查训练进度更新逻辑，确保_update_task_progress正确调用")
        
        if "训练可能未真正开始" in issue:
            solutions.append("检查训练数据加载和模型初始化过程")
        
        if "训练可能卡住" in issue:
            solutions.append("考虑重启训练任务或检查训练循环是否正常")
    
    # 去重
    solutions = list(set(solutions))
    
    for i, solution in enumerate(solutions, 1):
        print(f"   {i}. 💡 {solution}")
    
    print(f"\n🛠️ 立即可执行的操作:")
    print("   1. 运行 python fix_stuck_training.py 尝试修复卡住的训练")
    print("   2. 检查应用程序日志文件")
    print("   3. 重启训练服务")

def main():
    """主函数"""
    print("🔍 训练进度卡住问题诊断工具")
    print("=" * 60)
    
    # 1. 获取当前训练任务
    tasks = get_current_training_tasks()
    
    if not tasks:
        print("\n✅ 没有运行中的训练任务需要诊断")
        return
    
    all_issues = []
    
    # 2. 分析每个任务
    for task in tasks:
        issues = analyze_task_status(task)
        all_issues.extend(issues)
    
    # 3. 检查系统资源
    check_system_resources()
    
    # 4. 提供解决方案
    suggest_solutions(all_issues)
    
    print(f"\n📋 诊断完成！")
    if all_issues:
        print(f"   发现 {len(all_issues)} 个潜在问题")
    else:
        print("   未发现明显问题")

if __name__ == "__main__":
    main()
