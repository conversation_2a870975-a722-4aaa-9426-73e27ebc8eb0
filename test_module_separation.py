#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI推理交易和AI推理回测模块分离
验证两个模块是否正确分离并可以独立访问
"""

import requests
from bs4 import BeautifulSoup
import re

def test_module_separation():
    """测试模块分离"""
    print("🧪 测试AI推理交易和AI推理回测模块分离")
    print("=" * 70)
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 2. 测试导航栏结构
        print("\n📋 测试导航栏结构...")
        response = session.get('http://127.0.0.1:5000/dashboard')
        
        if response.status_code != 200:
            print(f"❌ 访问首页失败: {response.status_code}")
            return False
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找导航栏
        sidebar = soup.find('nav', class_='sidebar')
        if not sidebar:
            print("❌ 未找到导航栏")
            return False
        
        # 提取所有导航链接
        nav_links = sidebar.find_all('a', class_='nav-link')
        
        # 查找AI推理相关的链接
        ai_inference_links = []
        ai_backtest_links = []
        
        for link in nav_links:
            link_text = link.get_text(strip=True)
            href = link.get('href', '')
            
            if 'AI推理交易' in link_text:
                ai_inference_links.append({
                    'text': link_text,
                    'href': href
                })
            elif 'AI推理回测' in link_text:
                ai_backtest_links.append({
                    'text': link_text,
                    'href': href
                })
        
        # 验证导航链接
        success = True
        
        if ai_inference_links:
            print("✅ 找到AI推理交易导航链接:")
            for link in ai_inference_links:
                print(f"   • {link['text']} -> {link['href']}")
        else:
            print("❌ 未找到AI推理交易导航链接")
            success = False
        
        if ai_backtest_links:
            print("✅ 找到AI推理回测导航链接:")
            for link in ai_backtest_links:
                print(f"   • {link['text']} -> {link['href']}")
        else:
            print("❌ 未找到AI推理回测导航链接")
            success = False
        
        # 3. 测试AI推理交易页面
        print(f"\n🌐 测试AI推理交易页面...")
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            print("✅ AI推理交易页面访问正常")
            
            # 检查页面内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查页面标题
            page_title = soup.find('title')
            if page_title and 'AI推理交易' in page_title.get_text():
                print("✅ AI推理交易页面标题正确")
            else:
                print("⚠️ AI推理交易页面标题可能不正确")
            
            # 检查是否包含实盘交易相关内容
            page_content = response.text
            if 'AI推理交易配置' in page_content:
                print("✅ 包含AI推理交易配置内容")
            else:
                print("❌ 缺少AI推理交易配置内容")
                success = False
            
            # 检查是否还包含回测相关内容（应该被移除）
            backtest_indicators = [
                'backtestConfig',
                '回测配置',
                'startBacktest',
                '开始回测'
            ]
            
            found_backtest_content = []
            for indicator in backtest_indicators:
                if indicator in page_content:
                    found_backtest_content.append(indicator)
            
            if found_backtest_content:
                print("⚠️ AI推理交易页面仍包含回测相关内容:")
                for content in found_backtest_content:
                    print(f"   • {content}")
            else:
                print("✅ AI推理交易页面已清理回测相关内容")
                
        else:
            print(f"❌ AI推理交易页面访问失败: {response.status_code}")
            success = False
        
        # 4. 测试AI推理回测页面
        print(f"\n🌐 测试AI推理回测页面...")
        response = session.get('http://127.0.0.1:5000/deep-learning/backtest')
        
        if response.status_code == 200:
            print("✅ AI推理回测页面访问正常")
            
            # 检查页面内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查页面标题
            page_title = soup.find('title')
            if page_title and 'AI推理回测' in page_title.get_text():
                print("✅ AI推理回测页面标题正确")
            else:
                print("⚠️ AI推理回测页面标题可能不正确")
            
            # 检查是否包含回测相关内容
            page_content = response.text
            if '回测' in page_content or 'backtest' in page_content.lower():
                print("✅ 包含回测相关内容")
            else:
                print("❌ 缺少回测相关内容")
                success = False
                
        else:
            print(f"❌ AI推理回测页面访问失败: {response.status_code}")
            success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🔧 AI推理交易和AI推理回测模块分离测试工具")
    print("=" * 80)
    
    print("📋 测试内容:")
    print("• 导航栏是否包含两个独立的模块链接")
    print("• AI推理交易页面是否正常访问")
    print("• AI推理回测页面是否正常访问")
    print("• 两个页面的内容是否正确分离")
    print()
    
    success = test_module_separation()
    
    if success:
        print("\n🎉 模块分离测试通过！")
        print("✅ 成功实现以下分离:")
        print("   • AI推理交易：专注于实盘交易功能")
        print("   • AI推理回测：专注于历史数据回测功能")
        print("   • 两个模块在导航栏中独立显示")
        print("   • 用户可以分别访问两个功能")
        print()
        print("💡 用户体验改进:")
        print("   • 功能更加清晰明确")
        print("   • 避免了界面混乱")
        print("   • 便于用户快速找到所需功能")
    else:
        print("\n❌ 模块分离测试失败")
        print("🔧 请检查相关配置和实施情况")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
