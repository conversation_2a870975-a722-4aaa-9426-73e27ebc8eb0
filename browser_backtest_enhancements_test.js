
// AI推理回测增强功能测试脚本
console.log("🧪 开始测试AI推理回测增强功能...");

// 1. 测试增强特征配置功能
console.log("📋 测试增强特征配置:");
const enhancedFeaturesTests = [
    'useEnhancedFeatures',
    'featureSelectionStrategy', 
    'analyzeFeatureImportance',
    'customFeaturesConfig'
];

enhancedFeaturesTests.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ ${id}: 元素存在`);
    } else {
        console.log(`❌ ${id}: 元素不存在`);
    }
});

// 2. 测试增强特征配置函数
console.log("📋 测试增强特征配置函数:");
const enhancedFunctionTests = [
    'toggleEnhancedFeaturesConfig',
    'toggleCustomFeaturesConfig',
    'getEnhancedFeaturesConfig'
];

enhancedFunctionTests.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName}: 函数存在`);
    } else {
        console.log(`❌ ${funcName}: 函数不存在`);
    }
});

// 3. 测试MT5连接功能
console.log("📋 测试MT5连接功能:");
const mt5Tests = [
    'mt5ConnectionStatus',
    'reconnectMT5Btn'
];

mt5Tests.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ ${id}: 元素存在`);
    } else {
        console.log(`❌ ${id}: 元素不存在`);
    }
});

// 4. 测试MT5连接函数
console.log("📋 测试MT5连接函数:");
const mt5FunctionTests = [
    'checkMT5Connection',
    'autoReconnectMT5',
    'reconnectMT5',
    'startMT5ConnectionMonitoring'
];

mt5FunctionTests.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName}: 函数存在`);
    } else {
        console.log(`❌ ${funcName}: 函数不存在`);
    }
});

// 5. 测试增强特征配置切换
console.log("📋 测试增强特征配置切换:");
if (typeof toggleEnhancedFeaturesConfig === 'function') {
    console.log("🔄 测试增强特征开关...");
    const checkbox = document.getElementById('useEnhancedFeatures');
    if (checkbox) {
        checkbox.checked = true;
        toggleEnhancedFeaturesConfig();
        console.log("✅ 增强特征配置已启用");
        
        // 测试自定义特征配置
        const strategySelect = document.getElementById('featureSelectionStrategy');
        if (strategySelect) {
            strategySelect.value = 'custom';
            toggleCustomFeaturesConfig();
            console.log("✅ 自定义特征配置已显示");
        }
    }
}

// 6. 测试获取增强特征配置
console.log("📋 测试获取增强特征配置:");
if (typeof getEnhancedFeaturesConfig === 'function') {
    const config = getEnhancedFeaturesConfig();
    console.log("增强特征配置:", config);
}

// 7. 手动测试MT5连接检查
console.log("📋 测试MT5连接检查:");
if (typeof checkMT5Connection === 'function') {
    console.log("🔄 检查MT5连接状态...");
    checkMT5Connection();
}

console.log("🎉 AI推理回测增强功能测试完成！");
console.log("💡 功能说明:");
console.log("   1. 增强特征配置：提升回测预测准确性");
console.log("   2. MT5自动重连：确保数据连接稳定");
console.log("   3. 连接状态监控：实时显示连接状态");
