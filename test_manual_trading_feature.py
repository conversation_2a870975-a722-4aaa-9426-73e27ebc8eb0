#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI推理交易手动下单功能
验证手动下单功能是否正常工作，包括参数一致性和动态监控
"""

import requests
import json
import time
from datetime import datetime

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post('http://127.0.0.1:5000/login', data=login_data)
    if response.status_code == 200:
        print("✅ 登录成功")
        return session
    else:
        print(f"❌ 登录失败: {response.status_code}")
        return None

def test_manual_trading_prerequisites(session):
    """测试手动下单前置条件"""
    print("\n🔍 检查手动下单前置条件")
    print("=" * 60)
    
    # 1. 检查MT5连接
    try:
        response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('connected'):
                print("✅ MT5连接正常")
                mt5_ok = True
            else:
                print("❌ MT5未连接")
                mt5_ok = False
        else:
            print("❌ 无法检查MT5连接状态")
            mt5_ok = False
    except Exception as e:
        print(f"❌ 检查MT5连接异常: {e}")
        mt5_ok = False
    
    # 2. 检查交易模型
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/auto-trading/status')
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('active'):
                model_info = result.get('model_info', {})
                print(f"✅ 交易模型已选择: {model_info.get('name')} ({model_info.get('symbol')})")
                model_ok = True
                selected_model = model_info
            else:
                print("❌ 未选择交易模型或自动交易未启动")
                model_ok = False
                selected_model = None
        else:
            print("❌ 无法获取交易模型状态")
            model_ok = False
            selected_model = None
    except Exception as e:
        print(f"❌ 检查交易模型异常: {e}")
        model_ok = False
        selected_model = None
    
    # 3. 检查市场数据
    if selected_model:
        try:
            symbol = selected_model.get('symbol', 'XAUUSD')
            response = session.get(f'http://127.0.0.1:5000/api/mt5/market-data/{symbol}')
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('market_data'):
                    market_data = result['market_data']
                    print(f"✅ 市场数据正常: {symbol} 买价={market_data.get('bid'):.5f}, 卖价={market_data.get('ask'):.5f}")
                    market_ok = True
                else:
                    print(f"❌ 无法获取{symbol}市场数据")
                    market_ok = False
            else:
                print(f"❌ 市场数据API请求失败")
                market_ok = False
        except Exception as e:
            print(f"❌ 检查市场数据异常: {e}")
            market_ok = False
    else:
        market_ok = False
    
    return mt5_ok and model_ok and market_ok, selected_model

def test_manual_trade_execution(session, model_info, action='BUY'):
    """测试手动下单执行"""
    print(f"\n🚀 测试手动{action}下单")
    print("=" * 60)
    
    # 构建手动交易数据（使用与AI自动交易相同的参数）
    trade_data = {
        'symbol': model_info.get('symbol', 'XAUUSD'),
        'action': action,
        'lot_size': 0.01,  # 使用默认手数
        'stop_loss_pips': 50,  # 使用默认止损
        'take_profit_pips': 100,  # 使用默认止盈
        'manual_trade': True,
        'trading_config': {
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'enable_trailing_stop': True,  # 启用移动止损
            'trailing_stop_distance': 50,
            'trailing_stop_step': 10,
            'enable_dynamic_sl': True,
            'enable_cliff_brake': False
        },
        'inference_result': {
            'prediction': action,
            'confidence': 1.0,  # 手动交易置信度100%
            'reasoning': f'手动{action}交易测试'
        }
    }
    
    print(f"📊 交易参数:")
    print(f"   品种: {trade_data['symbol']}")
    print(f"   方向: {trade_data['action']}")
    print(f"   手数: {trade_data['lot_size']}")
    print(f"   止损: {trade_data['stop_loss_pips']} pips")
    print(f"   止盈: {trade_data['take_profit_pips']} pips")
    print(f"   移动止损: {trade_data['trading_config']['enable_trailing_stop']}")
    print(f"   止损距离: {trade_data['trading_config']['trailing_stop_distance']} pips")
    print(f"   止损步长: {trade_data['trading_config']['trailing_stop_step']} pips")
    
    try:
        print(f"\n🔄 发送手动交易请求...")
        response = session.post('http://127.0.0.1:5000/api/deep-learning/execute-trade', 
                               json=trade_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 手动{action}下单成功!")
                print(f"   订单ID: {result.get('order_id')}")
                print(f"   入场价: {result.get('entry_price')}")
                print(f"   止损价: {result.get('sl_price')}")
                print(f"   止盈价: {result.get('tp_price')}")
                print(f"   消息: {result.get('message')}")
                
                return True, result
            else:
                print(f"❌ 手动{action}下单失败: {result.get('error')}")
                return False, result
        else:
            print(f"❌ 手动交易请求失败: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ 手动交易请求异常: {e}")
        return False, None

def test_trailing_stop_monitoring():
    """测试移动止损监控"""
    print(f"\n📊 测试移动止损监控")
    print("=" * 60)
    
    try:
        # 检查止盈止损监控服务状态
        from services.stop_loss_take_profit_service import stop_loss_take_profit_service
        
        if stop_loss_take_profit_service.monitoring:
            print("✅ 止盈止损监控服务正在运行")
            print(f"   检查间隔: {stop_loss_take_profit_service.check_interval}秒")
            
            # 检查是否有持仓需要监控
            from services.mt5_service import mt5_service
            if mt5_service.is_connected():
                positions = mt5_service.get_positions()
                if positions:
                    ai_positions = [pos for pos in positions if 'AI' in pos.get('comment', '')]
                    print(f"📈 当前AI交易持仓: {len(ai_positions)}个")
                    
                    for pos in ai_positions[:3]:  # 显示前3个
                        print(f"   订单{pos.get('ticket')}: {pos.get('symbol')} {pos.get('type_str', 'Unknown')} {pos.get('volume')}手")
                else:
                    print("📊 当前无持仓")
            else:
                print("⚠️ MT5未连接，无法检查持仓")
            
            return True
        else:
            print("❌ 止盈止损监控服务未运行")
            return False
            
    except Exception as e:
        print(f"❌ 检查移动止损监控异常: {e}")
        return False

def test_trading_statistics(session):
    """测试交易统计"""
    print(f"\n📊 检查交易统计")
    print("=" * 60)
    
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/trading-statistics')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('statistics', {})
                print(f"✅ 交易统计:")
                print(f"   当前持仓: {stats.get('current_positions', 0)}")
                print(f"   今日交易: {stats.get('today_trades', 0)}")
                print(f"   总盈亏: ${stats.get('total_profit', 0):.2f}")
                print(f"   胜率: {stats.get('win_rate', 0):.1f}%")
                return True
            else:
                print(f"❌ 获取交易统计失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 交易统计请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查交易统计异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 测试AI推理交易手动下单功能")
    print("=" * 80)
    
    print("📋 测试目标:")
    print("• 验证手动下单前置条件")
    print("• 测试手动BUY/SELL下单功能")
    print("• 验证交易参数与AI自动交易一致")
    print("• 确认移动止损等动态监控生效")
    print("• 检查交易统计更新")
    print()
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 检查前置条件
    prerequisites_ok, model_info = test_manual_trading_prerequisites(session)
    if not prerequisites_ok:
        print("\n❌ 前置条件不满足，无法进行手动下单测试")
        print("💡 请确保:")
        print("   1. MT5已连接")
        print("   2. 已选择交易模型并启动自动交易")
        print("   3. 市场数据正常")
        return
    
    # 3. 测试移动止损监控
    monitoring_ok = test_trailing_stop_monitoring()
    
    # 4. 测试手动BUY下单
    buy_success, buy_result = test_manual_trade_execution(session, model_info, 'BUY')
    
    # 等待一下
    if buy_success:
        print("\n⏱️ 等待3秒后测试SELL下单...")
        time.sleep(3)
        
        # 5. 测试手动SELL下单
        sell_success, sell_result = test_manual_trade_execution(session, model_info, 'SELL')
    else:
        sell_success = False
    
    # 6. 检查交易统计
    if buy_success or sell_success:
        print("\n⏱️ 等待2秒后检查交易统计...")
        time.sleep(2)
        stats_ok = test_trading_statistics(session)
    else:
        stats_ok = False
    
    # 7. 总结
    print(f"\n📊 测试结果总结")
    print("=" * 80)
    
    tests = [
        ("前置条件检查", "✅" if prerequisites_ok else "❌"),
        ("移动止损监控", "✅" if monitoring_ok else "❌"),
        ("手动BUY下单", "✅" if buy_success else "❌"),
        ("手动SELL下单", "✅" if sell_success else "❌"),
        ("交易统计更新", "✅" if stats_ok else "❌")
    ]
    
    for test_name, status in tests:
        print(f"   {test_name}: {status}")
    
    success_count = sum(1 for _, status in tests if status == "✅")
    total_count = len(tests)
    
    if success_count == total_count:
        print("\n🎉 手动下单功能测试完全成功!")
        print("💡 功能特点:")
        print("   ✅ 使用与AI自动交易相同的参数配置")
        print("   ✅ 支持移动止损等动态监控")
        print("   ✅ 正确更新交易统计")
        print("   ✅ 完整的错误处理和状态反馈")
        print("\n🚀 现在可以在AI推理交易页面使用手动下单功能了!")
    else:
        print(f"\n⚠️ 测试部分成功 ({success_count}/{total_count})")
        print("💡 建议检查失败的测试项目")
    
    return 0

if __name__ == "__main__":
    main()
