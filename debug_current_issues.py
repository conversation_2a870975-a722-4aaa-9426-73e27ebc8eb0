#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试当前问题
"""

import requests
import json

def debug_current_issues():
    """调试当前问题"""
    print("🔍 调试当前AI推理交易问题")
    print("=" * 50)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
        
        print("✅ 登录成功")
        
        # 获取AI推理交易页面
        inference_response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if inference_response.status_code != 200:
            print(f"❌ 页面访问失败: {inference_response.status_code}")
            return
        
        content = inference_response.text
        print(f"✅ 页面访问成功，大小: {len(content):,} 字符")
        
        # 保存页面内容用于分析
        with open('current_inference_page.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("💾 页面内容已保存到: current_inference_page.html")
        
        # 检查关键元素
        print(f"\n🔍 检查关键元素:")
        
        elements_to_check = [
            ('交易模型选择', 'id="tradingModelSelect"'),
            ('MT5连接状态', 'id="mt5ConnectionStatus"'),
            ('检查连接按钮', 'onclick="checkMT5Connection()"'),
            ('自动连接按钮', 'onclick="autoConnectMT5()"'),
            ('开始交易按钮', 'id="startTradingBtn"'),
            ('增强特征选项', 'id="enableEnhancedFeatures"'),
            ('loadTradingModels函数', 'function loadTradingModels'),
            ('checkMT5Connection函数', 'function checkMT5Connection'),
            ('JavaScript初始化', 'DOMContentLoaded')
        ]
        
        for name, element in elements_to_check:
            if element in content:
                print(f"✅ {name}: 存在")
            else:
                print(f"❌ {name}: 缺失")
        
        # 测试API端点
        print(f"\n🔍 测试API端点:")
        
        # 1. 测试模型列表API
        try:
            models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
            if models_response.status_code == 200:
                models_data = models_response.json()
                if models_data.get('success'):
                    models = models_data.get('models', [])
                    completed_models = [m for m in models if m.get('status') == 'completed']
                    print(f"✅ 模型列表API: 成功，{len(models)} 个模型，{len(completed_models)} 个已完成")
                    
                    # 显示模型详情
                    if completed_models:
                        print("   已完成的模型:")
                        for model in completed_models[:3]:  # 只显示前3个
                            print(f"     - {model.get('name')} ({model.get('model_type')}, {model.get('symbol')})")
                    else:
                        print("   ⚠️ 没有已完成的模型")
                else:
                    print(f"❌ 模型列表API: 错误 - {models_data.get('error')}")
            else:
                print(f"❌ 模型列表API: HTTP {models_response.status_code}")
        except Exception as e:
            print(f"❌ 模型列表API: 异常 - {e}")
        
        # 2. 测试MT5连接状态API
        try:
            mt5_response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
            if mt5_response.status_code == 200:
                mt5_data = mt5_response.json()
                connected = mt5_data.get('connected', False)
                print(f"✅ MT5连接API: 成功，连接状态: {connected}")
                if not connected:
                    print(f"   连接错误: {mt5_data.get('error', '未知错误')}")
            else:
                print(f"❌ MT5连接API: HTTP {mt5_response.status_code}")
        except Exception as e:
            print(f"❌ MT5连接API: 异常 - {e}")
        
        # 3. 检查浏览器控制台可能的错误
        print(f"\n🔍 检查可能的JavaScript错误:")
        
        js_error_patterns = [
            'null.addEventListener',
            'undefined.addEventListener',
            'Cannot read properties of undefined',
            'Cannot read properties of null',
            'ReferenceError',
            'TypeError'
        ]
        
        found_errors = []
        for pattern in js_error_patterns:
            if pattern in content:
                found_errors.append(pattern)
        
        if found_errors:
            print(f"⚠️ 发现可能的JavaScript错误模式: {found_errors}")
        else:
            print("✅ 未发现明显的JavaScript错误模式")
        
        # 4. 检查页面结构完整性
        print(f"\n🔍 检查页面结构:")
        
        div_count = content.count('<div')
        div_close_count = content.count('</div>')
        script_count = content.count('<script')
        
        print(f"   • div标签: {div_count} 开始, {div_close_count} 结束")
        print(f"   • script标签: {script_count} 个")
        
        if div_count == div_close_count:
            print("✅ div标签匹配")
        else:
            print(f"⚠️ div标签不匹配，差异: {div_count - div_close_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    debug_current_issues()
