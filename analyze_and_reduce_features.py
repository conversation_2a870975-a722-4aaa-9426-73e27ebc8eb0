#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析并大幅缩减增强特征数量
从52个减少到10-25个最核心的指标
"""

def analyze_current_features():
    """分析当前的52个增强特征"""
    
    current_features = {
        'bollinger_bands': [
            'bb_percent_b', 'bb_band_width', 'bb_squeeze', 'bb_breakout',
            'bb_distance_from_middle', 'bb_percent_b_change', 'bb_band_width_change'
        ],
        'atr_volatility': [
            'atr_atr', 'atr_ratio', 'atr_percentile', 'atr_change',
            'atr_low_volatility', 'atr_high_volatility'
        ],
        'stochastic': [
            'stoch_stoch_k', 'stoch_stoch_d', 'stoch_k_d_diff', 'stoch_k_change',
            'stoch_overbought', 'stoch_oversold', 'stoch_golden_cross', 'stoch_death_cross'
        ],
        'combined_signals': [
            'combined_squeeze_low_vol', 'combined_breakout_confirmed',
            'combined_oversold_confirmed', 'combined_overbought_confirmed',
            'combined_bullish_confluence', 'combined_bearish_confluence',
            'combined_breakout_setup'
        ],
        'market_state': [
            'market_trend_strength', 'market_trending_market', 'market_trend_direction'
        ],
        'price_derived': [
            'close_returns', 'close_position', 'price_gap'
        ],
        'momentum': [
            'momentum_5', 'momentum_10', 'momentum_20', 'trend_strength'
        ]
    }
    
    print("📊 当前52个增强特征分析:")
    total_count = 0
    for category, features in current_features.items():
        count = len(features)
        total_count += count
        print(f"   {category}: {count}个特征")
        for feature in features:
            print(f"     - {feature}")
    
    print(f"\n总计: {total_count}个特征")
    return current_features

def create_reduced_feature_sets():
    """创建缩减后的特征集"""
    
    # 精简特征集 (15个核心特征)
    core_features = {
        'name': '核心特征集',
        'count': 15,
        'description': '最重要的15个技术指标，平衡效果和效率',
        'features': [
            # 布林带核心 (3个) - 最重要的趋势和波动指标
            'bb_percent_b',        # 价格在布林带中的位置 (0-1)
            'bb_band_width',       # 布林带宽度 (波动性指标)
            'bb_squeeze',          # 布林带收缩 (突破前兆)
            
            # ATR波动率核心 (2个) - 市场波动性
            'atr_atr',            # 平均真实波幅
            'atr_ratio',          # ATR比率 (当前波动vs历史波动)
            
            # 随机指标核心 (3个) - 超买超卖
            'stoch_stoch_k',      # 随机指标K值
            'stoch_overbought',   # 超买信号
            'stoch_oversold',     # 超卖信号
            
            # 组合信号核心 (3个) - 多指标确认
            'combined_squeeze_low_vol',     # 低波动挤压信号
            'combined_breakout_confirmed',  # 突破确认信号
            'combined_bullish_confluence',  # 多头汇合信号
            
            # 市场状态 (2个) - 趋势强度
            'market_trend_strength',        # 趋势强度
            'market_trending_market',       # 是否处于趋势市场
            
            # 价格动量 (2个) - 价格变化
            'close_returns',               # 收益率
            'momentum_20'                  # 20期动量
        ]
    }
    
    # 扩展特征集 (25个特征)
    extended_features = {
        'name': '扩展特征集',
        'count': 25,
        'description': '25个精选技术指标，提供更全面的市场信息',
        'features': core_features['features'] + [
            # 额外布林带特征 (2个)
            'bb_breakout',                 # 布林带突破
            'bb_distance_from_middle',     # 距离中轨的距离
            
            # 额外ATR特征 (2个)
            'atr_low_volatility',          # 低波动状态
            'atr_high_volatility',         # 高波动状态
            
            # 额外随机指标 (2个)
            'stoch_stoch_d',              # 随机指标D值
            'stoch_k_d_diff',             # K-D差值
            
            # 额外组合信号 (2个)
            'combined_bearish_confluence', # 空头汇合信号
            'combined_oversold_confirmed', # 超卖确认信号
            
            # 额外市场状态 (1个)
            'market_trend_direction',      # 趋势方向
            
            # 额外动量特征 (1个)
            'trend_strength'               # 趋势强度指标
        ]
    }
    
    # 最小特征集 (10个特征)
    minimal_features = {
        'name': '最小特征集',
        'count': 10,
        'description': '10个最核心的技术指标，快速训练',
        'features': [
            # 布林带 (2个)
            'bb_percent_b',
            'bb_band_width',
            
            # ATR (1个)
            'atr_atr',
            
            # 随机指标 (2个)
            'stoch_stoch_k',
            'stoch_overbought',
            
            # 组合信号 (2个)
            'combined_squeeze_low_vol',
            'combined_breakout_confirmed',
            
            # 市场状态 (1个)
            'market_trend_strength',
            
            # 动量 (2个)
            'close_returns',
            'momentum_20'
        ]
    }
    
    return {
        'minimal': minimal_features,
        'core': core_features,
        'extended': extended_features
    }

def analyze_feature_importance():
    """分析特征重要性和选择理由"""
    
    feature_analysis = {
        # 布林带特征 - 趋势和波动性
        'bb_percent_b': {
            'importance': 'very_high',
            'reason': '价格在布林带中的位置，直接反映超买超卖状态',
            'category': 'trend_volatility'
        },
        'bb_band_width': {
            'importance': 'high',
            'reason': '布林带宽度反映市场波动性，窄带预示突破',
            'category': 'volatility'
        },
        'bb_squeeze': {
            'importance': 'high',
            'reason': '布林带收缩是重要的突破前兆信号',
            'category': 'breakout_signal'
        },
        
        # ATR特征 - 波动性
        'atr_atr': {
            'importance': 'very_high',
            'reason': '平均真实波幅，衡量市场波动性的标准指标',
            'category': 'volatility'
        },
        'atr_ratio': {
            'importance': 'medium',
            'reason': '当前波动与历史波动的比较',
            'category': 'volatility'
        },
        
        # 随机指标 - 超买超卖
        'stoch_stoch_k': {
            'importance': 'very_high',
            'reason': '随机指标K值，经典的超买超卖指标',
            'category': 'overbought_oversold'
        },
        'stoch_overbought': {
            'importance': 'high',
            'reason': '明确的超买信号，便于模型理解',
            'category': 'signal'
        },
        'stoch_oversold': {
            'importance': 'high',
            'reason': '明确的超卖信号，便于模型理解',
            'category': 'signal'
        },
        
        # 组合信号 - 多指标确认
        'combined_squeeze_low_vol': {
            'importance': 'high',
            'reason': '低波动挤压，强烈的突破前兆',
            'category': 'breakout_signal'
        },
        'combined_breakout_confirmed': {
            'importance': 'very_high',
            'reason': '多指标确认的突破信号，可靠性高',
            'category': 'confirmed_signal'
        },
        'combined_bullish_confluence': {
            'importance': 'high',
            'reason': '多个看涨指标的汇合，强烈买入信号',
            'category': 'confluence_signal'
        },
        
        # 市场状态
        'market_trend_strength': {
            'importance': 'very_high',
            'reason': '趋势强度，帮助模型判断市场状态',
            'category': 'market_state'
        },
        'market_trending_market': {
            'importance': 'high',
            'reason': '是否处于趋势市场，影响交易策略',
            'category': 'market_state'
        },
        
        # 价格动量
        'close_returns': {
            'importance': 'very_high',
            'reason': '价格收益率，最直接的价格变化指标',
            'category': 'momentum'
        },
        'momentum_20': {
            'importance': 'high',
            'reason': '20期动量，中期价格趋势指标',
            'category': 'momentum'
        }
    }
    
    return feature_analysis

def create_implementation_plan():
    """创建实施计划"""
    
    plan = {
        'phase_1_analysis': {
            'status': '✅ 已完成',
            'description': '分析当前52个特征，识别最重要的指标'
        },
        'phase_2_reduction': {
            'status': '🔄 进行中',
            'description': '设计3个精简特征集：10个、15个、25个',
            'deliverables': [
                '最小特征集 (10个)',
                '核心特征集 (15个)',
                '扩展特征集 (25个)'
            ]
        },
        'phase_3_implementation': {
            'status': '⏳ 待执行',
            'description': '修改增强特征工程代码',
            'tasks': [
                '更新get_recommended_features()方法',
                '添加get_core_features()方法',
                '添加get_minimal_features()方法',
                '更新前端配置选项'
            ]
        },
        'phase_4_testing': {
            'status': '⏳ 待执行',
            'description': '测试精简特征集的效果',
            'tasks': [
                '对比训练时间',
                '对比预测准确性',
                '验证特征重要性',
                '用户体验测试'
            ]
        }
    }
    
    return plan

def main():
    """主函数"""
    print('🔧 增强特征大幅缩减分析')
    print('=' * 80)
    
    # 分析当前特征
    current_features = analyze_current_features()
    
    # 创建精简特征集
    reduced_sets = create_reduced_feature_sets()
    
    print(f"\n🎯 精简特征集设计:")
    for key, feature_set in reduced_sets.items():
        print(f"\n📊 {feature_set['name']} ({feature_set['count']}个):")
        print(f"   描述: {feature_set['description']}")
        print(f"   特征列表:")
        for i, feature in enumerate(feature_set['features'], 1):
            print(f"     {i:2d}. {feature}")
    
    # 分析特征重要性
    importance = analyze_feature_importance()
    
    print(f"\n💡 特征重要性分析:")
    very_high = [f for f, info in importance.items() if info['importance'] == 'very_high']
    high = [f for f, info in importance.items() if info['importance'] == 'high']
    
    print(f"   🔴 极高重要性 ({len(very_high)}个): {', '.join(very_high)}")
    print(f"   🟡 高重要性 ({len(high)}个): {', '.join(high)}")
    
    # 对比分析
    print(f"\n📈 缩减效果对比:")
    print(f"   原始特征数: 52个")
    print(f"   最小特征集: 10个 (缩减80.8%)")
    print(f"   核心特征集: 15个 (缩减71.2%)")
    print(f"   扩展特征集: 25个 (缩减51.9%)")
    
    # 实施计划
    plan = create_implementation_plan()
    print(f"\n📋 实施计划:")
    for phase, details in plan.items():
        print(f"   {details['status']} {phase}: {details['description']}")
    
    print(f"\n✅ 分析完成！")
    print(f"💡 建议:")
    print(f"   - 默认使用核心特征集 (15个)")
    print(f"   - 快速训练使用最小特征集 (10个)")
    print(f"   - 高精度需求使用扩展特征集 (25个)")
    print(f"   - 大幅提升训练速度和模型稳定性")

if __name__ == "__main__":
    main()
