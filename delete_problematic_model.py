#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除有问题的深度学习模型
删除模型ID: 9c9a18ec-1bad-480c-8032-2026ee5f660f (xau-0802-cnn混合-增强-2Y-10技术-高精度)
"""

import os
import sys
import sqlite3
import json
from datetime import datetime

def delete_problematic_model():
    """删除有问题的模型"""
    
    print("🗑️ 删除有问题的深度学习模型")
    print("=" * 60)
    
    # 有问题的模型ID
    problematic_model_id = "9c9a18ec-1bad-480c-8032-2026ee5f660f"
    model_name = "xau-0802-cnn混合-增强-2Y-10技术-高精度"
    
    print(f"📋 目标模型:")
    print(f"   ID: {problematic_model_id}")
    print(f"   名称: {model_name}")
    print(f"   问题: 基于错误推理逻辑的虚假高准确率")
    
    # 确认删除
    confirm = input(f"\n⚠️ 确定要删除这个模型吗？(输入 'YES' 确认): ")
    if confirm != 'YES':
        print("❌ 操作已取消")
        return False
    
    try:
        # 连接数据库
        db_path = 'trading_system.db'
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 获取模型信息
        print(f"\n🔍 查询模型信息...")
        cursor.execute('''
            SELECT id, name, model_type, symbol, timeframe, status, model_path, 
                   performance_metrics, created_at, completed_at
            FROM deep_learning_models
            WHERE id = ?
        ''', (problematic_model_id,))
        
        model_info = cursor.fetchone()
        if not model_info:
            print(f"❌ 模型不存在: {problematic_model_id}")
            conn.close()
            return False
        
        print(f"✅ 找到模型:")
        print(f"   名称: {model_info[1]}")
        print(f"   类型: {model_info[2]}")
        print(f"   品种: {model_info[3]}")
        print(f"   时间框架: {model_info[4]}")
        print(f"   状态: {model_info[5]}")
        print(f"   模型文件: {model_info[6]}")
        
        # 解析性能指标
        if model_info[7]:
            try:
                performance = json.loads(model_info[7])
                accuracy = performance.get('accuracy', 0) * 100
                print(f"   准确率: {accuracy:.2f}% (虚假高准确率)")
            except:
                print(f"   准确率: 无法解析")
        
        # 2. 删除模型文件
        model_path = model_info[6]
        if model_path and os.path.exists(model_path):
            print(f"\n🗑️ 删除模型文件: {model_path}")
            try:
                os.remove(model_path)
                print(f"✅ 模型文件删除成功")
            except Exception as e:
                print(f"⚠️ 删除模型文件失败: {e}")
        else:
            print(f"⚠️ 模型文件不存在或路径为空: {model_path}")
        
        # 3. 删除数据库记录
        print(f"\n🗑️ 删除数据库记录...")
        
        # 删除训练任务记录
        cursor.execute('''
            DELETE FROM training_tasks
            WHERE model_id = ?
        ''', (problematic_model_id,))
        training_tasks_deleted = cursor.rowcount
        print(f"✅ 删除训练任务记录: {training_tasks_deleted} 条")
        
        # 删除模型记录
        cursor.execute('''
            DELETE FROM deep_learning_models
            WHERE id = ?
        ''', (problematic_model_id,))
        models_deleted = cursor.rowcount
        print(f"✅ 删除模型记录: {models_deleted} 条")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n✅ 模型删除完成!")
        print(f"📋 删除总结:")
        print(f"   - 模型文件: {'已删除' if model_path and os.path.exists(model_path) else '已删除或不存在'}")
        print(f"   - 数据库记录: {models_deleted} 条")
        print(f"   - 训练任务: {training_tasks_deleted} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ 删除过程中出现错误: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def verify_deletion():
    """验证删除结果"""
    print(f"\n🔍 验证删除结果...")
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查模型是否还存在
        cursor.execute('''
            SELECT COUNT(*) FROM deep_learning_models
            WHERE id = ?
        ''', ("9c9a18ec-1bad-480c-8032-2026ee5f660f",))
        
        count = cursor.fetchone()[0]
        if count == 0:
            print(f"✅ 验证成功: 模型已从数据库中删除")
        else:
            print(f"❌ 验证失败: 模型仍存在于数据库中")
        
        # 检查剩余模型数量
        cursor.execute('SELECT COUNT(*) FROM deep_learning_models')
        total_models = cursor.fetchone()[0]
        print(f"📊 剩余模型数量: {total_models}")
        
        conn.close()
        return count == 0
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始删除有问题的深度学习模型")
    
    # 删除模型
    if delete_problematic_model():
        # 验证删除
        if verify_deletion():
            print(f"\n🎉 模型删除成功!")
            print(f"💡 建议:")
            print(f"   1. 重新训练一个新的增强特征模型")
            print(f"   2. 使用修复后的推理逻辑")
            print(f"   3. 关注真实的性能指标，而不是虚假的高准确率")
        else:
            print(f"\n⚠️ 删除可能不完整，请检查")
    else:
        print(f"\n❌ 模型删除失败")

if __name__ == "__main__":
    main()
