#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度诊断MT5数据不一致问题
分析为什么不同的查询方式返回不同的持仓数据
"""

import time
from datetime import datetime

def diagnose_mt5_connection_layers():
    """诊断MT5连接的不同层次"""
    print("🔍 诊断MT5连接的不同层次")
    print("=" * 60)
    
    try:
        import MetaTrader5 as mt5
        from services.mt5_service import mt5_service
        
        # 1. 检查MT5库层面
        print("1. MT5库层面检查")
        
        # 重新初始化MT5库
        mt5.shutdown()
        time.sleep(1)
        
        if mt5.initialize():
            print("   ✅ MT5库重新初始化成功")
            
            # 获取账户信息
            account_info = mt5.account_info()
            if account_info:
                print(f"   ✅ 账户: {account_info.login} ({account_info.server})")
                print(f"   余额: ${account_info.balance:.2f}")
                print(f"   净值: ${account_info.equity:.2f}")
            
            # 获取终端信息
            terminal_info = mt5.terminal_info()
            if terminal_info:
                print(f"   终端连接: {'✅ 是' if terminal_info.connected else '❌ 否'}")
                print(f"   交易允许: {'✅ 是' if terminal_info.trade_allowed else '❌ 否'}")
            
            # 直接查询持仓
            positions = mt5.positions_get()
            print(f"   直接查询持仓: {len(positions) if positions else 0} 个")
            
            if positions:
                for i, pos in enumerate(positions):
                    print(f"   持仓 {i+1}: {pos.symbol} 票号{pos.ticket} {pos.volume}手 魔术号{pos.magic}")
        else:
            print("   ❌ MT5库初始化失败")
            return False
        
        # 2. 检查MT5服务层面
        print("\n2. MT5服务层面检查")
        print(f"   服务连接状态: {mt5_service.connected}")
        
        # 强制重新连接服务
        mt5_service.disconnect()
        time.sleep(1)
        
        if mt5_service.connect():
            print("   ✅ MT5服务重新连接成功")
            
            # 通过服务查询持仓
            service_positions = mt5_service.get_positions()
            print(f"   服务查询持仓: {len(service_positions) if service_positions else 0} 个")
            
            if service_positions:
                for i, pos in enumerate(service_positions):
                    print(f"   持仓 {i+1}: {pos['symbol']} 票号{pos['ticket']} {pos['volume']}手 魔术号{pos.get('magic', 'Unknown')}")
        else:
            print("   ❌ MT5服务重新连接失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断MT5连接层次异常: {e}")
        return False

def check_mt5_position_filters():
    """检查MT5持仓过滤逻辑"""
    print("\n🔍 检查MT5持仓过滤逻辑")
    print("=" * 60)
    
    try:
        import MetaTrader5 as mt5
        from services.mt5_service import mt5_service
        
        # 1. 获取所有持仓（无过滤）
        print("1. 获取所有持仓（无过滤）")
        all_positions = mt5.positions_get()
        if all_positions:
            print(f"   总持仓数: {len(all_positions)}")
            for pos in all_positions:
                print(f"   持仓: {pos.symbol} 票号{pos.ticket} 魔术号{pos.magic} 注释'{pos.comment}'")
        else:
            print("   无持仓或查询失败")
        
        # 2. 检查不同魔术号的持仓
        print("\n2. 按魔术号分类")
        magic_groups = {}
        if all_positions:
            for pos in all_positions:
                magic = pos.magic
                if magic not in magic_groups:
                    magic_groups[magic] = []
                magic_groups[magic].append(pos)
            
            for magic, positions in magic_groups.items():
                print(f"   魔术号 {magic}: {len(positions)} 个持仓")
                for pos in positions:
                    print(f"     - {pos.symbol} 票号{pos.ticket} 注释'{pos.comment}'")
        
        # 3. 检查服务的get_positions方法
        print("\n3. 检查服务的get_positions方法")
        service_positions = mt5_service.get_positions()
        if service_positions:
            print(f"   服务返回: {len(service_positions)} 个持仓")
            for pos in service_positions:
                print(f"   持仓: {pos['symbol']} 票号{pos['ticket']} 魔术号{pos.get('magic', 'Unknown')}")
        else:
            print("   服务返回: 无持仓")
        
        # 4. 检查get_demo_positions方法
        print("\n4. 检查get_demo_positions方法")
        demo_positions = mt5_service.get_demo_positions()
        if demo_positions:
            print(f"   模拟持仓: {len(demo_positions)} 个")
            for pos in demo_positions:
                print(f"   持仓: {pos['symbol']} 票号{pos['ticket']} 魔术号{pos.get('magic', 'Unknown')}")
        else:
            print("   模拟持仓: 无持仓")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查持仓过滤逻辑异常: {e}")
        return False

def test_order_execution_detailed():
    """详细测试订单执行过程"""
    print("\n🧪 详细测试订单执行过程")
    print("=" * 60)
    
    try:
        from services.mt5_service import mt5_service
        import MetaTrader5 as mt5
        
        # 1. 执行前检查
        print("1. 执行前检查")
        pre_positions = mt5.positions_get()
        pre_count = len(pre_positions) if pre_positions else 0
        print(f"   执行前持仓数: {pre_count}")
        
        # 2. 执行测试订单
        print("\n2. 执行测试订单")
        test_params = {
            'symbol': 'XAUUSD',
            'order_type': 'buy',
            'lot_size': 0.01,
            'comment': '深度诊断测试订单'
        }
        
        print(f"   参数: {test_params}")
        
        result = mt5_service.place_order(
            symbol=test_params['symbol'],
            order_type=test_params['order_type'],
            lot_size=test_params['lot_size'],
            comment=test_params['comment']
        )
        
        if result and result.get('success'):
            order_id = result.get('order_id')
            print(f"   ✅ 订单执行成功: 订单ID {order_id}")
            
            # 3. 执行后立即检查
            print("\n3. 执行后立即检查")
            time.sleep(1)  # 等待1秒
            
            # 直接查询
            post_positions = mt5.positions_get()
            post_count = len(post_positions) if post_positions else 0
            print(f"   执行后持仓数: {post_count}")
            
            # 查找新订单
            found_order = False
            if post_positions:
                for pos in post_positions:
                    if pos.ticket == order_id:
                        print(f"   ✅ 找到新订单: 票号{pos.ticket} 品种{pos.symbol} 魔术号{pos.magic}")
                        found_order = True
                        
                        # 立即平仓
                        print(f"\n4. 立即平仓测试订单")
                        close_result = mt5_service.close_position(pos.ticket)
                        if close_result and close_result.get('success'):
                            print(f"   ✅ 测试订单已平仓")
                        else:
                            print(f"   ❌ 平仓失败: {close_result.get('error') if close_result else '未知错误'}")
                        break
            
            if not found_order:
                print(f"   ❌ 未找到新订单 {order_id}")
                
                # 检查历史记录
                print(f"\n4. 检查历史记录")
                from datetime import datetime, timedelta
                end_time = datetime.now()
                start_time = end_time - timedelta(minutes=5)
                
                deals = mt5.history_deals_get(start_time, end_time)
                if deals:
                    for deal in deals:
                        if deal.order == order_id or deal.position == order_id:
                            print(f"   ✅ 在历史记录中找到: 订单{deal.order} 持仓{deal.position}")
                            break
                    else:
                        print(f"   ❌ 历史记录中也未找到")
                else:
                    print(f"   ❌ 无历史记录")
            
            return found_order
        else:
            error_msg = result.get('error') if result else '未知错误'
            print(f"   ❌ 订单执行失败: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ 详细测试订单执行异常: {e}")
        return False

def analyze_api_responses():
    """分析API响应差异"""
    print("\n📊 分析API响应差异")
    print("=" * 60)
    
    try:
        import requests
        
        # 登录
        session = requests.Session()
        login_data = {'username': 'admin', 'password': 'admin123'}
        session.post('http://127.0.0.1:5000/login', data=login_data)
        
        apis = [
            ('/api/mt5/positions', 'MT5持仓API'),
            ('/api/deep-learning/trading-statistics', '交易统计API'),
            ('/api/deep-learning/position-details', '持仓详情API'),
        ]
        
        for endpoint, name in apis:
            print(f"\n{name} ({endpoint})")
            try:
                response = session.get(f'http://127.0.0.1:5000{endpoint}')
                if response.status_code == 200:
                    result = response.json()
                    print(f"   状态: ✅ 成功")
                    
                    if endpoint == '/api/mt5/positions':
                        positions = result.get('positions', [])
                        print(f"   持仓数: {len(positions)}")
                        for i, pos in enumerate(positions[:3]):
                            print(f"   持仓 {i+1}: {pos.get('symbol')} 票号{pos.get('ticket')}")
                    
                    elif endpoint == '/api/deep-learning/trading-statistics':
                        if result.get('success'):
                            stats = result.get('statistics', {})
                            print(f"   当前持仓: {stats.get('current_positions', 0)}")
                        else:
                            print(f"   错误: {result.get('error')}")
                    
                    elif endpoint == '/api/deep-learning/position-details':
                        if result.get('success'):
                            positions = result.get('positions', [])
                            print(f"   持仓数: {len(positions)}")
                        else:
                            print(f"   错误: {result.get('error')}")
                else:
                    print(f"   状态: ❌ HTTP {response.status_code}")
            except Exception as e:
                print(f"   状态: ❌ 异常 {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析API响应异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 深度诊断MT5数据不一致问题")
    print("=" * 80)
    
    print("📋 诊断目标:")
    print("1. 分析MT5连接的不同层次")
    print("2. 检查持仓过滤逻辑")
    print("3. 详细测试订单执行过程")
    print("4. 分析API响应差异")
    print()
    
    # 1. 诊断MT5连接层次
    connection_ok = diagnose_mt5_connection_layers()
    
    # 2. 检查持仓过滤逻辑
    if connection_ok:
        filter_ok = check_mt5_position_filters()
    else:
        filter_ok = False
    
    # 3. 详细测试订单执行
    if connection_ok:
        order_ok = test_order_execution_detailed()
    else:
        order_ok = False
    
    # 4. 分析API响应
    api_ok = analyze_api_responses()
    
    # 5. 总结诊断结果
    print(f"\n📊 深度诊断结果总结")
    print("=" * 80)
    
    print(f"MT5连接层次: {'✅ 正常' if connection_ok else '❌ 异常'}")
    print(f"持仓过滤逻辑: {'✅ 正常' if filter_ok else '❌ 异常'}")
    print(f"订单执行过程: {'✅ 正常' if order_ok else '❌ 异常'}")
    print(f"API响应分析: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if connection_ok and filter_ok and order_ok:
        print(f"\n🎉 深度诊断显示系统基本正常!")
        print("💡 可能的问题:")
        print("   • 前端缓存问题")
        print("   • API响应时间差异")
        print("   • 数据同步延迟")
    else:
        print(f"\n⚠️ 发现系统问题")
        print("💡 建议:")
        print("   • 重启MT5终端")
        print("   • 重启应用程序")
        print("   • 检查MT5设置")
    
    return 0

if __name__ == "__main__":
    main()
