
// 最终的AI推理交易功能测试脚本
console.log("🎯 开始最终功能测试...");

// 1. 检查页面是否有JavaScript错误
console.log("📋 检查JavaScript错误:");
if (window.onerror) {
    console.log("⚠️ 页面可能有JavaScript错误");
} else {
    console.log("✅ 未检测到JavaScript错误处理器");
}

// 2. 检查关键DOM元素
console.log("📋 检查关键DOM元素:");
const elements = {
    tradingModelSelect: document.getElementById('tradingModelSelect'),
    mt5ConnectionStatus: document.getElementById('mt5ConnectionStatus'),
    startTradingBtn: document.getElementById('startTradingBtn'),
    enableEnhancedFeatures: document.getElementById('enableEnhancedFeatures')
};

let allElementsExist = true;
for (const [name, element] of Object.entries(elements)) {
    if (element) {
        console.log(`✅ ${name}: 存在`);
    } else {
        console.log(`❌ ${name}: 缺失`);
        allElementsExist = false;
    }
}

// 3. 检查模型选择功能
console.log("📋 检查模型选择功能:");
const modelSelect = elements.tradingModelSelect;
if (modelSelect) {
    console.log(`模型选择选项数量: ${modelSelect.options.length}`);
    if (modelSelect.options.length > 1) {
        console.log("✅ 模型选择有选项");
        for (let i = 1; i < Math.min(modelSelect.options.length, 4); i++) {
            console.log(`  ${i}. ${modelSelect.options[i].text}`);
        }
    } else {
        console.log("❌ 模型选择没有选项，尝试手动加载...");
        if (typeof loadTradingModels === 'function') {
            loadTradingModels().then(() => {
                console.log("🔄 手动加载完成，重新检查选项数量:", modelSelect.options.length);
            });
        }
    }
}

// 4. 检查关键函数
console.log("📋 检查关键函数:");
const functions = ['loadTradingModels', 'checkMT5Connection', 'startAutoTrading', 'executeManualTrade'];
functions.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName}: 存在`);
    } else {
        console.log(`❌ ${funcName}: 缺失`);
    }
});

// 5. 测试MT5连接状态
console.log("📋 测试MT5连接状态:");
const mt5Status = elements.mt5ConnectionStatus;
if (mt5Status) {
    console.log(`当前MT5状态: ${mt5Status.textContent}`);
    console.log(`状态样式: ${mt5Status.className}`);
    
    // 手动检查连接
    if (typeof checkMT5Connection === 'function') {
        console.log("🔄 手动检查MT5连接...");
        checkMT5Connection();
    }
}

// 6. 总结测试结果
setTimeout(() => {
    console.log("🎯 测试总结:");
    console.log(`DOM元素完整性: ${allElementsExist ? '✅ 通过' : '❌ 失败'}`);
    console.log(`模型选择功能: ${modelSelect && modelSelect.options.length > 1 ? '✅ 正常' : '❌ 异常'}`);
    console.log(`关键函数可用性: ${functions.every(f => typeof window[f] === 'function') ? '✅ 正常' : '❌ 异常'}`);
    
    if (allElementsExist && modelSelect && modelSelect.options.length > 1) {
        console.log("🎉 所有功能测试通过！AI推理交易页面已修复完成！");
    } else {
        console.log("⚠️ 部分功能仍有问题，请检查上述详细信息");
    }
}, 2000);

console.log("✅ 测试脚本执行完成，请等待2秒查看总结结果");
