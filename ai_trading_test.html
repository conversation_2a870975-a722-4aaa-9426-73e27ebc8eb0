<!DOCTYPE html>
<html>
<head>
    <title>AI推理交易功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
    </style>
</head>
<body>
    <h1>AI推理交易功能测试</h1>
    
    <div class="test-section info">
        <h3>🔍 测试说明</h3>
        <p>此页面用于测试AI推理交易的关键功能是否正常工作。</p>
        <p>请访问: <a href="http://127.0.0.1:5000/deep-learning/inference" target="_blank">AI推理交易页面</a></p>
    </div>
    
    <div class="test-section">
        <h3>✅ 应该看到的功能</h3>
        <ul>
            <li><strong>MT5连接状态</strong>: 页面顶部应显示MT5连接状态和控制按钮</li>
            <li><strong>交易模型选择</strong>: 交易配置区域应有"交易模型"下拉框</li>
            <li><strong>模型列表</strong>: 下拉框中应显示已训练完成的模型</li>
            <li><strong>连接按钮</strong>: "检查连接"和"自动连接"按钮应该可以点击</li>
            <li><strong>增强特征</strong>: 交易配置中应有"增强特征"开关</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h3>🔧 故障排除</h3>
        <ol>
            <li><strong>如果模型列表为空</strong>:
                <ul>
                    <li>检查是否有训练完成的模型</li>
                    <li>打开浏览器控制台查看错误信息</li>
                    <li>检查API端点是否正常</li>
                </ul>
            </li>
            <li><strong>如果MT5连接失败</strong>:
                <ul>
                    <li>确保MT5软件已启动</li>
                    <li>检查MT5是否允许自动交易</li>
                    <li>检查网络连接</li>
                </ul>
            </li>
            <li><strong>如果页面功能异常</strong>:
                <ul>
                    <li>强制刷新页面 (Ctrl+F5)</li>
                    <li>清除浏览器缓存</li>
                    <li>检查浏览器控制台错误</li>
                </ul>
            </li>
        </ol>
    </div>
    
    <div class="test-section">
        <h3>📋 测试检查清单</h3>
        <p>请在AI推理交易页面上验证以下功能:</p>
        <ul>
            <li>□ 页面正常加载，无JavaScript错误</li>
            <li>□ MT5连接状态显示正常</li>
            <li>□ 交易模型下拉框有选项</li>
            <li>□ 点击"检查连接"按钮有响应</li>
            <li>□ 点击"自动连接"按钮有响应</li>
            <li>□ 选择模型后显示模型信息</li>
            <li>□ 增强特征开关存在且可操作</li>
            <li>□ 其他交易配置选项正常</li>
        </ul>
    </div>
    
    <script>
        // 简单的功能测试
        function testAPIs() {
            console.log('开始测试API端点...');
            
            // 测试模型列表API
            fetch('/api/deep-learning/models')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('✅ 模型列表API正常，模型数量:', data.models.length);
                    } else {
                        console.error('❌ 模型列表API错误:', data.error);
                    }
                })
                .catch(error => console.error('❌ 模型列表API异常:', error));
            
            // 测试MT5连接API
            fetch('/api/mt5/connection-status')
                .then(response => response.json())
                .then(data => {
                    console.log('✅ MT5连接状态API正常，连接状态:', data.connected);
                })
                .catch(error => console.error('❌ MT5连接API异常:', error));
        }
        
        // 页面加载后自动测试
        window.onload = function() {
            setTimeout(testAPIs, 1000);
        };
    </script>
</body>
</html>