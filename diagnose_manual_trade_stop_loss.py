#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断手动下单止损配置问题
检查为什么手动下单的止损特别小
"""

print("🔍 手动下单止损配置诊断")
print("=" * 80)

print("\n📋 问题描述:")
print("手动下单的止损怎么特别小，没有按照配置执行？")

print("\n🔍 问题分析:")
print("手动下单使用 getTradingConfig() 函数获取配置")
print("该函数从 'stopLossPips' 字段读取止损配置")

print("\n📊 配置字段映射:")
print("手动下单配置来源:")
print("• 止损: document.getElementById('stopLossPips').value")
print("• 止盈: document.getElementById('takeProfitPips').value")
print("• 手数: document.getElementById('tradingLotSize').value")
print("• 置信度: document.getElementById('minConfidence').value")

print("\n🎯 可能的问题:")
print("1. stopLossPips 字段值被设置得很小")
print("2. 页面上有多个止损配置字段，可能混淆了")
print("3. 默认配置没有正确应用")

print("\n📋 页面上的止损配置字段:")
print("1. stopLossPips - AI交易配置 (手动下单使用这个)")
print("2. inferenceStopLoss - AI推理配置")
print("3. backtestStopLoss - 回测配置")

print("\n🔧 解决方案:")

print("\n方案1: 检查当前配置值")
print("在浏览器控制台运行以下代码:")
print("```javascript")
print("// 检查当前止损配置")
print("const stopLossPips = document.getElementById('stopLossPips')?.value;")
print("const takeProfitPips = document.getElementById('takeProfitPips')?.value;")
print("const tradingLotSize = document.getElementById('tradingLotSize')?.value;")
print("const minConfidence = document.getElementById('minConfidence')?.value;")
print("")
print("console.log('当前AI交易配置:');")
print("console.log('止损:', stopLossPips, 'pips');")
print("console.log('止盈:', takeProfitPips, 'pips');")
print("console.log('手数:', tradingLotSize);")
print("console.log('置信度:', minConfidence);")
print("")
print("// 检查getTradingConfig()函数的返回值")
print("if (typeof getTradingConfig === 'function') {")
print("    const config = getTradingConfig();")
print("    console.log('getTradingConfig()返回:', config);")
print("} else {")
print("    console.log('getTradingConfig函数不存在');")
print("}")
print("```")

print("\n方案2: 修正止损配置")
print("在浏览器控制台运行以下代码:")
print("```javascript")
print("// 修正止损配置")
print("console.log('🔧 修正手动下单止损配置...');")
print("")
print("// 设置合理的止损值")
print("const stopLossElement = document.getElementById('stopLossPips');")
print("const takeProfitElement = document.getElementById('takeProfitPips');")
print("")
print("if (stopLossElement) {")
print("    const currentStopLoss = parseInt(stopLossElement.value);")
print("    console.log('当前止损:', currentStopLoss, 'pips');")
print("    ")
print("    if (currentStopLoss < 20) {")
print("        stopLossElement.value = '50';  // 设置为50 pips")
print("        console.log('✅ 止损已修正为50 pips');")
print("    } else {")
print("        console.log('✅ 止损配置正常:', currentStopLoss, 'pips');")
print("    }")
print("} else {")
print("    console.log('❌ 找不到stopLossPips元素');")
print("}")
print("")
print("if (takeProfitElement) {")
print("    const currentTakeProfit = parseInt(takeProfitElement.value);")
print("    console.log('当前止盈:', currentTakeProfit, 'pips');")
print("    ")
print("    if (currentTakeProfit < 30) {")
print("        takeProfitElement.value = '100';  // 设置为100 pips")
print("        console.log('✅ 止盈已修正为100 pips');")
print("    } else {")
print("        console.log('✅ 止盈配置正常:', currentTakeProfit, 'pips');")
print("    }")
print("} else {")
print("    console.log('❌ 找不到takeProfitPips元素');")
print("}")
print("")
print("// 验证修正后的配置")
print("if (typeof getTradingConfig === 'function') {")
print("    const newConfig = getTradingConfig();")
print("    console.log('修正后的配置:', newConfig);")
print("}")
print("```")

print("\n方案3: 应用预设配置")
print("在浏览器控制台运行以下代码:")
print("```javascript")
print("// 应用平衡型预设配置")
print("console.log('🔧 应用平衡型预设配置...');")
print("")
print("// 设置平衡型配置")
print("if (document.getElementById('stopLossPips')) {")
print("    document.getElementById('stopLossPips').value = '50';")
print("}")
print("if (document.getElementById('takeProfitPips')) {")
print("    document.getElementById('takeProfitPips').value = '100';")
print("}")
print("if (document.getElementById('tradingLotSize')) {")
print("    document.getElementById('tradingLotSize').value = '0.01';")
print("}")
print("if (document.getElementById('minConfidence')) {")
print("    document.getElementById('minConfidence').value = '0.3';")
print("}")
print("if (document.getElementById('maxPositions')) {")
print("    document.getElementById('maxPositions').value = '4';")
print("}")
print("")
print("console.log('✅ 平衡型配置已应用');")
print("")
print("// 验证配置")
print("if (typeof getTradingConfig === 'function') {")
print("    const config = getTradingConfig();")
print("    console.log('应用后的配置:', config);")
print("}")
print("```")

print("\n💡 推荐的止损配置:")
print("• 保守型: 30 pips")
print("• 平衡型: 50 pips")
print("• 激进型: 80 pips")

print("\n⚠️ 注意事项:")
print("1. 止损太小(< 20 pips)容易被市场噪音触发")
print("2. 止损太大(> 200 pips)风险过高")
print("3. 建议根据品种的平均波动设置合理止损")

print("\n🎯 XAUUSD(黄金)推荐配置:")
print("• 止损: 50-100 pips (黄金波动较大)")
print("• 止盈: 100-200 pips")
print("• 手数: 0.01 (小手数降低风险)")

print("\n📊 验证步骤:")
print("1. 运行方案1检查当前配置")
print("2. 如果止损过小，运行方案2或方案3修正")
print("3. 重新执行手动下单测试")
print("4. 检查MT5中的实际止损价格")

print("\n🔍 调试信息:")
print("手动下单执行流程:")
print("1. executeManualTrade(action) 被调用")
print("2. 调用 getTradingConfig() 获取配置")
print("3. 从 stopLossPips 字段读取止损值")
print("4. 构建 tradeData 对象")
print("5. 发送到 /api/deep-learning/execute-trade")

print("\n🎉 总结:")
print("手动下单止损小的问题很可能是 stopLossPips 字段值设置过小")
print("请使用上面的方案检查和修正配置")
