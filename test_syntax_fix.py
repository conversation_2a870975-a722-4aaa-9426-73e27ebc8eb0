#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JavaScript语法错误修复
"""

import requests
import time

def test_syntax_fix():
    """测试JavaScript语法错误修复"""
    print("🧪 测试JavaScript语法错误修复")
    print("=" * 50)
    
    # 等待应用启动
    time.sleep(5)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 获取AI推理交易页面
        print(f"\n🔍 获取AI推理交易页面...")
        inference_response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if inference_response.status_code != 200:
            print(f"❌ 页面访问失败: {inference_response.status_code}")
            return False
        
        content = inference_response.text
        print(f"✅ 页面访问成功，大小: {len(content):,} 字符")
        
        # 检查语法错误修复
        print(f"\n🔍 检查语法错误修复:")
        
        # 1. 检查null.value错误是否已修复
        if 'null.value' in content:
            print("❌ null.value语法错误仍然存在")
            return False
        else:
            print("✅ null.value语法错误已修复")
        
        # 2. 检查null.addEventListener错误是否已修复
        if 'null.addEventListener' in content:
            print("❌ null.addEventListener语法错误仍然存在")
            return False
        else:
            print("✅ null.addEventListener语法错误已修复")
        
        # 3. 检查null按钮操作错误是否已修复
        if 'const startBtn = null;' in content and 'startBtn.disabled' in content:
            print("❌ null按钮操作语法错误仍然存在")
            return False
        else:
            print("✅ null按钮操作语法错误已修复")
        
        # 4. 检查关键功能是否正常
        print(f"\n🔍 检查关键功能:")
        
        # 检查交易模型选择
        if 'id="tradingModelSelect"' in content:
            print("✅ 交易模型选择元素存在")
        else:
            print("❌ 交易模型选择元素缺失")
        
        # 检查loadTradingModels函数
        if 'function loadTradingModels()' in content:
            print("✅ loadTradingModels函数存在")
        else:
            print("❌ loadTradingModels函数缺失")
        
        # 检查MT5连接功能
        if 'function checkMT5Connection()' in content:
            print("✅ checkMT5Connection函数存在")
        else:
            print("❌ checkMT5Connection函数缺失")
        
        # 保存修复后的页面内容
        with open('syntax_fixed_page.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("💾 修复后页面内容已保存到: syntax_fixed_page.html")
        
        # 测试API端点
        print(f"\n🔍 测试API端点:")
        
        # 测试模型列表API
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        if models_response.status_code == 200:
            models_data = models_response.json()
            if models_data.get('success'):
                completed_models = [m for m in models_data.get('models', []) if m.get('status') == 'completed']
                print(f"✅ 模型API正常: {len(completed_models)} 个已完成模型")
            else:
                print(f"❌ 模型API错误: {models_data.get('error')}")
        else:
            print(f"❌ 模型API失败: {models_response.status_code}")
        
        # 测试MT5连接API
        mt5_response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        if mt5_response.status_code == 200:
            mt5_data = mt5_response.json()
            print(f"✅ MT5连接API正常: 连接状态 = {mt5_data.get('connected', False)}")
        else:
            print(f"❌ MT5连接API失败: {mt5_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def create_final_browser_test():
    """创建最终的浏览器测试脚本"""
    print(f"\n🔧 创建最终的浏览器测试脚本...")
    
    test_script = '''
// 最终的AI推理交易功能测试脚本
console.log("🎯 开始最终功能测试...");

// 1. 检查页面是否有JavaScript错误
console.log("📋 检查JavaScript错误:");
if (window.onerror) {
    console.log("⚠️ 页面可能有JavaScript错误");
} else {
    console.log("✅ 未检测到JavaScript错误处理器");
}

// 2. 检查关键DOM元素
console.log("📋 检查关键DOM元素:");
const elements = {
    tradingModelSelect: document.getElementById('tradingModelSelect'),
    mt5ConnectionStatus: document.getElementById('mt5ConnectionStatus'),
    startTradingBtn: document.getElementById('startTradingBtn'),
    enableEnhancedFeatures: document.getElementById('enableEnhancedFeatures')
};

let allElementsExist = true;
for (const [name, element] of Object.entries(elements)) {
    if (element) {
        console.log(`✅ ${name}: 存在`);
    } else {
        console.log(`❌ ${name}: 缺失`);
        allElementsExist = false;
    }
}

// 3. 检查模型选择功能
console.log("📋 检查模型选择功能:");
const modelSelect = elements.tradingModelSelect;
if (modelSelect) {
    console.log(`模型选择选项数量: ${modelSelect.options.length}`);
    if (modelSelect.options.length > 1) {
        console.log("✅ 模型选择有选项");
        for (let i = 1; i < Math.min(modelSelect.options.length, 4); i++) {
            console.log(`  ${i}. ${modelSelect.options[i].text}`);
        }
    } else {
        console.log("❌ 模型选择没有选项，尝试手动加载...");
        if (typeof loadTradingModels === 'function') {
            loadTradingModels().then(() => {
                console.log("🔄 手动加载完成，重新检查选项数量:", modelSelect.options.length);
            });
        }
    }
}

// 4. 检查关键函数
console.log("📋 检查关键函数:");
const functions = ['loadTradingModels', 'checkMT5Connection', 'startAutoTrading', 'executeManualTrade'];
functions.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName}: 存在`);
    } else {
        console.log(`❌ ${funcName}: 缺失`);
    }
});

// 5. 测试MT5连接状态
console.log("📋 测试MT5连接状态:");
const mt5Status = elements.mt5ConnectionStatus;
if (mt5Status) {
    console.log(`当前MT5状态: ${mt5Status.textContent}`);
    console.log(`状态样式: ${mt5Status.className}`);
    
    // 手动检查连接
    if (typeof checkMT5Connection === 'function') {
        console.log("🔄 手动检查MT5连接...");
        checkMT5Connection();
    }
}

// 6. 总结测试结果
setTimeout(() => {
    console.log("🎯 测试总结:");
    console.log(`DOM元素完整性: ${allElementsExist ? '✅ 通过' : '❌ 失败'}`);
    console.log(`模型选择功能: ${modelSelect && modelSelect.options.length > 1 ? '✅ 正常' : '❌ 异常'}`);
    console.log(`关键函数可用性: ${functions.every(f => typeof window[f] === 'function') ? '✅ 正常' : '❌ 异常'}`);
    
    if (allElementsExist && modelSelect && modelSelect.options.length > 1) {
        console.log("🎉 所有功能测试通过！AI推理交易页面已修复完成！");
    } else {
        console.log("⚠️ 部分功能仍有问题，请检查上述详细信息");
    }
}, 2000);

console.log("✅ 测试脚本执行完成，请等待2秒查看总结结果");
'''
    
    with open('final_browser_test.js', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 最终浏览器测试脚本已创建: final_browser_test.js")

if __name__ == "__main__":
    success = test_syntax_fix()
    create_final_browser_test()
    
    if success:
        print("\n🎉 JavaScript语法错误修复完成！")
        print("📋 修复总结:")
        print("   1. ✅ 修复了null.value语法错误")
        print("   2. ✅ 修复了null.addEventListener语法错误")
        print("   3. ✅ 修复了null按钮操作语法错误")
        print("   4. ✅ 保留了所有关键功能")
        print("   5. ✅ API端点正常工作")
        print("\n🔄 请刷新浏览器页面并使用最终测试脚本验证")
        print("🌐 访问: http://127.0.0.1:5000/deep-learning/inference")
    else:
        print("\n❌ 语法错误修复测试失败")
        print("🔧 请检查上述错误信息")
