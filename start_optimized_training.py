#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动优化配置的训练任务
"""

import requests
import time
import json

def login_session():
    """登录并返回会话"""
    session = requests.Session()
    
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def start_optimized_training():
    """启动优化配置的训练"""
    print('🚀 启动优化配置的训练任务')
    print('=' * 50)
    
    session = login_session()
    if not session:
        return
    
    # 优化的训练配置 - 减少复杂度，提高稳定性
    optimized_configs = [
        {
            'model_name': f'稳定基础模型_{int(time.time())}',
            'model_type': 'lstm',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'mode': 'days',
                'training_days': 30
            },
            'sequence_length': 10,  # 减少序列长度
            'hidden_size': 32,      # 减少隐藏层大小
            'num_layers': 1,        # 单层LSTM
            'dropout': 0.1,         # 减少dropout
            'batch_size': 8,        # 小批次大小
            'learning_rate': 0.001,
            'epochs': 10,           # 减少训练轮数
            'patience': 3,
            'early_stopping': True,
            'min_epochs': 2,
            'use_gpu': True,
            'save_checkpoints': True,
            # 使用基础特征
            'use_enhanced_features': False,
            'features': {
                'price': True,
                'volume': True,
                'technical': True,
                'time': True
            }
        },
        {
            'model_name': f'轻量增强模型_{int(time.time())}',
            'model_type': 'lstm',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'mode': 'days',
                'training_days': 30
            },
            'sequence_length': 10,
            'hidden_size': 32,
            'num_layers': 1,
            'dropout': 0.1,
            'batch_size': 8,
            'learning_rate': 0.001,
            'epochs': 10,
            'patience': 3,
            'early_stopping': True,
            'min_epochs': 2,
            'use_gpu': True,
            'save_checkpoints': True,
            # 使用推荐增强特征（较少的特征数量）
            'use_enhanced_features': True,
            'feature_selection_strategy': 'custom',
            'analyze_feature_importance': True,
            'selected_features': [
                'bb_percent_b', 'atr_atr', 'stoch_stoch_k'  # 只选择3个核心特征
            ]
        }
    ]
    
    started_tasks = []
    
    for i, config in enumerate(optimized_configs):
        print(f"\n📊 启动配置 {i+1}: {config['model_name']}")
        print(f"   特征类型: {'增强特征' if config['use_enhanced_features'] else '基础特征'}")
        print(f"   模型复杂度: 简化 (hidden_size={config['hidden_size']}, layers={config['num_layers']})")
        print(f"   训练轮数: {config['epochs']}")
        
        try:
            response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                                   json=config,
                                   headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    task_id = result.get('task_id')
                    print(f"   ✅ 启动成功: {task_id}")
                    started_tasks.append({
                        'task_id': task_id,
                        'name': config['model_name'],
                        'type': '增强特征' if config['use_enhanced_features'] else '基础特征'
                    })
                else:
                    print(f"   ❌ 启动失败: {result.get('error')}")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 启动异常: {e}")
        
        # 等待一下再启动下一个
        time.sleep(2)
    
    # 总结
    print(f"\n🎯 启动总结:")
    print(f"   成功启动: {len(started_tasks)} 个任务")
    
    for task in started_tasks:
        print(f"   - {task['name']} ({task['type']}): {task['task_id']}")
    
    if started_tasks:
        print(f"\n💡 优化说明:")
        print(f"   - 减少了模型复杂度 (hidden_size=32, layers=1)")
        print(f"   - 降低了内存使用 (batch_size=8)")
        print(f"   - 减少了训练时间 (epochs=10)")
        print(f"   - 使用了稳定的配置参数")
        print(f"   - 增强特征模型只使用3个核心特征")
        
        print(f"\n📊 监控建议:")
        print(f"   1. 观察训练进度是否稳定推进")
        print(f"   2. 检查GPU内存使用是否正常")
        print(f"   3. 关注损失函数是否下降")
        print(f"   4. 如果再次卡住，考虑进一步减少复杂度")
    else:
        print(f"\n❌ 没有成功启动任何任务")

if __name__ == "__main__":
    start_optimized_training()
