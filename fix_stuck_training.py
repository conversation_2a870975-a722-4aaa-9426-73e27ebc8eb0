#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复卡住的训练任务
"""

import sqlite3
import json
import os
import requests
import time
from datetime import datetime, timedelta

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def get_stuck_training_tasks():
    """获取卡住的训练任务"""
    print("🔍 查找卡住的训练任务...")
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找状态为running但长时间无更新的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs,
                   train_loss, val_loss, logs, updated_at
            FROM training_tasks
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        stuck_tasks = []
        
        for task in tasks:
            task_id, model_id, status, progress, current_epoch, total_epochs, train_loss, val_loss, logs, updated_at = task
            
            # 检查是否卡住
            is_stuck = False
            
            # 条件1：进度>0但轮次为0，且损失为0
            if progress > 0 and current_epoch == 0 and train_loss == 0.0 and val_loss == 0.0:
                is_stuck = True
                
            # 条件2：长时间无更新
            if updated_at:
                try:
                    updated_time = datetime.fromisoformat(updated_at.replace('T', ' '))
                    if datetime.now() - updated_time > timedelta(minutes=10):
                        is_stuck = True
                except:
                    pass
            
            if is_stuck:
                stuck_tasks.append(task)
        
        if stuck_tasks:
            print(f"📊 找到 {len(stuck_tasks)} 个卡住的训练任务")
        else:
            print("✅ 没有找到卡住的训练任务")
        
        return stuck_tasks
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return []

def stop_stuck_task(session, task_id):
    """停止卡住的任务"""
    print(f"🛑 停止卡住的任务: {task_id[:8]}...")
    
    try:
        response = session.post(f'http://127.0.0.1:5000/api/deep-learning/stop-training/{task_id}')
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 任务停止成功")
                return True
            else:
                print(f"❌ 任务停止失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 停止任务异常: {e}")
        return False

def mark_task_as_failed(task_id, reason):
    """将任务标记为失败"""
    print(f"❌ 标记任务为失败: {task_id[:8]}...")
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE training_tasks
            SET status = 'failed',
                logs = ?,
                updated_at = ?
            WHERE id = ?
        ''', (
            json.dumps({
                'stage': 'failed',
                'message': f'训练卡住已自动标记为失败: {reason}',
                'error': reason,
                'auto_fixed': True
            }),
            datetime.now().isoformat(),
            task_id
        ))
        
        conn.commit()
        conn.close()
        
        print("✅ 任务已标记为失败")
        return True
        
    except Exception as e:
        print(f"❌ 标记失败异常: {e}")
        return False

def restart_training_with_optimized_config(session, original_task_data):
    """使用优化配置重启训练"""
    task_id, model_id, status, progress, current_epoch, total_epochs, train_loss, val_loss, logs, updated_at = original_task_data
    
    print(f"🔄 重启训练任务: {task_id[:8]}...")
    
    # 获取原始模型配置
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT name, config FROM deep_learning_models WHERE id = ?', (model_id,))
        model_data = cursor.fetchone()
        conn.close()
        
        if not model_data:
            print("❌ 找不到原始模型配置")
            return False
        
        model_name, config_str = model_data
        original_config = json.loads(config_str) if config_str else {}
        
        # 创建优化的配置
        optimized_config = {
            'model_name': f"{model_name}_RESTART_{int(time.time())}",
            'model_type': original_config.get('model_type', 'lstm'),
            'symbol': original_config.get('symbol', 'XAUUSD'),
            'timeframe': original_config.get('timeframe', 'H1'),
            'epochs': min(50, original_config.get('epochs', 100)),  # 减少轮次
            'batch_size': max(16, original_config.get('batch_size', 32)),  # 适中批次
            'learning_rate': 0.001,  # 稳定学习率
            'sequence_length': min(60, original_config.get('sequence_length', 60)),  # 适中序列长度
            'data_config': {
                'days': min(30, original_config.get('data_config', {}).get('days', 365)),  # 减少数据量
                'validation_split': 0.2
            },
            'early_stopping': True,
            'patience': 10,
            'min_epochs': 5
        }
        
        print(f"📝 优化配置:")
        for key, value in optimized_config.items():
            if key != 'data_config':
                print(f"   {key}: {value}")
        
        # 启动新的训练
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=optimized_config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                new_task_id = result.get('task_id')
                print(f"✅ 重启训练成功!")
                print(f"   新任务ID: {new_task_id}")
                return new_task_id
            else:
                print(f"❌ 重启训练失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 重启训练异常: {e}")
        return False

def monitor_new_training(session, task_id, duration=120):
    """监控新训练任务"""
    print(f"\n📊 监控新训练任务: {task_id[:8]}...")
    print("=" * 50)
    
    for i in range(duration // 10):  # 每10秒检查一次
        try:
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    progress = progress_data.get('progress', 0)
                    epoch = progress_data.get('epoch', 0)
                    status = progress_data.get('status', 'unknown')
                    train_loss = progress_data.get('train_loss', 0)
                    val_loss = progress_data.get('val_loss', 0)
                    
                    print(f"[{(i+1)*10:3d}s] 进度: {progress:5.1f}%, 轮次: {epoch:2d}, 状态: {status}, 训练损失: {train_loss:.4f}")
                    
                    # 检查是否正常进行
                    if epoch > 0 or train_loss > 0:
                        print("✅ 训练正常进行中")
                        return True
                    
                    # 如果训练完成或失败
                    if status in ['completed', 'failed', 'stopped']:
                        print(f"🏁 训练结束: {status}")
                        return status == 'completed'
                        
                else:
                    print(f"❌ API错误: {result.get('error')}")
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 监控异常: {e}")
        
        time.sleep(10)
    
    print("⏰ 监控超时")
    return False

def main():
    """主函数"""
    print("🔧 卡住训练任务修复工具")
    print("=" * 60)
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 查找卡住的任务
    stuck_tasks = get_stuck_training_tasks()
    
    if not stuck_tasks:
        print("✅ 没有发现卡住的训练任务")
        return
    
    # 3. 处理每个卡住的任务
    for task_data in stuck_tasks:
        task_id = task_data[0]
        
        print(f"\n🔧 处理卡住的任务: {task_id[:8]}...")
        
        # 尝试停止任务
        if stop_stuck_task(session, task_id):
            time.sleep(2)  # 等待停止完成
        
        # 标记为失败
        mark_task_as_failed(task_id, "训练卡住超时")
        
        # 询问是否重启
        response = input(f"是否要重启这个训练任务？(y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            new_task_id = restart_training_with_optimized_config(session, task_data)
            
            if new_task_id:
                # 监控新任务
                monitor_new_training(session, new_task_id)
        
        print("-" * 50)
    
    print("\n🎉 修复完成！")

if __name__ == "__main__":
    main()
