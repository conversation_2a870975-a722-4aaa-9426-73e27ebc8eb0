#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强特征UI显示
验证增强特征配置界面是否正确显示和工作
"""

import requests
from bs4 import BeautifulSoup
import re

def test_enhanced_features_ui():
    """测试增强特征UI"""
    print("🧪 测试增强特征UI显示")
    print("=" * 60)
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 2. 访问AI推理交易页面
        print("\n📋 访问AI推理交易页面...")
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code != 200:
            print(f"❌ 访问页面失败: {response.status_code}")
            return False
        
        print("✅ 页面访问成功")
        
        # 3. 解析HTML检查UI元素
        soup = BeautifulSoup(response.text, 'html.parser')
        
        success = True
        
        # 检查增强特征提示区域
        hint_area = soup.find('div', id='enhancedFeaturesHint')
        if hint_area:
            print("✅ 找到增强特征提示区域")
            
            # 检查提示内容
            hint_text = hint_area.get_text()
            if '增强特征' in hint_text and '动态风险管理' in hint_text:
                print("✅ 提示内容包含增强特征和动态风险管理")
            else:
                print("❌ 提示内容不完整")
                success = False
                
            # 检查"立即体验"按钮
            experience_btn = hint_area.find('button', onclick='toggleInferenceConfig()')
            if experience_btn:
                print("✅ 找到'立即体验'按钮")
            else:
                print("❌ 未找到'立即体验'按钮")
                success = False
        else:
            print("❌ 未找到增强特征提示区域")
            success = False
        
        # 检查AI推理配置按钮
        config_btn = soup.find('button', id='toggleInferenceBtn')
        if config_btn:
            print("✅ 找到AI推理配置按钮")
            
            btn_text = config_btn.get_text()
            if 'AI推理配置' in btn_text and '增强特征' in btn_text:
                print("✅ 按钮文本包含'AI推理配置'和'增强特征'")
            else:
                print(f"❌ 按钮文本不正确: {btn_text}")
                success = False
                
            # 检查按钮样式
            btn_class = config_btn.get('class', [])
            if 'btn-warning' in btn_class:
                print("✅ 按钮使用警告色样式")
            else:
                print(f"❌ 按钮样式不正确: {btn_class}")
                success = False
        else:
            print("❌ 未找到AI推理配置按钮")
            success = False
        
        # 检查增强特征配置区域
        config_area = soup.find('div', id='inferenceConfig')
        if config_area:
            print("✅ 找到推理配置区域")
            
            # 检查增强特征配置卡片
            enhanced_card = config_area.find('div', class_='border-primary')
            if enhanced_card:
                print("✅ 找到增强特征配置卡片")
                
                # 检查增强特征开关
                enhanced_switch = config_area.find('input', id='useEnhancedFeatures')
                if enhanced_switch:
                    print("✅ 找到增强特征开关")
                else:
                    print("❌ 未找到增强特征开关")
                    success = False
                
                # 检查特征选择策略下拉框
                strategy_select = config_area.find('select', id='featureSelectionStrategy')
                if strategy_select:
                    print("✅ 找到特征选择策略下拉框")
                    
                    # 检查选项
                    options = strategy_select.find_all('option')
                    option_values = [opt.get('value') for opt in options]
                    expected_options = ['recommended', 'all', 'top_importance', 'custom']
                    
                    if all(opt in option_values for opt in expected_options):
                        print("✅ 特征选择策略选项完整")
                    else:
                        print(f"❌ 特征选择策略选项不完整: {option_values}")
                        success = False
                else:
                    print("❌ 未找到特征选择策略下拉框")
                    success = False
                
                # 检查特征重要性分析开关
                importance_switch = config_area.find('input', id='analyzeFeatureImportance')
                if importance_switch:
                    print("✅ 找到特征重要性分析开关")
                else:
                    print("❌ 未找到特征重要性分析开关")
                    success = False
                    
            else:
                print("❌ 未找到增强特征配置卡片")
                success = False
            
            # 检查动态风险管理配置卡片
            risk_card = config_area.find('div', class_='border-warning')
            if risk_card:
                print("✅ 找到动态风险管理配置卡片")
                
                # 检查动态风险管理开关
                risk_switch = config_area.find('input', id='enableDynamicRiskManagement')
                if risk_switch:
                    print("✅ 找到动态风险管理开关")
                else:
                    print("❌ 未找到动态风险管理开关")
                    success = False
                
                # 检查ATR倍数输入框
                atr_input = config_area.find('input', id='atrMultiplier')
                if atr_input:
                    print("✅ 找到ATR倍数输入框")
                else:
                    print("❌ 未找到ATR倍数输入框")
                    success = False
                    
            else:
                print("❌ 未找到动态风险管理配置卡片")
                success = False
                
        else:
            print("❌ 未找到推理配置区域")
            success = False
        
        # 4. 检查JavaScript函数
        print(f"\n🔧 检查JavaScript函数...")
        
        # 检查关键函数是否存在
        js_functions = [
            'toggleEnhancedFeaturesConfig',
            'toggleDynamicRiskConfig', 
            'getEnhancedFeaturesConfig',
            'getDynamicRiskConfig',
            'dismissEnhancedFeaturesHint'
        ]
        
        page_content = response.text
        for func_name in js_functions:
            if f'function {func_name}' in page_content:
                print(f"✅ 找到JavaScript函数: {func_name}")
            else:
                print(f"❌ 未找到JavaScript函数: {func_name}")
                success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🔧 增强特征UI显示测试工具")
    print("=" * 80)
    
    print("📋 测试内容:")
    print("• 增强特征提示区域显示")
    print("• AI推理配置按钮样式和文本")
    print("• 增强特征配置卡片")
    print("• 动态风险管理配置卡片")
    print("• 相关JavaScript函数")
    print()
    
    success = test_enhanced_features_ui()
    
    if success:
        print("\n🎉 增强特征UI测试通过！")
        print("✅ 所有UI元素都正确显示")
        print("💡 用户现在可以看到:")
        print("   • 页面顶部的增强特征功能提示")
        print("   • 醒目的'AI推理配置'按钮（带增强特征标签）")
        print("   • 完整的增强特征配置选项")
        print("   • 动态风险管理配置选项")
        print("   • 所有相关的JavaScript功能")
        print()
        print("🎯 使用方法:")
        print("1. 点击页面上的'立即体验'按钮")
        print("2. 或点击'AI推理配置'按钮")
        print("3. 在配置面板中启用增强特征和动态风险管理")
        print("4. 选择合适的特征策略和风险参数")
    else:
        print("\n❌ 增强特征UI测试失败")
        print("🔧 请检查页面元素和JavaScript函数")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
