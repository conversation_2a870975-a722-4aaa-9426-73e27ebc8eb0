#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找JavaScript语法错误
"""

import re

def find_syntax_errors():
    """查找JavaScript语法错误"""
    print("🔍 查找JavaScript语法错误")
    print("=" * 50)
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📄 文件总行数: {len(lines)}")
        
        # 检查常见的语法错误模式
        error_patterns = [
            (r'null\.[a-zA-Z_]', 'null对象属性访问'),
            (r'undefined\.[a-zA-Z_]', 'undefined对象属性访问'),
            (r'===.*=(?!=)', '三等号后面跟赋值'),
            (r'==.*=(?!=)', '双等号后面跟赋值'),
            (r'const\s+[a-zA-Z_]\w*\s*=\s*null\s*;.*\1\.[a-zA-Z_]', 'null变量后续使用'),
            (r'let\s+[a-zA-Z_]\w*\s*=\s*null\s*;.*\1\.[a-zA-Z_]', 'null变量后续使用'),
            (r'var\s+[a-zA-Z_]\w*\s*=\s*null\s*;.*\1\.[a-zA-Z_]', 'null变量后续使用'),
        ]
        
        found_errors = []
        
        for line_num, line in enumerate(lines, 1):
            line_content = line.strip()
            if not line_content or line_content.startswith('//') or line_content.startswith('<!--'):
                continue
            
            for pattern, description in error_patterns:
                if re.search(pattern, line_content):
                    found_errors.append({
                        'line': line_num,
                        'content': line_content,
                        'error': description,
                        'pattern': pattern
                    })
        
        if found_errors:
            print(f"❌ 发现 {len(found_errors)} 个潜在语法错误:")
            for error in found_errors:
                print(f"   第{error['line']}行: {error['error']}")
                print(f"   内容: {error['content'][:100]}...")
                print()
        else:
            print("✅ 未发现明显的语法错误模式")
        
        # 特别检查第1138行附近
        print(f"\n🔍 检查第1138行附近:")
        start_line = max(1, 1138 - 5)
        end_line = min(len(lines), 1138 + 5)
        
        for i in range(start_line - 1, end_line):
            line_num = i + 1
            line_content = lines[i].rstrip()
            marker = " >>> " if line_num == 1138 else "     "
            print(f"{marker}{line_num:4d}: {line_content}")
        
        # 检查模板字符串语法
        print(f"\n🔍 检查模板字符串语法:")
        template_string_errors = []
        
        for line_num, line in enumerate(lines, 1):
            line_content = line.strip()
            
            # 检查模板字符串中的语法错误
            if '`' in line_content and '${' in line_content:
                # 检查是否有未闭合的模板字符串
                backtick_count = line_content.count('`')
                if backtick_count % 2 != 0:
                    # 可能是跨行模板字符串，检查后续行
                    continue
                
                # 检查模板字符串内的表达式
                template_expressions = re.findall(r'\$\{([^}]+)\}', line_content)
                for expr in template_expressions:
                    # 检查表达式中的语法错误
                    if '===' in expr and '=' in expr.replace('===', ''):
                        template_string_errors.append({
                            'line': line_num,
                            'content': line_content,
                            'expression': expr,
                            'error': '模板字符串表达式中的赋值语法错误'
                        })
        
        if template_string_errors:
            print(f"❌ 发现 {len(template_string_errors)} 个模板字符串语法错误:")
            for error in template_string_errors:
                print(f"   第{error['line']}行: {error['error']}")
                print(f"   表达式: ${{{error['expression']}}}")
                print(f"   内容: {error['content'][:100]}...")
                print()
        else:
            print("✅ 模板字符串语法正常")
        
        # 创建修复建议
        if found_errors or template_string_errors:
            print(f"\n🔧 修复建议:")
            print("1. 检查所有null对象的属性访问")
            print("2. 确保变量在使用前已正确初始化")
            print("3. 检查模板字符串中的表达式语法")
            print("4. 使用浏览器开发者工具查看具体错误位置")
            
            return False
        else:
            print(f"\n✅ 语法检查通过")
            return True
        
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def create_browser_syntax_checker():
    """创建浏览器语法检查器"""
    print(f"\n🔧 创建浏览器语法检查器...")
    
    checker_script = '''
// 浏览器JavaScript语法检查器
console.log("🔍 开始JavaScript语法检查...");

// 1. 检查全局错误
let hasErrors = false;
const originalError = console.error;
const errors = [];

console.error = function(...args) {
    errors.push(args.join(' '));
    originalError.apply(console, args);
    hasErrors = true;
};

// 2. 检查语法错误
try {
    // 尝试访问可能有问题的元素
    const testElements = [
        'tradingModelSelect',
        'mt5ConnectionStatus', 
        'startTradingBtn',
        'enableEnhancedFeatures'
    ];
    
    console.log("📋 检查DOM元素访问:");
    testElements.forEach(id => {
        try {
            const element = document.getElementById(id);
            if (element) {
                console.log(`✅ ${id}: 正常`);
            } else {
                console.log(`⚠️ ${id}: 元素不存在`);
            }
        } catch (error) {
            console.error(`❌ ${id}: 访问错误 -`, error.message);
            hasErrors = true;
        }
    });
    
    // 3. 检查关键函数
    console.log("📋 检查关键函数:");
    const testFunctions = [
        'loadTradingModels',
        'checkMT5Connection',
        'startAutoTrading',
        'executeManualTrade'
    ];
    
    testFunctions.forEach(funcName => {
        try {
            if (typeof window[funcName] === 'function') {
                console.log(`✅ ${funcName}: 函数存在`);
            } else {
                console.log(`⚠️ ${funcName}: 函数不存在`);
            }
        } catch (error) {
            console.error(`❌ ${funcName}: 检查错误 -`, error.message);
            hasErrors = true;
        }
    });
    
    // 4. 尝试调用loadTradingModels
    console.log("🔄 尝试调用loadTradingModels...");
    if (typeof loadTradingModels === 'function') {
        loadTradingModels().then(() => {
            console.log("✅ loadTradingModels调用成功");
        }).catch(error => {
            console.error("❌ loadTradingModels调用失败:", error);
            hasErrors = true;
        });
    }
    
} catch (error) {
    console.error("❌ 语法检查过程中发生错误:", error);
    hasErrors = true;
}

// 5. 延迟检查结果
setTimeout(() => {
    console.log("🎯 语法检查结果:");
    if (hasErrors || errors.length > 0) {
        console.log("❌ 发现JavaScript错误:");
        errors.forEach((error, index) => {
            console.log(`  ${index + 1}. ${error}`);
        });
        console.log("🔧 请检查浏览器控制台的详细错误信息");
    } else {
        console.log("✅ 未发现JavaScript语法错误");
        console.log("🎉 页面JavaScript运行正常！");
    }
}, 3000);

console.log("⏳ 语法检查进行中，请等待3秒查看结果...");
'''
    
    with open('browser_syntax_checker.js', 'w', encoding='utf-8') as f:
        f.write(checker_script)
    
    print("✅ 浏览器语法检查器已创建: browser_syntax_checker.js")
    print("📋 使用方法:")
    print("   1. 打开AI推理交易页面")
    print("   2. 按F12打开开发者工具")
    print("   3. 在Console标签中粘贴browser_syntax_checker.js的内容并执行")

if __name__ == "__main__":
    success = find_syntax_errors()
    create_browser_syntax_checker()
    
    if success:
        print("\n🎉 静态语法检查通过！")
        print("🔄 请使用浏览器语法检查器进一步验证")
    else:
        print("\n❌ 发现潜在语法错误")
        print("🔧 请根据上述建议进行修复")
