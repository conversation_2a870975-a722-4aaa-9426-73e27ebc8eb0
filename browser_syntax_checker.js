
// 浏览器JavaScript语法检查器
console.log("🔍 开始JavaScript语法检查...");

// 1. 检查全局错误
let hasErrors = false;
const originalError = console.error;
const errors = [];

console.error = function(...args) {
    errors.push(args.join(' '));
    originalError.apply(console, args);
    hasErrors = true;
};

// 2. 检查语法错误
try {
    // 尝试访问可能有问题的元素
    const testElements = [
        'tradingModelSelect',
        'mt5ConnectionStatus', 
        'startTradingBtn',
        'enableEnhancedFeatures'
    ];
    
    console.log("📋 检查DOM元素访问:");
    testElements.forEach(id => {
        try {
            const element = document.getElementById(id);
            if (element) {
                console.log(`✅ ${id}: 正常`);
            } else {
                console.log(`⚠️ ${id}: 元素不存在`);
            }
        } catch (error) {
            console.error(`❌ ${id}: 访问错误 -`, error.message);
            hasErrors = true;
        }
    });
    
    // 3. 检查关键函数
    console.log("📋 检查关键函数:");
    const testFunctions = [
        'loadTradingModels',
        'checkMT5Connection',
        'startAutoTrading',
        'executeManualTrade'
    ];
    
    testFunctions.forEach(funcName => {
        try {
            if (typeof window[funcName] === 'function') {
                console.log(`✅ ${funcName}: 函数存在`);
            } else {
                console.log(`⚠️ ${funcName}: 函数不存在`);
            }
        } catch (error) {
            console.error(`❌ ${funcName}: 检查错误 -`, error.message);
            hasErrors = true;
        }
    });
    
    // 4. 尝试调用loadTradingModels
    console.log("🔄 尝试调用loadTradingModels...");
    if (typeof loadTradingModels === 'function') {
        loadTradingModels().then(() => {
            console.log("✅ loadTradingModels调用成功");
        }).catch(error => {
            console.error("❌ loadTradingModels调用失败:", error);
            hasErrors = true;
        });
    }
    
} catch (error) {
    console.error("❌ 语法检查过程中发生错误:", error);
    hasErrors = true;
}

// 5. 延迟检查结果
setTimeout(() => {
    console.log("🎯 语法检查结果:");
    if (hasErrors || errors.length > 0) {
        console.log("❌ 发现JavaScript错误:");
        errors.forEach((error, index) => {
            console.log(`  ${index + 1}. ${error}`);
        });
        console.log("🔧 请检查浏览器控制台的详细错误信息");
    } else {
        console.log("✅ 未发现JavaScript语法错误");
        console.log("🎉 页面JavaScript运行正常！");
    }
}, 3000);

console.log("⏳ 语法检查进行中，请等待3秒查看结果...");
