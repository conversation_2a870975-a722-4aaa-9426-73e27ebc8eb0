
// 总盈亏显示修复测试脚本
console.log("🧪 开始测试总盈亏显示修复...");

// 1. 测试模拟统计数据显示
console.log("📋 测试模拟统计数据显示:");
if (typeof displayBacktestStats === 'function') {
    // 创建包含不同盈亏字段的模拟数据
    const mockStats1 = {
        total_profit: 150.75,    // 前端期望字段
        net_profit: 150.75,      // 后端返回字段
        gross_profit: 250.50,
        gross_loss: 99.75,
        win_rate: 65.5,
        total_trades: 25,
        winning_trades: 16,
        losing_trades: 9,
        profit_factor: 2.51,
        max_drawdown: 8.5,
        final_balance: 10150.75
    };
    
    const mockStats2 = {
        // 只有net_profit字段的情况
        net_profit: -75.25,
        gross_profit: 125.50,
        gross_loss: 200.75,
        win_rate: 40.0,
        total_trades: 20,
        winning_trades: 8,
        losing_trades: 12,
        profit_factor: 0.63,
        max_drawdown: 12.3,
        final_balance: 9924.75
    };
    
    const mockStats3 = {
        // 只有gross_profit字段的情况
        gross_profit: 89.50,
        gross_loss: 45.25,
        win_rate: 70.0,
        total_trades: 10,
        winning_trades: 7,
        losing_trades: 3,
        profit_factor: 1.98,
        max_drawdown: 5.2,
        final_balance: 10089.50
    };
    
    console.log("🔄 测试包含total_profit字段的数据...");
    displayBacktestStats(mockStats1);
    
    setTimeout(() => {
        console.log("🔄 测试只有net_profit字段的数据...");
        displayBacktestStats(mockStats2);
        
        setTimeout(() => {
            console.log("🔄 测试只有gross_profit字段的数据...");
            displayBacktestStats(mockStats3);
            
            setTimeout(() => {
                console.log("✅ 统计显示测试完成");
                
                // 检查显示结果
                const statsContainer = document.getElementById('backtestStats');
                if (statsContainer) {
                    const profitElement = statsContainer.querySelector('.metric-value');
                    if (profitElement) {
                        const profitText = profitElement.textContent;
                        console.log(`📊 当前显示的总盈亏: ${profitText}`);
                        
                        if (profitText.includes('$') && !profitText.includes('$0.00')) {
                            console.log("✅ 总盈亏显示正常，不再是$0.00");
                        } else {
                            console.log("❌ 总盈亏仍然显示为$0.00或格式错误");
                        }
                    }
                }
            }, 1000);
        }, 2000);
    }, 2000);
} else {
    console.log("❌ displayBacktestStats函数不存在");
}

// 2. 测试字段映射逻辑
console.log("📋 测试字段映射逻辑:");
const testStats = {
    net_profit: 123.45,
    gross_profit: 200.00,
    win_rate: 60.0,
    total_trades: 15
};

// 模拟前端字段映射逻辑
const totalProfit = testStats.total_profit || testStats.net_profit || testStats.gross_profit || 0;
console.log(`输入数据: ${JSON.stringify(testStats)}`);
console.log(`映射结果: totalProfit = ${totalProfit}`);

if (totalProfit === 123.45) {
    console.log("✅ 字段映射逻辑正确：优先使用net_profit");
} else {
    console.log("❌ 字段映射逻辑错误");
}

console.log("🎉 总盈亏显示修复测试完成！");
console.log("💡 修复内容:");
console.log("   1. 后端添加total_profit字段映射");
console.log("   2. 前端支持多种盈亏字段名称");
console.log("   3. 确保总盈亏不再显示为$0.00");
