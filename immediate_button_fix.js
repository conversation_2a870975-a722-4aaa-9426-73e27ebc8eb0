// 立即修复手动交易按钮状态的JavaScript代码
// 请在浏览器控制台中运行此代码

console.log('🔧 开始立即修复手动交易按钮状态...');

// 1. 获取按钮元素
const buyBtn = document.getElementById('manualBuyBtn');
const sellBtn = document.getElementById('manualSellBtn');

if (!buyBtn || !sellBtn) {
    console.error('❌ 找不到手动交易按钮元素');
} else {
    console.log('✅ 找到手动交易按钮元素');
    
    // 2. 强制重置按钮状态
    console.log('🔄 强制重置按钮状态...');
    
    // 重置买入按钮
    buyBtn.disabled = true;
    buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
    buyBtn.className = 'btn btn-outline-success w-100';
    
    // 重置卖出按钮
    sellBtn.disabled = true;
    sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
    sellBtn.className = 'btn btn-outline-danger w-100';
    
    console.log('✅ 按钮状态已重置为正常禁用状态');
    
    // 3. 检查条件并更新状态
    setTimeout(() => {
        console.log('🔍 检查手动交易条件...');
        
        // 检查各种条件
        const mt5Connected = window.mt5Connected || false;
        const selectedTradingModel = window.selectedTradingModel || null;
        const currentMarketData = window.currentMarketData || null;
        
        console.log('条件检查结果:', {
            mt5Connected: mt5Connected,
            selectedTradingModel: !!selectedTradingModel,
            currentMarketData: !!currentMarketData
        });
        
        const canTrade = mt5Connected && selectedTradingModel && currentMarketData;
        
        if (canTrade) {
            // 启用按钮
            buyBtn.disabled = false;
            buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
            buyBtn.className = 'btn btn-success w-100';
            
            sellBtn.disabled = false;
            sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
            sellBtn.className = 'btn btn-danger w-100';
            
            console.log('✅ 按钮已启用，可以进行手动交易');
        } else {
            // 保持禁用状态，但显示正常文本
            console.log('⚠️ 按钮保持禁用状态');
            
            let reason = '';
            if (!mt5Connected) reason = 'MT5未连接';
            else if (!selectedTradingModel) reason = '未选择交易模型';
            else if (!currentMarketData) reason = '无市场数据';
            
            console.log(`禁用原因: ${reason}`);
        }
    }, 100);
}

// 4. 重新定义按钮设置函数（修复版本）
window.setManualTradeButtonsEnabled = function(enabled, isProcessing = false) {
    const buyBtn = document.getElementById('manualBuyBtn');
    const sellBtn = document.getElementById('manualSellBtn');

    console.log(`🔄 设置手动下单按钮状态: ${enabled ? '启用' : '禁用'}, 处理中: ${isProcessing}`);

    if (buyBtn) {
        buyBtn.disabled = !enabled;
        if (enabled) {
            buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
            buyBtn.className = 'btn btn-success w-100';
        } else if (isProcessing) {
            buyBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
            buyBtn.className = 'btn btn-secondary w-100';
        } else {
            // 正常禁用状态，不显示"处理中"
            buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
            buyBtn.className = 'btn btn-outline-success w-100';
        }
    }

    if (sellBtn) {
        sellBtn.disabled = !enabled;
        if (enabled) {
            sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
            sellBtn.className = 'btn btn-danger w-100';
        } else if (isProcessing) {
            sellBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
            sellBtn.className = 'btn btn-secondary w-100';
        } else {
            // 正常禁用状态，不显示"处理中"
            sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
            sellBtn.className = 'btn btn-outline-danger w-100';
        }
    }
};

// 5. 重新定义更新函数
window.updateManualTradeButtonsStatus = function() {
    const buyBtn = document.getElementById('manualBuyBtn');
    const sellBtn = document.getElementById('manualSellBtn');
    
    if (!buyBtn || !sellBtn) {
        console.log('⚠️ 手动下单按钮元素未找到，稍后重试');
        setTimeout(window.updateManualTradeButtonsStatus, 500);
        return;
    }

    const canTrade = window.mt5Connected && window.selectedTradingModel && window.currentMarketData;
    
    console.log('🔍 检查手动下单条件:', {
        mt5Connected: window.mt5Connected,
        selectedTradingModel: !!window.selectedTradingModel,
        currentMarketData: !!window.currentMarketData,
        canTrade: canTrade
    });
    
    window.setManualTradeButtonsEnabled(canTrade, false); // 正常状态，不是处理中

    if (!canTrade) {
        let reason = '';
        if (!window.mt5Connected) reason = 'MT5未连接';
        else if (!window.selectedTradingModel) reason = '未选择交易模型';
        else if (!window.currentMarketData) reason = '无市场数据';

        console.log(`🔒 手动下单按钮已禁用: ${reason}`);
    } else {
        console.log('🔓 手动下单按钮已启用');
    }
};

console.log('🎉 立即修复完成！按钮应该不再显示"处理中"状态');
console.log('💡 如果需要，可以运行 updateManualTradeButtonsStatus() 来重新检查状态');
