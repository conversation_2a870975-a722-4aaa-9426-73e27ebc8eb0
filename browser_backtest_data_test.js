
// AI推理回测数据显示修复测试脚本
console.log("🧪 开始测试AI推理回测数据显示修复...");

// 1. 测试交易类型格式化函数
console.log("📋 测试交易类型格式化:");
if (typeof formatTradeType === 'function') {
    const testTypes = [
        'BUY', 'SELL', 'buy', 'sell',
        'LONG', 'SHORT', 'long', 'short',
        '0', '1', 0, 1,
        'ORDER_TYPE_BUY', 'ORDER_TYPE_SELL',
        null, undefined, '', 'undefined'
    ];
    
    testTypes.forEach(type => {
        const result = formatTradeType(type);
        console.log(`   输入: ${type} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatTradeType函数不存在");
}

// 2. 测试模拟交易数据显示
console.log("📋 测试模拟交易数据显示:");
if (typeof displayTradeDetails === 'function') {
    // 创建修复后的模拟交易数据
    const mockTrades = [
        {
            open_time: '2025-07-28T17:30:00Z',
            type: 'BUY',
            lot_size: 0.01,
            open_price: 3302.25000,
            close_price: 3312.85000,
            stop_loss: 3292.25000,  // 修复：添加止损价格
            take_profit: 3322.25000, // 修复：添加止盈价格
            profit: 10.60,
            confidence: 0.585
        },
        {
            open_time: '2025-07-29T11:15:00Z',
            type: 'SELL',
            lot_size: 0.01,
            open_price: 3308.51000,
            close_price: 3327.55000,
            stop_loss: 3318.51000,  // 修复：添加止损价格
            take_profit: 3288.51000, // 修复：添加止盈价格
            profit: -19.04,
            confidence: 0.50
        },
        {
            open_time: '2025-07-30T09:45:00Z',
            type: 'BUY',
            lot_size: 0.01,
            open_price: 3315.75000,
            close_price: 3325.25000,
            stop_loss: 3305.75000,  // 修复：添加止损价格
            take_profit: 3335.75000, // 修复：添加止盈价格
            profit: 9.50,
            confidence: 0.72
        }
    ];
    
    console.log("🔄 显示修复后的模拟交易数据...");
    displayTradeDetails(mockTrades);
    console.log("✅ 交易详情显示完成");
    
    // 检查显示结果
    setTimeout(() => {
        const resultsContainer = document.getElementById('backtestResults');
        if (resultsContainer) {
            const tableRows = resultsContainer.querySelectorAll('tbody tr');
            console.log(`📊 显示了 ${tableRows.length} 行交易数据`);
            
            // 检查第一行数据
            if (tableRows.length > 0) {
                const firstRow = tableRows[0];
                const cells = firstRow.querySelectorAll('td');
                if (cells.length >= 9) {
                    console.log("📋 第一行数据检查:");
                    console.log(`   时间: ${cells[0].textContent}`);
                    console.log(`   类型: ${cells[1].textContent}`);
                    console.log(`   手数: ${cells[2].textContent}`);
                    console.log(`   开仓价: ${cells[3].textContent}`);
                    console.log(`   平仓价: ${cells[4].textContent}`);
                    console.log(`   止损: ${cells[5].textContent}`);
                    console.log(`   止盈: ${cells[6].textContent}`);
                    console.log(`   盈亏: ${cells[7].textContent}`);
                    console.log(`   置信度: ${cells[8].textContent}`);
                    
                    // 检查是否还有undefined或N/A
                    const hasUndefined = Array.from(cells).some(cell => 
                        cell.textContent.includes('undefined') || 
                        cell.textContent.includes('N/A')
                    );
                    
                    if (hasUndefined) {
                        console.log("❌ 仍然存在undefined或N/A显示");
                    } else {
                        console.log("✅ 所有数据显示正常");
                    }
                }
            }
        }
    }, 1000);
} else {
    console.log("❌ displayTradeDetails函数不存在");
}

console.log("🎉 AI推理回测数据显示修复测试完成！");
console.log("💡 修复内容:");
console.log("   1. 交易类型：支持多种格式，统一显示为中文");
console.log("   2. 止损止盈：后端返回实际价格，前端正确显示");
console.log("   3. 时间格式：统一使用open_time字段");
console.log("   4. 数据兼容：支持多种字段名称");
