#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试训练修复是否有效
"""

import requests
import json
import time
from datetime import datetime

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def start_minimal_training(session):
    """启动最小化训练测试"""
    print("🚀 启动最小化训练测试")
    print("=" * 40)
    
    # 使用最小化配置
    config = {
        'model_name': f'MINIMAL_TEST_{int(time.time())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'epochs': 3,  # 只训练3轮
        'batch_size': 8,  # 小批次
        'learning_rate': 0.01,
        'sequence_length': 10,  # 短序列
        'data_config': {
            'days': 7,  # 只用7天数据
            'validation_split': 0.2
        },
        'early_stopping': False,  # 禁用早停
        'save_checkpoints': False  # 禁用检查点
    }
    
    print(f"📝 最小化配置:")
    for key, value in config.items():
        if key != 'data_config':
            print(f"   {key}: {value}")
    
    try:
        response = session.post(
            'http://127.0.0.1:5000/api/deep-learning/start-training',
            json=config,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 训练启动成功!")
                print(f"   任务ID: {task_id}")
                return task_id
            else:
                print(f"❌ 训练启动失败: {result.get('error')}")
                return None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 启动训练异常: {e}")
        return None

def monitor_training_detailed(session, task_id, duration=300):
    """详细监控训练进度"""
    print(f"\n📊 详细监控训练进度 (任务: {task_id[:8]}...)")
    print("=" * 60)
    
    last_progress = -1
    last_epoch = -1
    progress_updates = 0
    stuck_count = 0
    
    for i in range(duration // 5):  # 每5秒检查一次
        try:
            response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    progress_data = result['progress']
                    progress = progress_data.get('progress', 0)
                    epoch = progress_data.get('epoch', 0)
                    status = progress_data.get('status', 'unknown')
                    train_loss = progress_data.get('train_loss', 0)
                    val_loss = progress_data.get('val_loss', 0)
                    
                    # 解析日志获取更多信息
                    logs = progress_data.get('logs')
                    stage = 'unknown'
                    message = ''
                    
                    if logs:
                        try:
                            log_data = json.loads(logs)
                            stage = log_data.get('stage', 'unknown')
                            message = log_data.get('message', '')
                        except:
                            pass
                    
                    print(f"[{(i+1)*5:3d}s] 进度: {progress:5.1f}%, 轮次: {epoch:2d}, 状态: {status:12s}, 阶段: {stage:15s}, 训练损失: {train_loss:.4f}")
                    
                    # 检查进度更新
                    if progress != last_progress or epoch != last_epoch:
                        progress_updates += 1
                        stuck_count = 0
                        print(f"        ✅ 进度更新 (总更新: {progress_updates})")
                        
                        # 如果轮次开始增加，说明训练真正开始了
                        if epoch > 0 and last_epoch == 0:
                            print("        🎉 训练循环成功启动！")
                        
                    else:
                        stuck_count += 1
                        if stuck_count >= 12:  # 60秒无变化
                            print(f"        ⚠️ 进度卡住 (连续{stuck_count}次无变化)")
                    
                    last_progress = progress
                    last_epoch = epoch
                    
                    # 检查训练是否完成
                    if status in ['completed', 'failed', 'stopped']:
                        print(f"        🏁 训练结束: {status}")
                        return status == 'completed'
                    
                    # 如果训练正常进行（轮次>0且有损失），认为修复成功
                    if epoch > 0 and train_loss > 0:
                        print("        ✅ 训练正常进行，修复成功！")
                        return True
                        
                else:
                    print(f"[{(i+1)*5:3d}s] ❌ API错误: {result.get('error')}")
                    
            else:
                print(f"[{(i+1)*5:3d}s] ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"[{(i+1)*5:3d}s] ❌ 监控异常: {e}")
        
        time.sleep(5)
    
    print("⏰ 监控超时")
    return False

def main():
    """主函数"""
    print("🧪 训练修复效果测试")
    print("=" * 50)
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 启动最小化训练
    task_id = start_minimal_training(session)
    if not task_id:
        return
    
    # 3. 详细监控训练
    success = monitor_training_detailed(session, task_id)
    
    # 4. 总结结果
    print("\n" + "=" * 60)
    print("📋 测试结果:")
    
    if success:
        print("✅ 训练修复成功！")
        print("   - 训练循环正常启动")
        print("   - 进度正确更新")
        print("   - 轮次正常递增")
        print("   - 损失值正常计算")
    else:
        print("❌ 训练修复失败")
        print("   - 训练可能仍然卡住")
        print("   - 需要进一步调试")
    
    print("\n💡 建议:")
    if success:
        print("   - 可以正常使用训练功能")
        print("   - 建议使用适中的配置参数")
    else:
        print("   - 检查系统资源使用情况")
        print("   - 查看应用程序日志")
        print("   - 考虑重启训练服务")

if __name__ == "__main__":
    main()
