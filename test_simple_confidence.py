#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试置信度类型修复
"""

def test_confidence_comparison():
    """测试置信度比较"""
    
    print("🧪 测试置信度类型比较")
    print("=" * 40)
    
    # 测试不同类型的置信度值
    test_cases = [
        (0.5, 0.3, "浮点数 vs 浮点数"),
        ("0.5", 0.3, "字符串 vs 浮点数"),
        (0.5, "0.3", "浮点数 vs 字符串"),
        ("0.5", "0.3", "字符串 vs 字符串"),
        (None, 0.3, "None vs 浮点数"),
        (0.5, None, "浮点数 vs None"),
    ]
    
    for confidence, min_confidence, description in test_cases:
        print(f"\n📊 测试: {description}")
        print(f"   confidence: {confidence} (类型: {type(confidence)})")
        print(f"   min_confidence: {min_confidence} (类型: {type(min_confidence)})")
        
        try:
            # 模拟修复后的代码逻辑
            confidence_fixed = float(confidence) if confidence is not None else 0.0
            min_confidence_fixed = float(min_confidence) if min_confidence is not None else 0.3
            
            result = confidence_fixed >= min_confidence_fixed
            
            print(f"   修复后: {confidence_fixed} >= {min_confidence_fixed} = {result}")
            print(f"   ✅ 比较成功")
            
        except Exception as e:
            print(f"   ❌ 比较失败: {e}")

def test_deep_learning_service():
    """测试深度学习服务的置信度处理"""
    
    print(f"\n🧪 测试深度学习服务置信度处理")
    print("=" * 40)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        dl_service = DeepLearningService()
        
        # 模拟推理结果
        mock_inference_results = [
            {
                'prediction': 'BUY',
                'confidence': '0.75',  # 字符串类型
                'current_price': 2650.50
            },
            {
                'prediction': 'SELL',
                'confidence': 0.65,  # 浮点数类型
                'current_price': 2650.50
            },
            {
                'prediction': 'HOLD',
                'confidence': None,  # None类型
                'current_price': 2650.50
            }
        ]
        
        min_confidence = 0.3
        
        for i, result in enumerate(mock_inference_results, 1):
            print(f"\n📊 测试推理结果 {i}:")
            print(f"   原始置信度: {result['confidence']} (类型: {type(result['confidence'])})")
            
            # 模拟修复后的置信度处理
            confidence_raw = result.get('confidence', 0)
            try:
                confidence = float(confidence_raw) if confidence_raw is not None else 0.0
            except (ValueError, TypeError):
                print(f"   ⚠️ 置信度类型转换失败: {confidence_raw}, 使用默认值0.0")
                confidence = 0.0
            
            print(f"   修复后置信度: {confidence} (类型: {type(confidence)})")
            
            # 测试比较
            try:
                comparison_result = confidence >= min_confidence
                print(f"   比较结果: {confidence} >= {min_confidence} = {comparison_result}")
                print(f"   ✅ 处理成功")
            except Exception as e:
                print(f"   ❌ 比较失败: {e}")
        
        print(f"\n✅ 深度学习服务置信度处理测试完成")
        
    except Exception as e:
        print(f"❌ 深度学习服务测试失败: {e}")

def main():
    """主函数"""
    
    print("🚀 置信度类型修复验证")
    print("=" * 80)
    
    # 测试基本的置信度比较
    test_confidence_comparison()
    
    # 测试深度学习服务
    test_deep_learning_service()
    
    print(f"\n🎉 所有测试完成!")
    print(f"💡 修复后的代码应该能正确处理各种类型的置信度值")

if __name__ == "__main__":
    main()
