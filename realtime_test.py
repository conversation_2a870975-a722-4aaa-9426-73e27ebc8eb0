#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时测试页面功能
"""

import requests
import time
import json

def test_realtime_functionality():
    """实时测试页面功能"""
    print("🔍 实时测试AI推理交易页面功能")
    print("=" * 50)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 1. 测试模型列表API
        print(f"\n🔍 测试模型列表API...")
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if models_response.status_code == 200:
            models_data = models_response.json()
            if models_data.get('success'):
                models = models_data.get('models', [])
                completed_models = [m for m in models if m.get('status') == 'completed']
                print(f"✅ 模型API正常: {len(completed_models)} 个已完成模型")
                
                # 保存模型数据用于分析
                with open('available_models.json', 'w', encoding='utf-8') as f:
                    json.dump(completed_models, f, indent=2, ensure_ascii=False)
                
                print("💾 模型数据已保存到: available_models.json")
                
                if completed_models:
                    print("📋 可用模型列表:")
                    for i, model in enumerate(completed_models[:5]):  # 显示前5个
                        print(f"   {i+1}. {model.get('name')} ({model.get('model_type')}, {model.get('symbol')})")
                else:
                    print("⚠️ 没有已完成的模型")
                    return False
            else:
                print(f"❌ 模型API错误: {models_data.get('error')}")
                return False
        else:
            print(f"❌ 模型API失败: {models_response.status_code}")
            return False
        
        # 2. 测试MT5连接API
        print(f"\n🔍 测试MT5连接API...")
        mt5_response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        
        if mt5_response.status_code == 200:
            mt5_data = mt5_response.json()
            connected = mt5_data.get('connected', False)
            print(f"✅ MT5 API正常: 连接状态 = {connected}")
            
            if not connected:
                error_msg = mt5_data.get('error', '未知错误')
                print(f"   连接错误: {error_msg}")
                
                # 尝试自动连接
                print("🔄 尝试自动连接MT5...")
                connect_response = session.post('http://127.0.0.1:5000/api/mt5/auto-connect')
                if connect_response.status_code == 200:
                    connect_data = connect_response.json()
                    if connect_data.get('success'):
                        print("✅ MT5自动连接成功")
                        time.sleep(2)  # 等待连接稳定
                        
                        # 重新检查连接状态
                        mt5_check = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
                        if mt5_check.status_code == 200:
                            mt5_check_data = mt5_check.json()
                            print(f"✅ 重新检查: 连接状态 = {mt5_check_data.get('connected', False)}")
                    else:
                        print(f"❌ MT5自动连接失败: {connect_data.get('error')}")
                else:
                    print(f"❌ MT5自动连接请求失败: {connect_response.status_code}")
        else:
            print(f"❌ MT5 API失败: {mt5_response.status_code}")
            return False
        
        # 3. 测试页面JavaScript执行
        print(f"\n🔍 测试页面JavaScript执行...")
        
        # 访问页面并检查关键JavaScript函数
        page_response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        if page_response.status_code == 200:
            content = page_response.text
            
            # 检查关键JavaScript函数是否存在
            js_functions = [
                'loadTradingModels',
                'checkMT5Connection',
                'onTradingModelChange',
                'startAutoTrading',
                'executeManualTrade'
            ]
            
            missing_functions = []
            for func in js_functions:
                if f'function {func}' not in content:
                    missing_functions.append(func)
            
            if missing_functions:
                print(f"❌ 缺失JavaScript函数: {missing_functions}")
                return False
            else:
                print("✅ 所有关键JavaScript函数都存在")
            
            # 检查DOM元素
            dom_elements = [
                'id="tradingModelSelect"',
                'id="mt5ConnectionStatus"',
                'id="startTradingBtn"',
                'id="enableEnhancedFeatures"'
            ]
            
            missing_elements = []
            for element in dom_elements:
                if element not in content:
                    missing_elements.append(element)
            
            if missing_elements:
                print(f"❌ 缺失DOM元素: {missing_elements}")
                return False
            else:
                print("✅ 所有关键DOM元素都存在")
        
        # 4. 创建浏览器测试脚本
        print(f"\n🔧 创建浏览器测试脚本...")
        
        browser_test_js = '''
// 浏览器控制台测试脚本
console.log("🧪 开始测试AI推理交易功能...");

// 1. 检查关键元素是否存在
const elements = {
    tradingModelSelect: document.getElementById('tradingModelSelect'),
    mt5ConnectionStatus: document.getElementById('mt5ConnectionStatus'),
    startTradingBtn: document.getElementById('startTradingBtn'),
    enableEnhancedFeatures: document.getElementById('enableEnhancedFeatures')
};

console.log("📋 DOM元素检查:");
for (const [name, element] of Object.entries(elements)) {
    if (element) {
        console.log(`✅ ${name}: 存在`);
    } else {
        console.log(`❌ ${name}: 缺失`);
    }
}

// 2. 检查模型选择下拉框选项
const modelSelect = elements.tradingModelSelect;
if (modelSelect) {
    console.log(`📋 模型选择选项数量: ${modelSelect.options.length}`);
    if (modelSelect.options.length > 1) {
        console.log("✅ 模型选择有选项");
        for (let i = 1; i < Math.min(modelSelect.options.length, 4); i++) {
            console.log(`   ${i}. ${modelSelect.options[i].text}`);
        }
    } else {
        console.log("❌ 模型选择没有选项");
    }
}

// 3. 检查MT5连接状态
const mt5Status = elements.mt5ConnectionStatus;
if (mt5Status) {
    console.log(`📋 MT5连接状态: ${mt5Status.textContent}`);
    console.log(`📋 MT5状态样式: ${mt5Status.className}`);
}

// 4. 测试关键函数是否可调用
console.log("🔧 测试关键函数:");
try {
    if (typeof loadTradingModels === 'function') {
        console.log("✅ loadTradingModels函数可用");
    } else {
        console.log("❌ loadTradingModels函数不可用");
    }
    
    if (typeof checkMT5Connection === 'function') {
        console.log("✅ checkMT5Connection函数可用");
    } else {
        console.log("❌ checkMT5Connection函数不可用");
    }
} catch (error) {
    console.log("❌ 函数测试出错:", error);
}

console.log("🎯 测试完成！请检查上述结果。");
'''
        
        with open('browser_test.js', 'w', encoding='utf-8') as f:
            f.write(browser_test_js)
        
        print("✅ 浏览器测试脚本已创建: browser_test.js")
        print("📋 使用方法:")
        print("   1. 打开AI推理交易页面")
        print("   2. 按F12打开开发者工具")
        print("   3. 在Console标签中粘贴browser_test.js的内容并执行")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def create_manual_test_guide():
    """创建手动测试指南"""
    print(f"\n📋 创建手动测试指南...")
    
    guide = '''# AI推理交易功能手动测试指南

## 🎯 测试目标
验证AI推理交易页面的模型选择和MT5连接功能是否正常工作。

## 📋 测试步骤

### 1. 页面访问测试
- [ ] 访问: http://127.0.0.1:5000/deep-learning/inference
- [ ] 页面正常加载，无明显错误
- [ ] 页面布局完整，所有区域都可见

### 2. 模型选择功能测试
- [ ] 找到"交易配置"区域中的"交易模型"下拉框
- [ ] 下拉框中应该有模型选项（不只是"请选择交易模型..."）
- [ ] 选择一个模型后，下方应显示模型信息
- [ ] 模型信息应包含模型名称、类型、交易品种等

### 3. MT5连接功能测试
- [ ] 页面顶部应显示MT5连接状态（绿色"已连接"或红色"未连接"）
- [ ] 点击"检查连接"按钮，状态应该更新
- [ ] 如果未连接，点击"自动连接"按钮尝试连接
- [ ] 连接成功后状态应变为绿色"MT5已连接"

### 4. 浏览器控制台检查
- [ ] 按F12打开开发者工具
- [ ] 切换到Console标签
- [ ] 检查是否有红色错误信息
- [ ] 特别注意是否有JavaScript错误

### 5. 功能交互测试
- [ ] 选择模型后，其他配置选项应该可以正常设置
- [ ] 增强特征开关应该可以切换
- [ ] 各种交易参数应该可以输入和修改

## 🔧 故障排除

### 如果模型选择没有选项：
1. 检查浏览器控制台是否有错误
2. 手动刷新页面 (Ctrl+F5)
3. 检查是否有训练完成的模型
4. 在控制台执行: `loadTradingModels()`

### 如果MT5连接失败：
1. 确保MT5软件已启动
2. 检查MT5设置中的"允许自动交易"
3. 检查防火墙设置
4. 在控制台执行: `checkMT5Connection()`

### 如果页面功能异常：
1. 清除浏览器缓存
2. 尝试无痕模式
3. 检查网络连接
4. 重启应用服务器

## 📞 报告问题
如果发现问题，请提供：
1. 具体的错误现象描述
2. 浏览器控制台的错误信息截图
3. 页面显示的截图
4. 使用的浏览器类型和版本
'''
    
    with open('manual_test_guide.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("✅ 手动测试指南已创建: manual_test_guide.md")

if __name__ == "__main__":
    success = test_realtime_functionality()
    create_manual_test_guide()
    
    if success:
        print("\n🎉 实时功能测试完成！")
        print("📋 所有API和功能都正常工作")
        print("🔍 如果页面仍有问题，请按照手动测试指南进行检查")
    else:
        print("\n❌ 实时功能测试发现问题")
        print("🔧 请检查上述错误信息")
