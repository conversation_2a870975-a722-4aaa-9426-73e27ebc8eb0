#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的深度学习模型训练配置
包含各种优化配置示例
"""

# 基础增强配置
ENHANCED_BASIC_CONFIG = {
    "model_type": "lstm",
    "sequence_length": 60,
    "hidden_size": 128,
    "num_layers": 2,
    "dropout": 0.2,
    "epochs": 100,
    "batch_size": 32,
    "learning_rate": 0.001,
    "weight_decay": 0.01,
    "patience": 20,
    "min_epochs": 30,
    
    # 增强特征配置
    "use_enhanced_features": True,
    "analyze_feature_importance": True,
    "selected_features": None,  # 使用推荐特征集
    
    # 风险管理配置
    "enable_dynamic_risk_management": True,
    "atr_multiplier": 2.0,
    "min_stop_pips": 10,
    "risk_per_trade": 0.02,
    
    # 特征工程配置
    "feature_engineering": {
        "bollinger_bands": {
            "period": 20,
            "std_dev": 2.0,
            "enable_squeeze_detection": True,
            "enable_breakout_signals": True
        },
        "atr": {
            "period": 14,
            "enable_volatility_classification": True,
            "enable_dynamic_stops": True
        },
        "stochastic": {
            "k_period": 14,
            "d_period": 3,
            "enable_overbought_oversold": True,
            "enable_crossover_signals": True
        },
        "combined_signals": {
            "enable_confluence_analysis": True,
            "enable_confirmation_signals": True
        },
        "market_regime": {
            "enable_trend_analysis": True,
            "enable_volatility_regime": True,
            "enable_momentum_analysis": True
        }
    }
}

# 高级Transformer配置
ENHANCED_TRANSFORMER_CONFIG = {
    "model_type": "transformer",
    "sequence_length": 120,  # 更长的序列用于Transformer
    "hidden_size": 256,      # 更大的隐藏层
    "num_layers": 4,
    "nhead": 8,
    "dropout": 0.1,
    "epochs": 150,
    "batch_size": 16,        # 较小的批次大小
    "learning_rate": 0.0005, # 较小的学习率
    "weight_decay": 0.01,
    "patience": 25,
    "min_epochs": 50,
    
    # 增强特征配置
    "use_enhanced_features": True,
    "analyze_feature_importance": True,
    "selected_features": [
        # 布林带核心特征
        'bb_percent_b', 'bb_band_width', 'bb_squeeze', 'bb_breakout',
        'bb_distance_from_middle', 'bb_percent_b_change', 'bb_band_width_change',
        
        # ATR核心特征
        'atr_atr', 'atr_ratio', 'atr_percentile', 'atr_change',
        'atr_low_volatility', 'atr_high_volatility',
        
        # 随机指标核心特征
        'stoch_stoch_k', 'stoch_stoch_d', 'stoch_k_d_diff', 'stoch_k_change',
        'stoch_overbought', 'stoch_oversold', 'stoch_golden_cross', 'stoch_death_cross',
        
        # 组合信号
        'combined_squeeze_low_vol', 'combined_breakout_confirmed',
        'combined_oversold_confirmed', 'combined_overbought_confirmed',
        'combined_bullish_confluence', 'combined_bearish_confluence',
        'combined_breakout_setup',
        
        # 市场状态
        'market_trend_strength', 'market_trending_market', 'market_ranging_market',
        'market_trend_direction', 'market_market_efficiency',
        
        # 价格特征
        'close_returns', 'high_returns', 'low_returns',
        'close_position', 'price_gap',
        
        # 动量特征
        'momentum_5', 'momentum_10', 'momentum_20', 
        'price_acceleration', 'trend_strength'
    ],
    
    # 学习率调度配置
    "scheduler_config": {
        "type": "cosine_annealing",
        "warmup_steps_ratio": 0.1,
        "min_lr_ratio": 0.01
    }
}

# CNN-LSTM混合配置
ENHANCED_CNN_LSTM_CONFIG = {
    "model_type": "cnn_lstm",
    "sequence_length": 80,
    "hidden_size": 128,
    "num_layers": 2,
    "dropout": 0.3,
    "epochs": 120,
    "batch_size": 24,
    "learning_rate": 0.001,
    "weight_decay": 0.015,
    "patience": 20,
    "min_epochs": 40,
    
    # 增强特征配置
    "use_enhanced_features": True,
    "analyze_feature_importance": True,
    
    # CNN特定配置
    "cnn_config": {
        "kernel_sizes": [3, 5, 7],
        "num_filters": 64,
        "pool_size": 2
    },
    
    # 特征选择策略
    "feature_selection_strategy": "importance_based",
    "max_features": 30,  # 限制特征数量以避免过拟合
    
    # 数据增强
    "data_augmentation": {
        "enable_noise_injection": True,
        "noise_level": 0.01,
        "enable_time_warping": True,
        "warp_ratio": 0.1
    }
}

# 注意力机制LSTM配置
ENHANCED_ATTENTION_LSTM_CONFIG = {
    "model_type": "attention_lstm",
    "sequence_length": 100,
    "hidden_size": 192,
    "num_layers": 3,
    "dropout": 0.25,
    "epochs": 100,
    "batch_size": 20,
    "learning_rate": 0.0008,
    "weight_decay": 0.012,
    "patience": 18,
    "min_epochs": 35,
    
    # 增强特征配置
    "use_enhanced_features": True,
    "analyze_feature_importance": True,
    
    # 注意力机制配置
    "attention_config": {
        "attention_size": 64,
        "enable_self_attention": True,
        "num_attention_heads": 4
    },
    
    # 高级特征工程
    "advanced_feature_engineering": {
        "enable_feature_interaction": True,
        "enable_polynomial_features": False,  # 谨慎使用，可能导致过拟合
        "enable_feature_scaling": True,
        "scaling_method": "robust"  # 对异常值更鲁棒
    }
}

# 集成学习配置
ENHANCED_ENSEMBLE_CONFIG = {
    "ensemble_type": "voting",
    "models": [
        {
            "model_type": "lstm",
            "weight": 0.3,
            "config": ENHANCED_BASIC_CONFIG
        },
        {
            "model_type": "transformer", 
            "weight": 0.4,
            "config": ENHANCED_TRANSFORMER_CONFIG
        },
        {
            "model_type": "attention_lstm",
            "weight": 0.3,
            "config": ENHANCED_ATTENTION_LSTM_CONFIG
        }
    ],
    
    # 集成特定配置
    "ensemble_config": {
        "enable_stacking": True,
        "meta_learner": "linear_regression",
        "cross_validation_folds": 5
    }
}

# 生产环境配置
PRODUCTION_CONFIG = {
    "model_type": "transformer",
    "sequence_length": 60,
    "hidden_size": 128,
    "num_layers": 2,
    "nhead": 8,
    "dropout": 0.1,
    "epochs": 50,  # 较少的训练轮次以节省时间
    "batch_size": 32,
    "learning_rate": 0.001,
    "weight_decay": 0.01,
    "patience": 10,
    "min_epochs": 20,
    
    # 增强特征配置（精简版）
    "use_enhanced_features": True,
    "analyze_feature_importance": False,  # 生产环境中关闭以节省时间
    "selected_features": [
        # 只使用最重要的特征
        'bb_percent_b', 'bb_squeeze', 'bb_breakout',
        'atr_atr', 'atr_ratio', 'atr_low_volatility', 'atr_high_volatility',
        'stoch_stoch_k', 'stoch_overbought', 'stoch_oversold',
        'combined_squeeze_low_vol', 'combined_breakout_confirmed',
        'market_trend_strength', 'market_trend_direction',
        'close_returns', 'momentum_10', 'trend_strength'
    ],
    
    # 性能优化
    "performance_optimization": {
        "enable_mixed_precision": True,
        "enable_gradient_checkpointing": False,
        "enable_model_compilation": True
    },
    
    # 监控配置
    "monitoring": {
        "enable_performance_tracking": True,
        "enable_feature_drift_detection": True,
        "alert_thresholds": {
            "accuracy_drop": 0.05,
            "feature_drift_score": 0.3
        }
    }
}

# 研究实验配置
RESEARCH_CONFIG = {
    "model_type": "transformer",
    "sequence_length": 200,  # 很长的序列用于研究
    "hidden_size": 512,      # 很大的模型
    "num_layers": 6,
    "nhead": 16,
    "dropout": 0.1,
    "epochs": 300,           # 很多训练轮次
    "batch_size": 8,         # 小批次大小
    "learning_rate": 0.0001, # 很小的学习率
    "weight_decay": 0.01,
    "patience": 50,
    "min_epochs": 100,
    
    # 完整的增强特征
    "use_enhanced_features": True,
    "analyze_feature_importance": True,
    "selected_features": None,  # 使用所有特征
    
    # 实验性功能
    "experimental_features": {
        "enable_feature_selection_during_training": True,
        "enable_adaptive_learning_rate": True,
        "enable_curriculum_learning": True,
        "enable_adversarial_training": False
    },
    
    # 详细的日志和分析
    "logging": {
        "log_level": "DEBUG",
        "save_intermediate_results": True,
        "enable_tensorboard": True,
        "save_attention_weights": True
    }
}

def get_config_by_name(config_name: str):
    """根据名称获取配置"""
    configs = {
        "basic": ENHANCED_BASIC_CONFIG,
        "transformer": ENHANCED_TRANSFORMER_CONFIG,
        "cnn_lstm": ENHANCED_CNN_LSTM_CONFIG,
        "attention_lstm": ENHANCED_ATTENTION_LSTM_CONFIG,
        "ensemble": ENHANCED_ENSEMBLE_CONFIG,
        "production": PRODUCTION_CONFIG,
        "research": RESEARCH_CONFIG
    }
    
    return configs.get(config_name, ENHANCED_BASIC_CONFIG)

def print_config_summary(config_name: str):
    """打印配置摘要"""
    config = get_config_by_name(config_name)
    
    print(f"\n📋 {config_name.upper()} 配置摘要:")
    print(f"   模型类型: {config['model_type']}")
    print(f"   序列长度: {config['sequence_length']}")
    print(f"   隐藏层大小: {config['hidden_size']}")
    print(f"   训练轮次: {config['epochs']}")
    print(f"   使用增强特征: {config.get('use_enhanced_features', False)}")
    
    if config.get('selected_features'):
        print(f"   选择特征数量: {len(config['selected_features'])}")
    else:
        print(f"   特征选择: 使用推荐特征集")

if __name__ == "__main__":
    print("🔧 增强的深度学习模型训练配置")
    print("="*50)
    
    configs = ["basic", "transformer", "cnn_lstm", "attention_lstm", "production", "research"]
    
    for config_name in configs:
        print_config_summary(config_name)
