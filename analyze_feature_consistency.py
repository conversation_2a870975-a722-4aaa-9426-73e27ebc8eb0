#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析特征选择策略一致性
检查模型训练、推理回测、推理交易中的特征配置是否一致
"""

import re
import os

def extract_feature_strategies_from_file(file_path, file_type):
    """从文件中提取特征选择策略配置"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        strategies = {}
        
        # 查找特征选择策略的选项
        if file_type == 'training':
            # 模型训练页面
            pattern = r'<option value="([^"]+)"[^>]*>([^<]+)</option>'
            matches = re.findall(pattern, content)
            
            for value, text in matches:
                if 'feature' in text.lower() or '特征' in text:
                    strategies[value] = text.strip()
        
        elif file_type == 'backtest':
            # 模型回测页面
            pattern = r'<option value="([^"]+)"[^>]*>([^<]+)</option>'
            matches = re.findall(pattern, content)
            
            for value, text in matches:
                if 'feature' in text.lower() or '特征' in text:
                    strategies[value] = text.strip()
        
        elif file_type == 'inference':
            # 模型推理页面
            # 查找JavaScript中的策略定义
            strategy_pattern = r"'([^']+)':\s*'([^']+)'"
            matches = re.findall(strategy_pattern, content)
            
            for value, text in matches:
                if 'feature' in text.lower() or '特征' in text:
                    strategies[value] = text.strip()
        
        return strategies
        
    except Exception as e:
        print(f"❌ 读取文件 {file_path} 失败: {e}")
        return {}

def analyze_feature_consistency():
    """分析特征选择策略一致性"""
    print('🔍 分析特征选择策略一致性')
    print('=' * 60)
    
    # 文件路径配置
    files_to_check = {
        'training': 'templates/model_training.html',
        'backtest': 'templates/model_backtest.html',
        'inference': 'templates/model_inference.html'
    }
    
    all_strategies = {}
    
    # 提取每个文件的特征策略
    for file_type, file_path in files_to_check.items():
        if os.path.exists(file_path):
            print(f"\n📋 检查 {file_type} 页面: {file_path}")
            strategies = extract_feature_strategies_from_file(file_path, file_type)
            all_strategies[file_type] = strategies
            
            if strategies:
                print(f"   找到 {len(strategies)} 个特征策略:")
                for value, text in strategies.items():
                    print(f"     {value}: {text}")
            else:
                print(f"   ❌ 未找到特征策略配置")
        else:
            print(f"❌ 文件不存在: {file_path}")
            all_strategies[file_type] = {}
    
    return all_strategies

def check_strategy_consistency(all_strategies):
    """检查策略一致性"""
    print('\n🔍 检查策略一致性')
    print('=' * 60)
    
    # 获取所有唯一的策略值
    all_strategy_values = set()
    for file_type, strategies in all_strategies.items():
        all_strategy_values.update(strategies.keys())
    
    print(f"📊 发现的所有策略值: {sorted(all_strategy_values)}")
    
    # 检查每个策略在所有文件中的一致性
    inconsistencies = []
    
    for strategy_value in sorted(all_strategy_values):
        print(f"\n🔍 检查策略 '{strategy_value}':")
        
        strategy_texts = {}
        missing_files = []
        
        for file_type, strategies in all_strategies.items():
            if strategy_value in strategies:
                strategy_texts[file_type] = strategies[strategy_value]
                print(f"   {file_type}: {strategies[strategy_value]}")
            else:
                missing_files.append(file_type)
                print(f"   {file_type}: ❌ 缺失")
        
        # 检查文本描述是否一致
        if len(set(strategy_texts.values())) > 1:
            inconsistencies.append({
                'strategy': strategy_value,
                'issue': 'description_mismatch',
                'details': strategy_texts
            })
            print(f"   ⚠️ 描述不一致")
        
        # 检查是否有文件缺失该策略
        if missing_files:
            inconsistencies.append({
                'strategy': strategy_value,
                'issue': 'missing_in_files',
                'details': missing_files
            })
            print(f"   ⚠️ 在以下文件中缺失: {missing_files}")
    
    return inconsistencies

def analyze_backend_feature_implementation():
    """分析后端特征实现"""
    print('\n🔍 分析后端特征实现')
    print('=' * 60)
    
    backend_files = [
        'services/deep_learning_service.py',
        'services/feature_engineering.py'
    ]
    
    feature_strategies_found = {}
    
    for file_path in backend_files:
        if os.path.exists(file_path):
            print(f"\n📋 检查后端文件: {file_path}")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找特征选择策略的实现
                strategy_patterns = [
                    r"feature_selection_strategy.*?['\"]([^'\"]+)['\"]",
                    r"['\"]([^'\"]*(?:recommended|all|top|custom)[^'\"]*)['\"]",
                    r"def.*feature.*strategy.*\(.*\):",
                ]
                
                found_strategies = set()
                for pattern in strategy_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    found_strategies.update(matches)
                
                if found_strategies:
                    feature_strategies_found[file_path] = list(found_strategies)
                    print(f"   找到策略实现: {sorted(found_strategies)}")
                else:
                    print(f"   ❌ 未找到策略实现")
                    
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print(f"❌ 文件不存在: {file_path}")
    
    return feature_strategies_found

def provide_consistency_recommendations(inconsistencies):
    """提供一致性建议"""
    print('\n🔧 一致性建议')
    print('=' * 60)
    
    if not inconsistencies:
        print("✅ 未发现明显的一致性问题")
        return
    
    print(f"❌ 发现 {len(inconsistencies)} 个一致性问题:")
    
    for i, issue in enumerate(inconsistencies, 1):
        print(f"\n{i}. 策略 '{issue['strategy']}':")
        print(f"   问题类型: {issue['issue']}")
        
        if issue['issue'] == 'description_mismatch':
            print(f"   描述不一致:")
            for file_type, description in issue['details'].items():
                print(f"     {file_type}: {description}")
            
            print(f"   建议: 统一所有页面中的描述文本")
        
        elif issue['issue'] == 'missing_in_files':
            print(f"   缺失文件: {issue['details']}")
            print(f"   建议: 在缺失的文件中添加该策略选项")

def create_unified_feature_strategy_config():
    """创建统一的特征策略配置"""
    print('\n🔧 创建统一的特征策略配置')
    print('=' * 60)
    
    # 推荐的统一配置
    unified_config = {
        'recommended': {
            'value': 'recommended',
            'display_name': '推荐特征集 (26个核心特征)',
            'description': '经过优化的核心技术指标组合',
            'feature_count': 26,
            'includes': ['布林带', 'ATR', '随机指标', 'RSI', 'MACD', '移动平均', '价格动量']
        },
        'enhanced': {
            'value': 'enhanced',
            'display_name': '增强特征集 (52个特征)',
            'description': '包含所有可用的技术指标和组合信号',
            'feature_count': 52,
            'includes': ['所有基础指标', '组合信号', '市场结构', '波动率指标']
        },
        'top_importance': {
            'value': 'top_importance',
            'display_name': '重要性前15个特征',
            'description': '基于特征重要性分析的前15个特征',
            'feature_count': 15,
            'includes': ['最重要的技术指标', '高预测能力特征']
        },
        'minimal': {
            'value': 'minimal',
            'display_name': '最小特征集 (10个特征)',
            'description': '用于快速训练和测试的最小特征集',
            'feature_count': 10,
            'includes': ['基础价格指标', '核心技术指标']
        },
        'custom': {
            'value': 'custom',
            'display_name': '自定义特征选择',
            'description': '手动选择特定的技术指标组合',
            'feature_count': '可变',
            'includes': ['用户自定义选择']
        }
    }
    
    print("📋 推荐的统一特征策略配置:")
    for strategy_id, config in unified_config.items():
        print(f"\n🎯 {config['display_name']}:")
        print(f"   值: {config['value']}")
        print(f"   描述: {config['description']}")
        print(f"   特征数量: {config['feature_count']}")
        print(f"   包含: {', '.join(config['includes'])}")
    
    # 生成HTML选项代码
    print(f"\n📝 HTML选项代码:")
    print("```html")
    for strategy_id, config in unified_config.items():
        print(f'<option value="{config["value"]}">{config["display_name"]}</option>')
    print("```")
    
    # 生成JavaScript映射代码
    print(f"\n📝 JavaScript映射代码:")
    print("```javascript")
    print("const strategyNames = {")
    for strategy_id, config in unified_config.items():
        print(f"    '{config['value']}': '{config['display_name']}',")
    print("};")
    print("```")
    
    return unified_config

def main():
    """主函数"""
    print('🔧 特征选择策略一致性分析')
    print('=' * 80)
    
    # 分析前端页面的特征策略
    all_strategies = analyze_feature_consistency()
    
    # 检查策略一致性
    inconsistencies = check_strategy_consistency(all_strategies)
    
    # 分析后端实现
    backend_strategies = analyze_backend_feature_implementation()
    
    # 提供一致性建议
    provide_consistency_recommendations(inconsistencies)
    
    # 创建统一配置
    unified_config = create_unified_feature_strategy_config()
    
    print(f"\n🎯 总结")
    print('=' * 80)
    print(f"✅ 您的观察完全正确！")
    print(f"📊 发现的问题:")
    print(f"   - 不同页面的特征策略选项可能不一致")
    print(f"   - 策略描述文本可能有差异")
    print(f"   - 某些页面可能缺少特定策略选项")
    
    print(f"\n💡 建议:")
    print(f"   1. 统一所有页面的特征策略选项")
    print(f"   2. 使用相同的策略值和描述文本")
    print(f"   3. 确保后端实现支持所有策略")
    print(f"   4. 添加特征数量和类型的详细说明")
    
    print(f"\n🚀 关键要点:")
    print(f"   - 模型训练时的特征选择必须与推理时一致")
    print(f"   - 特征数量和类型的不匹配会导致模型加载失败")
    print(f"   - 建议使用统一的特征策略配置文件")

if __name__ == "__main__":
    main()
