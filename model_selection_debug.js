
// AI推理交易模型选择调试脚本
console.log("🔧 开始调试AI推理交易模型选择...");

// 1. 检查DOM元素
const tradingModelSelect = document.getElementById('tradingModelSelect');
const tradingModelInfo = document.getElementById('tradingModelInfo');

console.log("📋 DOM元素检查:");
console.log("tradingModelSelect:", tradingModelSelect);
console.log("tradingModelInfo:", tradingModelInfo);

if (tradingModelSelect) {
    console.log("模型选择选项数量:", tradingModelSelect.options.length);
    console.log("当前选项:");
    for (let i = 0; i < tradingModelSelect.options.length; i++) {
        console.log(`  ${i}: ${tradingModelSelect.options[i].text}`);
    }
} else {
    console.log("❌ tradingModelSelect元素未找到");
}

// 2. 手动调用loadTradingModels函数
console.log("🔄 手动调用loadTradingModels...");
if (typeof loadTradingModels === 'function') {
    loadTradingModels().then(() => {
        console.log("✅ loadTradingModels调用完成");
        
        // 重新检查选项
        if (tradingModelSelect) {
            console.log("更新后的选项数量:", tradingModelSelect.options.length);
            for (let i = 0; i < tradingModelSelect.options.length; i++) {
                console.log(`  ${i}: ${tradingModelSelect.options[i].text}`);
            }
        }
    }).catch(error => {
        console.error("❌ loadTradingModels调用失败:", error);
    });
} else {
    console.log("❌ loadTradingModels函数不存在");
}

// 3. 检查API端点
console.log("🔍 测试模型API...");
fetch('/api/deep-learning/models')
    .then(response => response.json())
    .then(data => {
        console.log("模型API响应:", data);
        if (data.success) {
            const completed = data.models.filter(m => m.status === 'completed');
            console.log(`找到 ${completed.length} 个已完成的模型`);
            completed.forEach((model, i) => {
                console.log(`  ${i+1}. ${model.name} (${model.symbol}-${model.timeframe})`);
            });
        } else {
            console.error("模型API错误:", data.error);
        }
    })
    .catch(error => {
        console.error("模型API异常:", error);
    });

console.log("🎯 调试脚本执行完成！");
