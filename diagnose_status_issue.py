#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断模型训练状态不一致问题
"""

import sqlite3
import json
import os
from datetime import datetime

def check_database_exists():
    """检查数据库是否存在"""
    db_path = 'trading_system.db'
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    print(f"✅ 找到数据库文件: {db_path}")
    return True

def analyze_status_inconsistency():
    """分析状态不一致问题"""
    print("\n🔍 分析状态不一致问题")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查training_tasks表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='training_tasks'")
        if not cursor.fetchone():
            print("❌ training_tasks表不存在")
            conn.close()
            return
        
        print("✅ 找到training_tasks表")
        
        # 获取所有训练任务
        cursor.execute('''
            SELECT id, model_id, status, progress, logs, created_at, updated_at
            FROM training_tasks
            ORDER BY created_at DESC
        ''')
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("ℹ️ 没有找到任何训练任务")
            conn.close()
            return
        
        print(f"📊 找到 {len(tasks)} 个训练任务")
        print("\n" + "=" * 80)
        
        inconsistent_tasks = []
        
        for task in tasks:
            task_id, model_id, status, progress, logs, created_at, updated_at = task
            
            print(f"\n📋 任务ID: {task_id[:8]}...")
            print(f"   模型ID: {model_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            
            # 分析日志
            log_stage = None
            log_message = None
            
            if logs:
                try:
                    log_data = json.loads(logs)
                    log_stage = log_data.get('stage')
                    log_message = log_data.get('message')
                    print(f"   日志阶段: {log_stage}")
                    print(f"   日志消息: {log_message}")
                except json.JSONDecodeError:
                    print(f"   日志解析失败: {logs[:100]}...")
            else:
                print("   无日志信息")
            
            # 检查状态不一致
            is_inconsistent = False
            issue_description = ""
            
            # 情况1：状态为running但日志显示data_ready
            if status == 'running' and log_stage == 'data_ready':
                is_inconsistent = True
                issue_description = "状态为running但日志显示data_ready"
            
            # 情况2：进度100%但状态不是data_ready或completed
            elif progress >= 100 and status not in ['data_ready', 'completed']:
                is_inconsistent = True
                issue_description = f"进度{progress}%但状态为{status}"
            
            # 情况3：数据准备阶段进度很高但状态不正确
            elif log_stage == 'data_preparation' and progress >= 90 and status != 'data_ready':
                is_inconsistent = True
                issue_description = f"数据准备进度{progress}%但状态为{status}"
            
            if is_inconsistent:
                print(f"   ⚠️ 状态不一致: {issue_description}")
                inconsistent_tasks.append({
                    'task_id': task_id,
                    'model_id': model_id,
                    'status': status,
                    'progress': progress,
                    'log_stage': log_stage,
                    'issue': issue_description
                })
            else:
                print("   ✅ 状态一致")
        
        conn.close()
        
        # 总结
        print("\n" + "=" * 80)
        print(f"📊 诊断结果:")
        print(f"   总任务数: {len(tasks)}")
        print(f"   状态不一致任务数: {len(inconsistent_tasks)}")
        
        if inconsistent_tasks:
            print(f"\n⚠️ 发现 {len(inconsistent_tasks)} 个状态不一致的任务:")
            for task in inconsistent_tasks:
                print(f"   - {task['task_id'][:8]}...: {task['issue']}")
        else:
            print("\n✅ 所有任务状态一致")
        
        return inconsistent_tasks
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        return []

def check_deep_learning_models():
    """检查深度学习模型表状态"""
    print("\n🔍 检查深度学习模型表")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查deep_learning_models表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='deep_learning_models'")
        if not cursor.fetchone():
            print("❌ deep_learning_models表不存在")
            conn.close()
            return
        
        print("✅ 找到deep_learning_models表")
        
        # 获取所有模型
        cursor.execute('''
            SELECT id, name, status, created_at
            FROM deep_learning_models
            ORDER BY created_at DESC
            LIMIT 10
        ''')
        
        models = cursor.fetchall()
        
        if not models:
            print("ℹ️ 没有找到任何深度学习模型")
        else:
            print(f"📊 找到 {len(models)} 个深度学习模型:")
            for model in models:
                model_id, name, status, created_at = model
                print(f"   - {model_id[:8]}...: {name} ({status}) - {created_at}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查模型表失败: {e}")

def main():
    """主函数"""
    print("🔍 模型训练状态不一致问题诊断工具")
    print("=" * 60)
    
    # 1. 检查数据库
    if not check_database_exists():
        return
    
    # 2. 分析状态不一致
    inconsistent_tasks = analyze_status_inconsistency()
    
    # 3. 检查模型表
    check_deep_learning_models()
    
    # 4. 提供修复建议
    if inconsistent_tasks:
        print("\n🔧 修复建议:")
        print("1. 运行状态修复工具自动修复这些问题")
        print("2. 检查数据准备完成后的状态更新逻辑")
        print("3. 改进前端状态检查机制")
    else:
        print("\n✅ 当前没有发现状态不一致问题")

if __name__ == "__main__":
    main()
