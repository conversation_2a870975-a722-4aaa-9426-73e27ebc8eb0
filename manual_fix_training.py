#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""手动修复训练进度脚本"""

import sqlite3
from datetime import datetime

def manual_fix_progress():
    """手动修复训练进度"""
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找卡住的任务
        cursor.execute("""
            SELECT id, model_name, progress 
            FROM training_tasks 
            WHERE status = 'running' 
            AND datetime(updated_at) < datetime('now', '-3 minutes')
        """)
        
        stuck_tasks = cursor.fetchall()
        
        if not stuck_tasks:
            print("✅ 没有发现卡住的任务")
            return
        
        print(f"🔴 发现 {len(stuck_tasks)} 个卡住的任务:")
        
        for task_id, model_name, progress in stuck_tasks:
            print(f"\n📊 任务: {model_name}")
            print(f"   当前进度: {progress}%")
            
            # 强制更新进度
            new_progress = min(progress + 5, 95)
            
            cursor.execute("""
                UPDATE training_tasks 
                SET progress = ?, updated_at = ?
                WHERE id = ?
            """, (new_progress, datetime.now().isoformat(), task_id))
            
            print(f"   ✅ 更新进度: {progress}% -> {new_progress}%")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ 手动修复完成")
        
    except Exception as e:
        print(f"❌ 手动修复失败: {e}")

if __name__ == "__main__":
    manual_fix_progress()
