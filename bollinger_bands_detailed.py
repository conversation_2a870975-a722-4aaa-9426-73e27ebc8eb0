#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布林带在深度学习模型中的详细应用
展示布林带的计算、特征工程和模型输入过程
"""

import numpy as np
import pandas as pd
from services.technical_indicators import TechnicalIndicators
import matplotlib.pyplot as plt

class BollingerBandsForML:
    """布林带机器学习应用类"""
    
    def __init__(self):
        self.ti = TechnicalIndicators()
    
    def calculate_bollinger_bands_detailed(self, prices, period=20, std_multiplier=2):
        """
        详细计算布林带及相关特征
        这是深度学习模型中最重要的技术指标之一
        """
        print(f"📈 详细计算布林带 (周期={period}, 标准差倍数={std_multiplier})")
        print("=" * 60)
        
        # 转换为pandas Series便于计算
        price_series = pd.Series(prices)
        
        # 1. 计算中轨 (简单移动平均)
        middle_band = price_series.rolling(window=period).mean()
        print(f"✅ 中轨 (SMA{period}) 计算完成")
        
        # 2. 计算标准差
        rolling_std = price_series.rolling(window=period).std()
        print(f"✅ {period}期标准差计算完成")
        
        # 3. 计算上轨和下轨
        upper_band = middle_band + (rolling_std * std_multiplier)
        lower_band = middle_band - (rolling_std * std_multiplier)
        print(f"✅ 上轨和下轨计算完成")
        
        # 4. 计算布林带宽度 (Band Width)
        band_width = (upper_band - lower_band) / middle_band
        print(f"✅ 布林带宽度计算完成")
        
        # 5. 计算%B指标 (价格在布林带中的位置)
        percent_b = (price_series - lower_band) / (upper_band - lower_band)
        print(f"✅ %B指标计算完成")
        
        # 6. 计算布林带挤压指标 (Bollinger Band Squeeze)
        # 当带宽处于历史低位时，表示市场即将出现大幅波动
        bb_squeeze = band_width < band_width.rolling(window=20).quantile(0.2)
        print(f"✅ 布林带挤压指标计算完成")
        
        # 7. 计算价格与中轨的距离
        distance_from_middle = (price_series - middle_band) / middle_band
        print(f"✅ 价格与中轨距离计算完成")
        
        # 8. 计算布林带突破信号
        upper_breakout = price_series > upper_band
        lower_breakout = price_series < lower_band
        print(f"✅ 突破信号计算完成")
        
        result = {
            'price': price_series,
            'upper_band': upper_band,
            'middle_band': middle_band,
            'lower_band': lower_band,
            'band_width': band_width,
            'percent_b': percent_b,
            'bb_squeeze': bb_squeeze,
            'distance_from_middle': distance_from_middle,
            'upper_breakout': upper_breakout,
            'lower_breakout': lower_breakout
        }
        
        # 统计信息
        print(f"\n📊 布林带统计信息:")
        print(f"   上轨突破次数: {upper_breakout.sum()} ({upper_breakout.mean()*100:.1f}%)")
        print(f"   下轨突破次数: {lower_breakout.sum()} ({lower_breakout.mean()*100:.1f}%)")
        print(f"   平均%B值: {percent_b.mean():.3f}")
        print(f"   %B标准差: {percent_b.std():.3f}")
        print(f"   挤压状态次数: {bb_squeeze.sum()} ({bb_squeeze.mean()*100:.1f}%)")
        
        return result
    
    def create_bollinger_features_for_model(self, bb_data):
        """
        为深度学习模型创建布林带特征
        这些特征将作为模型的输入
        """
        print(f"\n🤖 创建布林带模型特征")
        print("-" * 40)
        
        features = {}
        
        # 1. 标准化的%B值 (最重要的特征)
        percent_b = bb_data['percent_b'].fillna(0.5)  # 缺失值用0.5填充
        features['bb_percent_b'] = percent_b
        print(f"✅ %B特征: 范围 {percent_b.min():.3f} - {percent_b.max():.3f}")
        
        # 2. 标准化的带宽
        band_width = bb_data['band_width'].fillna(0)
        band_width_normalized = (band_width - band_width.mean()) / (band_width.std() + 1e-8)
        features['bb_width_norm'] = band_width_normalized
        print(f"✅ 带宽特征: 范围 {band_width_normalized.min():.3f} - {band_width_normalized.max():.3f}")
        
        # 3. 价格与中轨距离
        distance = bb_data['distance_from_middle'].fillna(0)
        features['bb_distance'] = distance
        print(f"✅ 距离特征: 范围 {distance.min():.3f} - {distance.max():.3f}")
        
        # 4. 布林带挤压状态 (二进制特征)
        squeeze = bb_data['bb_squeeze'].astype(int)
        features['bb_squeeze'] = squeeze
        print(f"✅ 挤压特征: 挤压状态占比 {squeeze.mean()*100:.1f}%")
        
        # 5. 突破信号 (组合特征)
        breakout_signal = np.where(bb_data['upper_breakout'], 1, 
                                 np.where(bb_data['lower_breakout'], -1, 0))
        features['bb_breakout'] = breakout_signal
        print(f"✅ 突破特征: 上突破 {(breakout_signal==1).sum()}, 下突破 {(breakout_signal==-1).sum()}")
        
        # 6. %B的变化率 (动量特征)
        percent_b_change = percent_b.diff().fillna(0)
        features['bb_percent_b_change'] = percent_b_change
        print(f"✅ %B变化特征: 范围 {percent_b_change.min():.3f} - {percent_b_change.max():.3f}")
        
        # 7. 布林带位置分类 (离散特征)
        bb_position = np.where(percent_b < 0.2, 0,      # 下轨附近
                              np.where(percent_b > 0.8, 2, 1))  # 上轨附近 vs 中间
        features['bb_position_class'] = bb_position
        position_dist = pd.Series(bb_position).value_counts(normalize=True)
        print(f"✅ 位置分类: 下轨{position_dist.get(0, 0)*100:.1f}%, 中间{position_dist.get(1, 0)*100:.1f}%, 上轨{position_dist.get(2, 0)*100:.1f}%")
        
        return features
    
    def demonstrate_model_input_format(self, bb_features, sequence_length=60):
        """
        演示布林带特征如何输入到LSTM模型
        """
        print(f"\n🔄 演示LSTM模型输入格式 (序列长度={sequence_length})")
        print("-" * 50)
        
        # 将所有特征组合成矩阵
        feature_names = list(bb_features.keys())
        feature_matrix = np.column_stack([bb_features[name] for name in feature_names])
        
        print(f"📊 特征矩阵信息:")
        print(f"   形状: {feature_matrix.shape}")
        print(f"   特征列表: {feature_names}")
        
        # 创建LSTM序列
        sequences = []
        targets = []
        
        for i in range(sequence_length, len(feature_matrix)):
            # 输入序列：过去sequence_length个时间步的布林带特征
            sequence = feature_matrix[i-sequence_length:i]
            sequences.append(sequence)
            
            # 目标：基于布林带信号的交易决策
            current_percent_b = bb_features['bb_percent_b'].iloc[i-1]
            next_percent_b = bb_features['bb_percent_b'].iloc[i]
            
            # 简单的交易策略：
            # 1: 买入信号 (%B从低位上升)
            # 0: 卖出信号 (%B从高位下降)
            if current_percent_b < 0.2 and next_percent_b > current_percent_b:
                target = 1  # 买入
            elif current_percent_b > 0.8 and next_percent_b < current_percent_b:
                target = 0  # 卖出
            else:
                target = 0.5  # 持有
            
            targets.append(target)
        
        X = np.array(sequences)
        y = np.array(targets)
        
        print(f"\n🎯 LSTM输入输出:")
        print(f"   输入形状: {X.shape} (样本数, 序列长度, 特征数)")
        print(f"   输出形状: {y.shape}")
        print(f"   买入信号: {(y==1).sum()} ({(y==1).mean()*100:.1f}%)")
        print(f"   卖出信号: {(y==0).sum()} ({(y==0).mean()*100:.1f}%)")
        print(f"   持有信号: {(y==0.5).sum()} ({(y==0.5).mean()*100:.1f}%)")
        
        return X, y
    
    def show_bollinger_trading_signals(self, bb_data):
        """
        展示基于布林带的交易信号
        """
        print(f"\n📈 布林带交易信号分析")
        print("-" * 40)
        
        prices = bb_data['price']
        percent_b = bb_data['percent_b']
        band_width = bb_data['band_width']
        
        # 经典布林带交易策略
        signals = []
        
        for i in range(1, len(prices)):
            signal = "持有"
            
            # 策略1: %B反转信号
            if percent_b.iloc[i-1] < 0.1 and percent_b.iloc[i] > 0.1:
                signal = "买入 (下轨反弹)"
            elif percent_b.iloc[i-1] > 0.9 and percent_b.iloc[i] < 0.9:
                signal = "卖出 (上轨回落)"
            
            # 策略2: 挤压突破
            elif (bb_data['bb_squeeze'].iloc[i-1] and 
                  not bb_data['bb_squeeze'].iloc[i] and 
                  percent_b.iloc[i] > 0.5):
                signal = "买入 (挤压突破向上)"
            elif (bb_data['bb_squeeze'].iloc[i-1] and 
                  not bb_data['bb_squeeze'].iloc[i] and 
                  percent_b.iloc[i] < 0.5):
                signal = "卖出 (挤压突破向下)"
            
            signals.append(signal)
        
        # 统计信号分布
        signal_counts = pd.Series(signals).value_counts()
        print(f"📊 交易信号统计:")
        for signal, count in signal_counts.items():
            print(f"   {signal}: {count} 次 ({count/len(signals)*100:.1f}%)")
        
        return signals

def main():
    """主演示函数"""
    print("🎯 布林带深度学习应用详细演示")
    print("=" * 60)
    
    # 创建示例价格数据
    np.random.seed(42)
    base_price = 2000
    price_changes = np.random.normal(0, 10, 200)
    prices = [base_price]
    
    for change in price_changes:
        new_price = prices[-1] + change
        prices.append(max(new_price, 1800))
    
    prices = np.array(prices[1:])
    
    # 创建布林带分析器
    bb_analyzer = BollingerBandsForML()
    
    # 1. 计算布林带
    bb_data = bb_analyzer.calculate_bollinger_bands_detailed(prices)
    
    # 2. 创建模型特征
    bb_features = bb_analyzer.create_bollinger_features_for_model(bb_data)
    
    # 3. 演示模型输入
    X, y = bb_analyzer.demonstrate_model_input_format(bb_features)
    
    # 4. 展示交易信号
    signals = bb_analyzer.show_bollinger_trading_signals(bb_data)
    
    print(f"\n🎉 布林带演示完成！")
    print(f"📋 关键要点:")
    print(f"   • %B指标是最重要的布林带特征")
    print(f"   • 布林带挤压预示着即将到来的波动")
    print(f"   • 结合多个布林带特征可以提高预测准确性")
    print(f"   • 特征标准化对模型训练至关重要")

if __name__ == "__main__":
    main()
