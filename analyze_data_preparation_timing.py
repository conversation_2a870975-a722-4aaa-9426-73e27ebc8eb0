#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析数据准备和训练启动的时序问题
检查是否数据没有完全准备好就开始训练了
"""

import sqlite3
import json
from datetime import datetime
import os

def analyze_data_preparation_timing():
    """分析数据准备和训练启动的时序"""
    print('🔍 分析数据准备和训练启动时序问题')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询最近的任务，包括data_ready和running状态
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs
            FROM training_tasks 
            WHERE status IN ('data_ready', 'running', 'failed')
            ORDER BY created_at DESC
            LIMIT 10
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("❌ 没有找到相关任务")
            return
        
        print(f"📊 分析最近 {len(tasks)} 个任务的时序:")
        
        for i, task in enumerate(tasks, 1):
            task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs = task
            
            print(f"\n任务 {i}: {task_id[:8]}...")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            
            # 分析时序问题
            if logs:
                try:
                    if isinstance(logs, str):
                        log_data = json.loads(logs)
                    else:
                        log_data = logs
                    
                    stage = log_data.get('stage', 'unknown')
                    message = log_data.get('message', 'N/A')
                    
                    print(f"   当前阶段: {stage}")
                    print(f"   最新消息: {message}")
                    
                    # 检查数据准备相关信息
                    if 'data_info' in log_data:
                        data_info = log_data['data_info']
                        print(f"   数据信息: {data_info}")
                        
                        # 检查数据形状是否合理
                        if 'X_train_shape' in data_info:
                            X_train_shape = data_info['X_train_shape']
                            print(f"   训练数据形状: {X_train_shape}")
                            
                            if len(X_train_shape) != 3:
                                print(f"   ⚠️ 训练数据形状异常！应该是3维")
                            elif X_train_shape[0] == 0:
                                print(f"   ⚠️ 训练数据为空！")
                            elif X_train_shape[1] == 0 or X_train_shape[2] == 0:
                                print(f"   ⚠️ 训练数据维度异常！")
                            else:
                                print(f"   ✅ 训练数据形状正常")
                    
                    # 分析时序问题
                    if status == 'running' and stage == 'model_training' and progress == 25.0:
                        print(f"   🚨 发现时序问题:")
                        print(f"      - 状态显示running但可能数据准备未完成")
                        print(f"      - 进度卡在25%，可能在等待数据")
                        
                        # 检查是否有数据准备完成的标志
                        if 'data_info' not in log_data:
                            print(f"      - ❌ 缺少data_info，数据可能未准备完成")
                        else:
                            print(f"      - ✅ 有data_info，数据应该已准备完成")
                    
                except Exception as e:
                    print(f"   ❌ 日志解析失败: {e}")
            
            # 检查时间间隔
            if created_at and updated_at:
                try:
                    created_time = datetime.fromisoformat(created_at)
                    updated_time = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    time_diff = updated_time.replace(tzinfo=None) - created_time
                    
                    print(f"   时间间隔: {time_diff}")
                    
                    if status == 'running' and time_diff.total_seconds() < 30:
                        print(f"   ⚠️ 可能启动过快！数据准备可能未完成")
                    elif status == 'data_ready' and time_diff.total_seconds() > 300:
                        print(f"   ⚠️ 数据准备时间过长，可能有问题")
                        
                except Exception as e:
                    print(f"   ❌ 时间分析失败: {e}")
        
    except Exception as e:
        print(f"❌ 时序分析失败: {e}")

def check_data_preparation_process():
    """检查数据准备过程的完整性"""
    print('\n🔍 检查数据准备过程完整性')
    print('=' * 60)
    
    # 检查数据文件是否存在
    data_files_to_check = [
        'data/processed_data.pkl',
        'data/training_data.pkl',
        'data/features.pkl',
        'models/temp_data.pkl'
    ]
    
    print("📁 检查数据文件:")
    for file_path in data_files_to_check:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} (大小: {file_size/1024:.1f} KB)")
        else:
            print(f"   ❌ {file_path} (不存在)")
    
    # 检查临时文件
    temp_dirs = ['temp', 'cache', 'models']
    print(f"\n📁 检查临时目录:")
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            files = os.listdir(temp_dir)
            print(f"   📂 {temp_dir}/ ({len(files)} 个文件)")
            for file in files[:3]:  # 只显示前3个
                print(f"      - {file}")
            if len(files) > 3:
                print(f"      ... 还有 {len(files)-3} 个文件")
        else:
            print(f"   ❌ {temp_dir}/ (不存在)")

def analyze_auto_start_mechanism():
    """分析自动启动机制"""
    print('\n🔍 分析自动启动机制')
    print('=' * 60)
    
    print("🔧 检查自动启动逻辑:")
    print("   当前系统的自动启动流程:")
    print("   1. 数据准备完成 → 状态更新为 data_ready")
    print("   2. 检查 auto_train 标志")
    print("   3. 如果启用自动启动 → 延迟2秒后启动模型训练")
    print("   4. 调用 start_model_training() 方法")
    
    print(f"\n🚨 可能的时序问题:")
    print(f"   1. 数据准备未完全完成就标记为 data_ready")
    print(f"   2. 自动启动延迟时间太短 (2秒)")
    print(f"   3. 数据写入磁盘可能还在进行中")
    print(f"   4. 特征计算可能还在后台运行")
    print(f"   5. 内存中的数据可能还在处理中")

def check_deep_learning_service_timing():
    """检查深度学习服务的时序逻辑"""
    print('\n🔍 检查深度学习服务时序逻辑')
    print('=' * 60)
    
    try:
        # 读取深度学习服务代码，查找时序相关逻辑
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("📋 关键时序代码片段:")
        
        # 查找自动启动相关代码
        if 'auto_train' in content:
            print("   ✅ 找到 auto_train 自动启动逻辑")
        
        if 'data_ready' in content:
            print("   ✅ 找到 data_ready 状态处理")
        
        if 'Timer' in content:
            print("   ✅ 找到 Timer 延迟启动机制")
        
        # 查找可能的时序问题
        timing_issues = []
        
        if 'time.sleep(0.5)' in content:
            timing_issues.append("发现0.5秒延迟，可能太短")
        
        if 'time.sleep(2.0)' in content:
            timing_issues.append("发现2秒延迟，可能仍然太短")
        
        if 'threading.Timer' in content:
            timing_issues.append("使用Timer异步启动，可能存在竞态条件")
        
        if timing_issues:
            print(f"\n⚠️ 发现潜在时序问题:")
            for issue in timing_issues:
                print(f"   - {issue}")
        else:
            print(f"\n✅ 未发现明显的时序问题")
            
    except Exception as e:
        print(f"❌ 代码检查失败: {e}")

def provide_timing_solutions():
    """提供时序问题解决方案"""
    print('\n🔧 时序问题解决方案')
    print('=' * 60)
    
    print("🎯 立即解决方案:")
    print("1. 禁用自动启动训练:")
    print("   - 在训练配置中设置 auto_start_training: false")
    print("   - 手动确认数据准备完成后再启动训练")
    
    print("\n2. 增加数据准备验证:")
    print("   - 在标记 data_ready 前验证数据完整性")
    print("   - 检查数据文件大小和格式")
    print("   - 验证特征计算是否完成")
    
    print("\n3. 延长自动启动延迟:")
    print("   - 将延迟从2秒增加到10-15秒")
    print("   - 给数据准备更多时间完成")
    
    print("\n🔧 代码修复建议:")
    print("4. 添加数据准备完成检查:")
    print("   - 在启动训练前验证数据文件存在")
    print("   - 检查数据形状和内容")
    print("   - 确保所有特征计算完成")
    
    print("\n5. 改进状态管理:")
    print("   - 添加 'data_preparing' 中间状态")
    print("   - 只有在数据完全准备好后才标记 'data_ready'")
    print("   - 添加数据准备进度的细分状态")
    
    print("\n📊 监控改进:")
    print("6. 增强数据准备监控:")
    print("   - 实时显示数据准备的具体步骤")
    print("   - 显示特征计算进度")
    print("   - 提供数据验证结果")

def create_timing_fix_config():
    """创建修复时序问题的配置"""
    print('\n⚙️ 修复时序问题的训练配置')
    print('=' * 60)
    
    timing_fix_config = {
        'model_name': '时序修复测试',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'data_config': {'mode': 'days', 'training_days': 30},
        'sequence_length': 10,
        'hidden_size': 16,
        'num_layers': 1,
        'dropout': 0.2,
        'batch_size': 4,
        'learning_rate': 0.001,
        'epochs': 10,
        'patience': 3,
        'early_stopping': True,
        'min_epochs': 2,
        'use_gpu': False,
        'save_checkpoints': True,
        'use_enhanced_features': True,
        'feature_selection_strategy': 'minimal',  # 使用最小特征集
        'include_basic_features': True,
        'analyze_feature_importance': False,
        'auto_start_training': False,  # 关键：禁用自动启动
        'data_preparation_timeout': 300,  # 数据准备超时时间
        'verify_data_before_training': True  # 训练前验证数据
    }
    
    print("📋 时序修复配置:")
    for key, value in timing_fix_config.items():
        if key in ['auto_start_training', 'data_preparation_timeout', 'verify_data_before_training']:
            print(f"   🔧 {key}: {value}")
        else:
            print(f"   {key}: {value}")
    
    print(f"\n💡 关键修复点:")
    print(f"   - auto_start_training: false (禁用自动启动)")
    print(f"   - feature_selection_strategy: minimal (减少特征计算时间)")
    print(f"   - analyze_feature_importance: false (跳过耗时分析)")
    print(f"   - 手动控制训练启动时机")

def main():
    """主函数"""
    print('🔧 数据准备和训练启动时序问题分析')
    print('=' * 80)
    
    # 分析时序问题
    analyze_data_preparation_timing()
    
    # 检查数据准备过程
    check_data_preparation_process()
    
    # 分析自动启动机制
    analyze_auto_start_mechanism()
    
    # 检查服务时序逻辑
    check_deep_learning_service_timing()
    
    # 提供解决方案
    provide_timing_solutions()
    
    # 创建修复配置
    create_timing_fix_config()
    
    print(f"\n✅ 时序问题分析完成！")
    print(f"💡 关键发现:")
    print(f"   - 用户分析正确：可能数据没有完全准备好就开始训练了")
    print(f"   - 自动启动机制可能存在竞态条件")
    print(f"   - 建议禁用自动启动，手动控制训练时机")

if __name__ == "__main__":
    main()
