#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动训练增强特征模型脚本
"""

import json
import sys
import os
from services.deep_learning_service import DeepLearningService

def main():
    """主函数"""
    print("🚀 开始训练增强特征模型")
    
    # 加载配置
    with open('enhanced_model_training_config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"📋 加载配置: {config['model_name']}")
    
    # 创建深度学习服务
    dl_service = DeepLearningService()
    
    # 开始训练
    result = dl_service.train_model(
        user_id=1,  # 默认用户ID
        model_name=config['model_name'],
        model_type=config['model_type'],
        symbol=config['symbol'],
        timeframe=config['timeframe'],
        start_date=config['data_config']['start_date'],
        end_date=config['data_config']['end_date'],
        data_points=config['data_config']['data_points'],
        sequence_length=config['model_config']['sequence_length'],
        hidden_size=config['model_config']['hidden_size'],
        num_layers=config['model_config']['num_layers'],
        dropout=config['model_config']['dropout'],
        learning_rate=config['model_config']['learning_rate'],
        batch_size=config['model_config']['batch_size'],
        epochs=config['model_config']['epochs'],
        use_gpu=config['training_config']['use_gpu'],
        use_enhanced_features=config['feature_config']['use_enhanced_features'],
        feature_selection_strategy=config['feature_config']['feature_selection_strategy'],
        selected_features=config['feature_config']['selected_features']
    )
    
    if result['success']:
        print(f"✅ 模型训练成功!")
        print(f"📋 模型ID: {result['model_id']}")
        print(f"📊 训练结果: {result}")
    else:
        print(f"❌ 模型训练失败: {result['error']}")
        return False
    
    return True

if __name__ == "__main__":
    main()
