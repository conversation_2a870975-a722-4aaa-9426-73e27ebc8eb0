#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特征策略修复
"""

import requests
import json

def test_feature_strategy_fix():
    """测试特征策略修复"""
    
    print("🧪 测试特征策略修复")
    print("=" * 60)
    
    # 模型ID（使用minimal策略训练的模型）
    model_id = "ffc738c7-0452-4de6-826e-fb9b6fe5ebe2"
    model_name = "xau-0804-LSTM-增强-1Y-10技术-高精度"
    
    print(f"📋 测试模型:")
    print(f"   ID: {model_id}")
    print(f"   名称: {model_name}")
    print(f"   训练策略: minimal (期望18个特征)")
    
    # 测试回测
    print(f"\n📊 测试回测...")
    print("-" * 40)
    
    try:
        backtest_data = {
            'model_id': model_id,
            'symbol': 'XAUUSD',
            'timeframe': '1h',
            'days': 7,
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 50,
            'take_profit_pips': 100,
            'min_confidence': 0.3,
            'use_enhanced_features': True,
            'feature_selection_strategy': 'recommended'  # 故意使用不同的策略
        }
        
        print(f"📋 回测参数:")
        print(f"   用户指定策略: {backtest_data['feature_selection_strategy']}")
        print(f"   期望行为: 系统应该自动使用训练时的minimal策略")
        
        # 发送回测请求
        response = requests.post('http://localhost:5000/api/backtest', json=backtest_data, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                stats = result.get('statistics', {})
                trades = result.get('trades', [])
                
                print(f"✅ 回测成功!")
                print(f"   交易数量: {len(trades)}")
                print(f"   总收益: {stats.get('total_return', 0):.2f}%")
                print(f"   胜率: {stats.get('win_rate', 0):.1f}%")
                
                # 检查是否使用了正确的特征策略
                if len(trades) > 0:
                    sample_trade = trades[0]
                    feature_strategy = sample_trade.get('feature_strategy')
                    feature_count = sample_trade.get('feature_count')
                    
                    print(f"   📊 实际使用的特征策略: {feature_strategy}")
                    print(f"   📊 实际特征数量: {feature_count}")
                    
                    if feature_strategy == 'minimal' and feature_count == 18:
                        print(f"   ✅ 特征策略修复成功！系统自动使用了训练时的策略")
                    else:
                        print(f"   ⚠️ 特征策略可能仍有问题")
                
                return True
            else:
                error_msg = result.get('error', 'Unknown error')
                print(f"❌ 回测失败: {error_msg}")
                
                # 检查是否还有特征维度不匹配错误
                if "特征维度不匹配" in error_msg:
                    print(f"🚨 仍然存在特征维度不匹配问题!")
                elif "'>' not supported between instances of 'str' and 'float'" in error_msg:
                    print(f"🚨 仍然存在置信度类型比较错误!")
                else:
                    print(f"💡 这是其他类型的错误")
                
                return False
        else:
            print(f"❌ 回测请求失败: HTTP {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ 回测测试失败: {e}")
        return False

def test_inference_fix():
    """测试推理修复"""
    
    print(f"\n📊 测试推理修复...")
    print("-" * 40)
    
    try:
        model_id = "ffc738c7-0452-4de6-826e-fb9b6fe5ebe2"
        
        inference_data = {
            'model_id': model_id,
            'use_enhanced_features': True,
            'feature_selection_strategy': 'all'  # 故意使用不同的策略
        }
        
        print(f"📋 推理参数:")
        print(f"   用户指定策略: {inference_data['feature_selection_strategy']}")
        print(f"   期望行为: 系统应该自动使用训练时的minimal策略")
        
        response = requests.post('http://localhost:5000/api/inference', json=inference_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                results = result.get('results', [])
                if results:
                    pred = results[0]
                    print(f"✅ 推理成功")
                    print(f"   预测: {pred.get('prediction')}")
                    print(f"   置信度: {pred.get('confidence', 0):.3f}")
                    print(f"   当前价格: {pred.get('current_price', 0):.5f}")
                    
                    # 检查特征信息
                    feature_strategy = pred.get('feature_strategy')
                    feature_count = pred.get('feature_count')
                    
                    if feature_strategy:
                        print(f"   📊 使用的特征策略: {feature_strategy}")
                    if feature_count:
                        print(f"   📊 特征数量: {feature_count}")
                    
                    return True
                else:
                    print("❌ 推理成功但无结果")
                    return False
            else:
                print(f"❌ 推理失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 推理请求失败: HTTP {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🚀 特征策略和置信度类型修复测试")
    print("=" * 80)
    
    # 测试回测修复
    backtest_success = test_feature_strategy_fix()
    
    # 测试推理修复
    inference_success = test_inference_fix()
    
    print(f"\n🎉 测试总结:")
    print(f"   回测修复: {'✅ 成功' if backtest_success else '❌ 失败'}")
    print(f"   推理修复: {'✅ 成功' if inference_success else '❌ 失败'}")
    
    if backtest_success and inference_success:
        print(f"\n✅ 所有修复都成功!")
        print(f"💡 现在系统会自动使用训练时的特征策略，避免维度不匹配")
    else:
        print(f"\n⚠️ 部分修复可能需要进一步调整")

if __name__ == "__main__":
    main()
