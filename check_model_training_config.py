#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查模型训练配置和特征策略
"""

import sqlite3
import json

def check_model_training_config():
    """检查模型训练配置"""
    
    print("🔍 检查模型训练配置和特征策略")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找增强特征模型
        cursor.execute('''
            SELECT id, name, config, status, created_at
            FROM deep_learning_models
            WHERE name LIKE '%增强%' OR config LIKE '%enhanced%'
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        models = cursor.fetchall()
        
        if not models:
            print("❌ 没有找到增强特征模型")
            return False
        
        print(f"✅ 找到 {len(models)} 个增强特征模型:")
        
        for i, (model_id, name, config_str, status, created_at) in enumerate(models, 1):
            print(f"\n📊 模型 {i}:")
            print(f"   ID: {model_id}")
            print(f"   名称: {name}")
            print(f"   状态: {status}")
            print(f"   创建时间: {created_at}")
            
            # 解析配置
            if config_str:
                try:
                    config = json.loads(config_str)
                    print(f"   📋 训练配置:")
                    
                    # 检查关键配置
                    use_enhanced = config.get('use_enhanced_features', False)
                    strategy = config.get('feature_selection_strategy', 'unknown')
                    selected_features = config.get('selected_features', None)
                    
                    print(f"      使用增强特征: {use_enhanced}")
                    print(f"      特征策略: {strategy}")
                    print(f"      自定义特征: {selected_features}")
                    
                    # 检查模型架构
                    model_type = config.get('model_type', 'unknown')
                    hidden_size = config.get('hidden_size', 'unknown')
                    num_layers = config.get('num_layers', 'unknown')
                    input_size = config.get('input_size', 'unknown')
                    
                    print(f"      模型类型: {model_type}")
                    print(f"      输入特征数: {input_size}")
                    print(f"      隐藏层大小: {hidden_size}")
                    print(f"      层数: {num_layers}")
                    
                    # 分析特征策略
                    if strategy == 'minimal':
                        expected_features = 8 + 10  # 基础 + 最小增强
                        print(f"      ✅ 期望特征数: {expected_features} (8基础 + 10最小增强)")
                    elif strategy == 'recommended':
                        expected_features = 8 + 26  # 基础 + 推荐增强
                        print(f"      ✅ 期望特征数: {expected_features} (8基础 + 26推荐增强)")
                    elif strategy == 'all' or strategy == 'enhanced':
                        expected_features = 8 + 52  # 基础 + 全部增强
                        print(f"      ✅ 期望特征数: {expected_features} (8基础 + 52全部增强)")
                    else:
                        expected_features = 'unknown'
                        print(f"      ❓ 未知策略，无法确定期望特征数")
                    
                    # 检查是否匹配
                    if input_size != 'unknown' and expected_features != 'unknown':
                        if input_size == expected_features:
                            print(f"      ✅ 特征数量匹配")
                        else:
                            print(f"      ❌ 特征数量不匹配: 实际{input_size} vs 期望{expected_features}")
                    
                except Exception as e:
                    print(f"   ❌ 配置解析失败: {e}")
                    print(f"   原始配置: {config_str[:200]}...")
            else:
                print(f"   ⚠️ 没有配置信息")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_feature_strategy_mapping():
    """检查特征策略映射"""
    
    print(f"\n🔍 检查特征策略映射")
    print("=" * 60)
    
    # 定义策略映射
    strategy_mapping = {
        'minimal': {
            'name': '最小特征集',
            'enhanced_features': 10,
            'total_features': 18,  # 8基础 + 10增强
            'description': '快速训练的最小特征集'
        },
        'recommended': {
            'name': '推荐特征集',
            'enhanced_features': 26,
            'total_features': 34,  # 8基础 + 26增强
            'description': '经过优化的核心技术指标组合'
        },
        'all': {
            'name': '全部特征集',
            'enhanced_features': 52,
            'total_features': 60,  # 8基础 + 52增强
            'description': '包含所有可用的技术指标'
        },
        'enhanced': {
            'name': '增强特征集',
            'enhanced_features': 52,
            'total_features': 60,  # 8基础 + 52增强
            'description': '与all策略相同'
        }
    }
    
    print("📋 特征策略映射表:")
    print("-" * 80)
    print(f"{'策略':<12} {'名称':<16} {'增强特征':<8} {'总特征':<8} {'描述':<20}")
    print("-" * 80)
    
    for strategy, info in strategy_mapping.items():
        print(f"{strategy:<12} {info['name']:<16} {info['enhanced_features']:<8} {info['total_features']:<8} {info['description']:<20}")
    
    print(f"\n💡 问题分析:")
    print(f"   1. 如果模型训练时使用 'minimal' 策略，期望18个特征")
    print(f"   2. 如果回测时使用 'all' 策略，会计算60个特征")
    print(f"   3. 这会导致特征维度不匹配，系统会截取前18个特征")
    print(f"   4. 但截取的特征可能与训练时的特征不同，影响预测准确性")

def main():
    """主函数"""
    
    print("🚀 模型训练配置和特征策略检查")
    print("=" * 80)
    
    # 检查模型配置
    if check_model_training_config():
        # 检查特征策略映射
        check_feature_strategy_mapping()
        
        print(f"\n🔧 修复建议:")
        print(f"   1. 确认模型训练时使用的特征策略")
        print(f"   2. 回测时使用相同的特征策略")
        print(f"   3. 或者修改代码自动检测并使用正确的策略")
    else:
        print(f"\n❌ 检查失败")

if __name__ == "__main__":
    main()
