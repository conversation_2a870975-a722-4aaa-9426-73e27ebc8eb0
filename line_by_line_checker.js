
// 逐行JavaScript语法检查器
console.log("🔍 开始逐行语法检查...");

// 获取页面源码
const pageSource = document.documentElement.outerHTML;
const lines = pageSource.split('\n');

console.log(`📄 页面总行数: ${lines.length}`);

// 查找JavaScript代码块
let inScriptBlock = false;
let scriptLines = [];
let scriptStartLine = 0;

for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    if (line.includes('<script') && !line.includes('src=')) {
        inScriptBlock = true;
        scriptStartLine = i + 1;
        console.log(`📋 发现JavaScript代码块开始于第${scriptStartLine}行`);
        continue;
    }
    
    if (line.includes('</script>')) {
        inScriptBlock = false;
        console.log(`📋 JavaScript代码块结束于第${i + 1}行`);
        
        // 检查这个代码块的语法
        const scriptCode = scriptLines.join('\n');
        try {
            // 尝试创建函数来检查语法
            new Function(scriptCode);
            console.log(`✅ 第${scriptStartLine}-${i + 1}行的JavaScript语法正确`);
        } catch (error) {
            console.error(`❌ 第${scriptStartLine}-${i + 1}行的JavaScript语法错误:`, error.message);
            
            // 尝试找到具体的错误行
            const errorLines = scriptCode.split('\n');
            errorLines.forEach((errorLine, index) => {
                if (errorLine.includes('null.') || 
                    errorLine.includes('undefined.') ||
                    /===.*=(?!=)/.test(errorLine) ||
                    /==.*=(?!=)/.test(errorLine)) {
                    console.error(`   可能的问题行 ${scriptStartLine + index}: ${errorLine.trim()}`);
                }
            });
        }
        
        scriptLines = [];
        continue;
    }
    
    if (inScriptBlock) {
        scriptLines.push(line);
    }
}

// 特别检查常见的语法错误模式
console.log("🔍 检查常见语法错误模式:");

const errorPatterns = [
    { pattern: /null\.[a-zA-Z_]/, description: 'null对象属性访问' },
    { pattern: /undefined\.[a-zA-Z_]/, description: 'undefined对象属性访问' },
    { pattern: /===.*=(?!=)/, description: '三等号后面跟赋值' },
    { pattern: /==.*=(?!=)/, description: '双等号后面跟赋值' },
    { pattern: /true\s*=/, description: 'true字面量赋值' },
    { pattern: /false\s*=/, description: 'false字面量赋值' }
];

let foundErrors = false;
errorPatterns.forEach(({ pattern, description }) => {
    if (pattern.test(pageSource)) {
        console.error(`❌ 发现${description}错误`);
        foundErrors = true;
    }
});

if (!foundErrors) {
    console.log("✅ 未发现常见语法错误模式");
}

console.log("🎯 逐行语法检查完成");
