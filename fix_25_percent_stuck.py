#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""25%卡住紧急修复脚本"""

import sqlite3
import json
from datetime import datetime

def fix_25_percent_stuck():
    """修复25%卡住问题"""
    print("🚨 修复25%卡住问题...")
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找卡在25%的任务
        cursor.execute("""
            SELECT id, name, progress, current_epoch, total_epochs
            FROM training_tasks 
            WHERE status = 'running' 
            AND progress >= 20 AND progress <= 30
            AND datetime(updated_at) < datetime('now', '-2 minutes')
        """)
        
        stuck_tasks = cursor.fetchall()
        
        if not stuck_tasks:
            print("✅ 没有发现卡在25%的任务")
            return
        
        print(f"🔴 发现 {len(stuck_tasks)} 个卡在25%的任务，开始修复...")
        
        for task_id, name, progress, current_epoch, total_epochs in stuck_tasks:
            print(f"\n📊 修复任务: {name}")
            print(f"   当前进度: {progress}%")
            
            # 强制推进到30%
            new_progress = 30.0
            new_epoch = max(1, current_epoch)
            
            cursor.execute("""
                UPDATE training_tasks 
                SET progress = ?, current_epoch = ?, updated_at = ?,
                    logs = ?
                WHERE id = ?
            """, (
                new_progress, 
                new_epoch, 
                datetime.now().isoformat(),
                json.dumps({
                    "stage": "force_progress_update",
                    "message": f"强制推进25%卡住: {progress}% -> {new_progress}%",
                    "action": "emergency_fix_25_percent",
                    "timestamp": datetime.now().isoformat()
                }),
                task_id
            ))
            
            print(f"   ✅ 强制推进: {progress}% -> {new_progress}%")
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 25%卡住修复完成！")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    fix_25_percent_stuck()
