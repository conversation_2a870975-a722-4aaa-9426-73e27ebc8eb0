#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速检查训练状态
"""

import sqlite3

def quick_check():
    """快速检查"""
    
    print("🔍 快速检查训练状态")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查训练任务
        cursor.execute('''
            SELECT COUNT(*) FROM training_tasks
            WHERE status IN ('running', 'pending', 'data_preparation', 'data_ready')
        ''')
        running_count = cursor.fetchone()[0]
        
        print(f"📊 正在运行的训练任务: {running_count}")
        
        # 检查模型
        cursor.execute('''
            SELECT COUNT(*) FROM deep_learning_models
            WHERE status IN ('training', 'pending')
        ''')
        training_models = cursor.fetchone()[0]
        
        print(f"📊 正在训练的模型: {training_models}")
        
        # 检查总模型数
        cursor.execute('SELECT COUNT(*) FROM deep_learning_models')
        total_models = cursor.fetchone()[0]
        
        print(f"📊 总模型数: {total_models}")
        
        conn.close()
        
        if running_count == 0 and training_models == 0:
            print("✅ 系统状态正常，可以开始新的训练")
        else:
            print("⚠️ 仍有任务在运行")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    quick_check()
