#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试持仓修复结果
"""

import requests
import time

def test_positions_fix():
    """测试持仓修复结果"""
    print("🧪 测试持仓修复结果")
    print("=" * 50)
    
    # 等待应用启动
    time.sleep(5)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 获取AI推理交易页面
        print(f"\n🔍 获取AI推理交易页面...")
        inference_response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if inference_response.status_code != 200:
            print(f"❌ 页面访问失败: {inference_response.status_code}")
            return False
        
        content = inference_response.text
        print(f"✅ 页面访问成功，大小: {len(content):,} 字符")
        
        # 检查修复内容
        print(f"\n🔍 检查修复内容:")
        
        # 1. 检查变量初始化修复
        if 'let lastPositionsData = [];' in content:
            print("✅ lastPositionsData变量初始化已修复")
        else:
            print("❌ lastPositionsData变量初始化未修复")
        
        # 2. 检查安全检查代码
        if "typeof lastPositionsData === 'undefined'" in content:
            print("✅ 变量安全检查已添加")
        else:
            print("❌ 变量安全检查未添加")
        
        # 3. 检查refreshPositions函数
        if 'function refreshPositions()' in content:
            print("✅ refreshPositions函数存在")
        else:
            print("❌ refreshPositions函数缺失")
        
        # 4. 检查错误处理改进
        if 'refreshPositions(); // 重新获取持仓数据' in content:
            print("✅ 错误处理已改进")
        else:
            print("❌ 错误处理未改进")
        
        # 测试API端点
        print(f"\n🔍 测试API端点:")
        
        # 测试MT5连接状态
        mt5_response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        if mt5_response.status_code == 200:
            mt5_data = mt5_response.json()
            print(f"✅ MT5连接API正常: 连接状态 = {mt5_data.get('connected', False)}")
        else:
            print(f"❌ MT5连接API失败: {mt5_response.status_code}")
        
        # 测试持仓API
        positions_response = session.get('http://127.0.0.1:5000/api/mt5/positions')
        if positions_response.status_code == 200:
            positions_data = positions_response.json()
            if positions_data.get('success'):
                positions = positions_data.get('positions', [])
                print(f"✅ 持仓API正常: {len(positions)} 个持仓")
            else:
                print(f"❌ 持仓API错误: {positions_data.get('error')}")
        else:
            print(f"❌ 持仓API失败: {positions_response.status_code}")
        
        # 保存修复后的页面内容
        with open('positions_fixed_page.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("💾 修复后页面内容已保存到: positions_fixed_page.html")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def create_browser_test_script():
    """创建浏览器测试脚本"""
    print(f"\n🔧 创建浏览器测试脚本...")
    
    test_script = '''
// 持仓功能测试脚本
console.log("🧪 开始测试持仓功能...");

// 1. 检查变量初始化
console.log("📋 检查变量初始化:");
if (typeof lastPositionsData !== 'undefined') {
    console.log("✅ lastPositionsData变量已定义:", lastPositionsData);
} else {
    console.log("❌ lastPositionsData变量未定义");
}

// 2. 测试refreshPositions函数
console.log("📋 测试refreshPositions函数:");
if (typeof refreshPositions === 'function') {
    console.log("✅ refreshPositions函数存在");
    
    // 手动调用refreshPositions
    console.log("🔄 手动调用refreshPositions...");
    refreshPositions().then(() => {
        console.log("✅ refreshPositions调用成功");
    }).catch(error => {
        console.error("❌ refreshPositions调用失败:", error);
    });
} else {
    console.log("❌ refreshPositions函数不存在");
}

// 3. 检查持仓容器元素
console.log("📋 检查持仓容器元素:");
const container = document.getElementById('currentPositionsContainer');
if (container) {
    console.log("✅ 持仓容器元素存在");
    console.log("   当前内容:", container.innerHTML.substring(0, 100) + "...");
} else {
    console.log("❌ 持仓容器元素不存在");
}

// 4. 检查其他相关元素
const elements = [
    'positionsLoadingState',
    'positionsErrorState', 
    'noPositionsState'
];

elements.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ ${id}: 存在`);
    } else {
        console.log(`❌ ${id}: 不存在`);
    }
});

// 5. 测试MT5连接状态
console.log("📋 测试MT5连接状态:");
if (typeof checkMT5Connection === 'function') {
    console.log("🔄 检查MT5连接状态...");
    checkMT5Connection();
} else {
    console.log("❌ checkMT5Connection函数不存在");
}

// 6. 延迟检查结果
setTimeout(() => {
    console.log("🎯 测试结果总结:");
    
    // 检查是否还有错误
    const errorState = document.getElementById('positionsErrorState');
    if (errorState && errorState.style.display !== 'none') {
        console.log("⚠️ 持仓区域仍显示错误状态");
        console.log("   错误内容:", errorState.textContent);
    } else {
        console.log("✅ 持仓区域无错误状态");
    }
    
    // 检查当前显示内容
    const container = document.getElementById('currentPositionsContainer');
    if (container) {
        const content = container.textContent.trim();
        if (content.includes('网络连接异常')) {
            console.log("❌ 仍然显示网络连接异常");
        } else if (content.includes('当前无持仓')) {
            console.log("✅ 正常显示无持仓状态");
        } else if (content.includes('正在获取')) {
            console.log("🔄 正在加载持仓数据");
        } else {
            console.log("📋 持仓内容:", content.substring(0, 50) + "...");
        }
    }
    
    console.log("🎉 持仓功能测试完成！");
}, 3000);

console.log("⏳ 测试进行中，请等待3秒查看结果...");
'''
    
    with open('browser_positions_test.js', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 浏览器测试脚本已创建: browser_positions_test.js")
    print("📋 使用方法:")
    print("   1. 打开AI推理交易页面")
    print("   2. 按F12打开开发者工具")
    print("   3. 在Console标签中粘贴browser_positions_test.js的内容并执行")

if __name__ == "__main__":
    success = test_positions_fix()
    create_browser_test_script()
    
    if success:
        print("\n🎉 持仓修复测试完成！")
        print("📋 修复总结:")
        print("   1. ✅ 修复了lastPositionsData变量初始化问题")
        print("   2. ✅ 添加了变量安全检查")
        print("   3. ✅ 改进了错误处理逻辑")
        print("   4. ✅ 确保了函数调用的安全性")
        print("\n🔄 请刷新浏览器页面并使用测试脚本验证")
    else:
        print("\n❌ 持仓修复测试失败")
        print("🔧 请检查上述错误信息")
