#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试止损验证删除
验证"止损必须在10-500 pips之间"这个错误信息已经被删除
"""

import requests
from bs4 import BeautifulSoup
import re

def test_stop_loss_validation_removal():
    """测试止损验证删除"""
    print("🧪 测试止损验证删除")
    print("=" * 60)
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 2. 访问AI推理交易页面
        print("\n📋 访问AI推理交易页面...")
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code != 200:
            print(f"❌ 访问页面失败: {response.status_code}")
            return False
        
        print("✅ 页面访问成功")
        
        # 3. 检查页面源码中是否还包含错误信息
        page_content = response.text
        
        # 要检查的错误信息
        error_messages = [
            '止损必须在10-500 pips之间',
            'stop_loss_pips < 10',
            'stop_loss_pips > 500'
        ]
        
        print(f"\n🔍 检查页面源码中的止损验证...")
        
        found_errors = []
        for error_msg in error_messages:
            if error_msg in page_content:
                found_errors.append(error_msg)
        
        if found_errors:
            print("❌ 仍然找到以下错误信息:")
            for error in found_errors:
                print(f"   • {error}")
            
            # 显示错误信息的上下文
            for error in found_errors:
                matches = re.finditer(re.escape(error), page_content)
                for match in matches:
                    start = max(0, match.start() - 100)
                    end = min(len(page_content), match.end() + 100)
                    context = page_content[start:end]
                    print(f"\n上下文:")
                    print(f"...{context}...")
            
            return False
        else:
            print("✅ 未找到止损验证错误信息")
        
        # 4. 检查JavaScript函数中的验证逻辑
        print(f"\n🔧 检查JavaScript验证函数...")
        
        # 查找validateInferenceConfig函数
        inference_config_pattern = r'function validateInferenceConfig\([^}]+\}'
        inference_matches = re.findall(inference_config_pattern, page_content, re.DOTALL)
        
        if inference_matches:
            print("✅ 找到validateInferenceConfig函数")
            
            inference_func = inference_matches[0]
            if '止损必须在10-500 pips之间' in inference_func:
                print("❌ validateInferenceConfig函数中仍包含止损验证")
                print(f"函数内容: {inference_func[:200]}...")
                return False
            else:
                print("✅ validateInferenceConfig函数中已删除止损验证")
        else:
            print("❌ 未找到validateInferenceConfig函数")
            return False
        
        # 查找validateBacktestConfig函数
        backtest_config_pattern = r'function validateBacktestConfig\([^}]+\}'
        backtest_matches = re.findall(backtest_config_pattern, page_content, re.DOTALL)
        
        if backtest_matches:
            print("✅ 找到validateBacktestConfig函数")
            
            backtest_func = backtest_matches[0]
            if '止损必须在10-500 pips之间' in backtest_func:
                print("❌ validateBacktestConfig函数中仍包含止损验证")
                print(f"函数内容: {backtest_func[:200]}...")
                return False
            else:
                print("✅ validateBacktestConfig函数中已删除止损验证")
        else:
            print("❌ 未找到validateBacktestConfig函数")
            return False
        
        # 5. 检查其他可能的验证位置
        print(f"\n🔍 检查其他可能的验证位置...")
        
        # 检查是否还有其他地方包含类似的验证
        validation_patterns = [
            r'stop_loss_pips.*<.*10',
            r'stop_loss_pips.*>.*500',
            r'止损.*10.*500.*pips',
            r'showError.*止损.*pips'
        ]
        
        other_validations = []
        for pattern in validation_patterns:
            matches = re.findall(pattern, page_content, re.IGNORECASE)
            if matches:
                other_validations.extend(matches)
        
        if other_validations:
            print("⚠️ 发现其他可能的止损验证:")
            for validation in other_validations:
                print(f"   • {validation}")
        else:
            print("✅ 未发现其他止损验证")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_stop_loss_functionality():
    """测试止损功能是否仍然正常工作"""
    print(f"\n🧪 测试止损功能...")
    
    # 这里可以添加实际的功能测试
    # 比如提交一个大于500 pips的止损值，看是否还会报错
    
    print("💡 建议手动测试:")
    print("1. 访问AI推理交易页面")
    print("2. 点击'AI推理配置'按钮")
    print("3. 设置止损为1000 pips (大于500)")
    print("4. 点击'开始推理'")
    print("5. 确认不再出现'止损必须在10-500 pips之间'的错误")
    
    return True

def main():
    """主函数"""
    print("🔧 止损验证删除测试工具")
    print("=" * 80)
    
    print("📋 测试内容:")
    print("• 检查页面源码中是否还包含错误信息")
    print("• 检查JavaScript验证函数")
    print("• 查找其他可能的验证位置")
    print("• 提供功能测试建议")
    print()
    
    success = test_stop_loss_validation_removal()
    
    if success:
        print("\n🎉 止损验证删除测试通过！")
        print("✅ 已成功删除以下内容:")
        print("   • validateInferenceConfig函数中的止损范围验证")
        print("   • validateBacktestConfig函数中的止损范围验证")
        print("   • '❌ 止损必须在10-500 pips之间'错误信息")
        print()
        print("💡 现在用户可以:")
        print("   • 设置任意大小的止损值")
        print("   • 不再受到10-500 pips的限制")
        print("   • 根据实际需要配置止损参数")
        
        # 测试功能
        test_stop_loss_functionality()
        
    else:
        print("\n❌ 止损验证删除测试失败")
        print("🔧 请检查是否还有其他地方包含相关验证")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
