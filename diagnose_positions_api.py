#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断持仓API问题
"""

import requests
import time

def diagnose_positions_api():
    """诊断持仓API问题"""
    print("🔍 诊断AI推理交易持仓API问题")
    print("=" * 50)
    
    # 等待应用启动
    time.sleep(3)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 1. 测试MT5连接状态API
        print(f"\n🔍 1. 测试MT5连接状态API...")
        try:
            mt5_status_response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
            if mt5_status_response.status_code == 200:
                mt5_data = mt5_status_response.json()
                print(f"✅ MT5连接状态API正常")
                print(f"   连接状态: {mt5_data.get('connected', False)}")
                print(f"   成功标志: {mt5_data.get('success', False)}")
                if not mt5_data.get('connected', False):
                    print(f"   错误信息: {mt5_data.get('error', '未知错误')}")
            else:
                print(f"❌ MT5连接状态API失败: {mt5_status_response.status_code}")
                return False
        except Exception as e:
            print(f"❌ MT5连接状态API异常: {e}")
            return False
        
        # 2. 测试MT5持仓API
        print(f"\n🔍 2. 测试MT5持仓API...")
        try:
            positions_response = session.get('http://127.0.0.1:5000/api/mt5/positions')
            print(f"   HTTP状态码: {positions_response.status_code}")
            print(f"   响应头: {dict(positions_response.headers)}")
            
            if positions_response.status_code == 200:
                try:
                    positions_data = positions_response.json()
                    print(f"✅ MT5持仓API响应正常")
                    print(f"   成功标志: {positions_data.get('success', False)}")
                    
                    if positions_data.get('success', False):
                        positions = positions_data.get('positions', [])
                        print(f"   持仓数量: {len(positions)}")
                        
                        if positions:
                            print(f"   持仓详情:")
                            for i, pos in enumerate(positions[:3]):  # 只显示前3个
                                print(f"     {i+1}. 票号:{pos.get('ticket')} 品种:{pos.get('symbol')} "
                                      f"手数:{pos.get('volume')} 盈亏:{pos.get('profit', 0):.2f}")
                        else:
                            print(f"   📋 当前无持仓")
                    else:
                        error_msg = positions_data.get('error', '未知错误')
                        print(f"❌ MT5持仓API返回错误: {error_msg}")
                        return False
                        
                except Exception as json_error:
                    print(f"❌ 解析持仓API响应JSON失败: {json_error}")
                    print(f"   原始响应: {positions_response.text[:200]}...")
                    return False
            else:
                print(f"❌ MT5持仓API HTTP错误: {positions_response.status_code}")
                print(f"   响应内容: {positions_response.text[:200]}...")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ MT5持仓API网络异常: {e}")
            return False
        except Exception as e:
            print(f"❌ MT5持仓API未知异常: {e}")
            return False
        
        # 3. 测试其他相关API
        print(f"\n🔍 3. 测试其他相关API...")
        
        # 测试MT5自动连接
        try:
            auto_connect_response = session.post('http://127.0.0.1:5000/api/mt5/auto-connect')
            if auto_connect_response.status_code == 200:
                auto_connect_data = auto_connect_response.json()
                print(f"✅ MT5自动连接API正常: {auto_connect_data.get('success', False)}")
            else:
                print(f"⚠️ MT5自动连接API异常: {auto_connect_response.status_code}")
        except Exception as e:
            print(f"⚠️ MT5自动连接API测试失败: {e}")
        
        # 4. 检查页面JavaScript调用
        print(f"\n🔍 4. 检查页面JavaScript调用...")
        inference_response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if inference_response.status_code == 200:
            content = inference_response.text
            
            # 检查refreshPositions函数
            if 'function refreshPositions()' in content:
                print("✅ refreshPositions函数存在")
            else:
                print("❌ refreshPositions函数缺失")
            
            # 检查API调用
            if "'/api/mt5/positions'" in content:
                print("✅ 持仓API调用路径正确")
            else:
                print("❌ 持仓API调用路径错误")
            
            # 检查错误处理
            if '网络连接异常，请检查连接后重试' in content:
                print("✅ 网络错误处理存在")
            else:
                print("❌ 网络错误处理缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def create_positions_fix():
    """创建持仓问题修复方案"""
    print(f"\n🔧 创建持仓问题修复方案...")
    
    fix_script = '''
// 持仓问题修复脚本
console.log("🔧 开始修复持仓显示问题...");

// 1. 手动测试持仓API
async function testPositionsAPI() {
    console.log("🔍 测试持仓API...");
    
    try {
        const response = await fetch('/api/mt5/positions');
        console.log("API响应状态:", response.status);
        console.log("API响应头:", Object.fromEntries(response.headers.entries()));
        
        if (response.ok) {
            const data = await response.json();
            console.log("API响应数据:", data);
            
            if (data.success) {
                console.log(`✅ 持仓API正常，获取到 ${data.positions?.length || 0} 个持仓`);
                return data.positions || [];
            } else {
                console.error("❌ 持仓API返回错误:", data.error);
                return null;
            }
        } else {
            console.error("❌ 持仓API HTTP错误:", response.status, response.statusText);
            return null;
        }
    } catch (error) {
        console.error("❌ 持仓API网络异常:", error);
        return null;
    }
}

// 2. 手动刷新持仓
async function manualRefreshPositions() {
    console.log("🔄 手动刷新持仓...");
    
    // 显示加载状态
    const container = document.getElementById('currentPositionsContainer');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在获取持仓数据...</p>
            </div>
        `;
    }
    
    const positions = await testPositionsAPI();
    
    if (positions !== null) {
        if (positions.length === 0) {
            // 无持仓
            if (container) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                        <p class="text-muted">当前无持仓</p>
                    </div>
                `;
            }
            console.log("📋 当前无持仓");
        } else {
            // 有持仓，显示持仓列表
            let positionsHtml = '';
            positions.forEach((pos, index) => {
                const isBuy = pos.type === 0;
                const directionText = isBuy ? '买入' : '卖出';
                const directionClass = isBuy ? 'text-success' : 'text-danger';
                const profitClass = pos.profit >= 0 ? 'text-success' : 'text-danger';
                
                positionsHtml += `
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <div class="row align-items-center">
                                <div class="col-md-2">
                                    <strong>${pos.symbol}</strong>
                                    <div class="small text-muted">票号: ${pos.ticket}</div>
                                </div>
                                <div class="col-md-2">
                                    <span class="badge bg-${isBuy ? 'success' : 'danger'}">${directionText}</span>
                                    <div class="small text-muted">${pos.volume} 手</div>
                                </div>
                                <div class="col-md-2">
                                    <div class="small text-muted">开仓价</div>
                                    <div>${pos.price_open}</div>
                                </div>
                                <div class="col-md-2">
                                    <div class="small text-muted">当前价</div>
                                    <div>${pos.price_current}</div>
                                </div>
                                <div class="col-md-2">
                                    <div class="small text-muted">盈亏</div>
                                    <div class="${profitClass}">$${pos.profit.toFixed(2)}</div>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-sm btn-outline-danger" onclick="closePosition(${pos.ticket})">
                                        <i class="fas fa-times"></i> 平仓
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            if (container) {
                container.innerHTML = positionsHtml;
            }
            console.log(`✅ 显示了 ${positions.length} 个持仓`);
        }
    } else {
        // API调用失败
        if (container) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                    <p class="text-muted mb-3">获取持仓数据失败</p>
                    <button class="btn btn-outline-primary btn-sm" onclick="manualRefreshPositions()">
                        <i class="fas fa-sync-alt me-1"></i>重试
                    </button>
                </div>
            `;
        }
        console.log("❌ 持仓数据获取失败");
    }
}

// 3. 检查MT5连接状态
async function checkMT5Status() {
    console.log("🔍 检查MT5连接状态...");
    
    try {
        const response = await fetch('/api/mt5/connection-status');
        const data = await response.json();
        
        console.log("MT5连接状态:", data);
        
        if (data.success && data.connected) {
            console.log("✅ MT5已连接");
            return true;
        } else {
            console.log("❌ MT5未连接:", data.error);
            return false;
        }
    } catch (error) {
        console.error("❌ 检查MT5连接状态失败:", error);
        return false;
    }
}

// 4. 执行修复
async function executePositionsFix() {
    console.log("🚀 开始执行持仓修复...");
    
    // 检查MT5连接
    const mt5Connected = await checkMT5Status();
    if (!mt5Connected) {
        console.log("⚠️ MT5未连接，尝试自动连接...");
        try {
            const connectResponse = await fetch('/api/mt5/auto-connect', { method: 'POST' });
            const connectData = await connectResponse.json();
            if (connectData.success) {
                console.log("✅ MT5自动连接成功");
            } else {
                console.log("❌ MT5自动连接失败:", connectData.error);
            }
        } catch (error) {
            console.log("❌ MT5自动连接异常:", error);
        }
    }
    
    // 手动刷新持仓
    await manualRefreshPositions();
    
    console.log("🎯 持仓修复完成");
}

// 立即执行修复
executePositionsFix();

// 提供手动调用函数
window.testPositionsAPI = testPositionsAPI;
window.manualRefreshPositions = manualRefreshPositions;
window.checkMT5Status = checkMT5Status;

console.log("✅ 持仓修复脚本加载完成");
console.log("💡 可以手动调用以下函数:");
console.log("   - testPositionsAPI() : 测试持仓API");
console.log("   - manualRefreshPositions() : 手动刷新持仓");
console.log("   - checkMT5Status() : 检查MT5连接状态");
'''
    
    with open('positions_fix.js', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 持仓修复脚本已创建: positions_fix.js")
    print("📋 使用方法:")
    print("   1. 打开AI推理交易页面")
    print("   2. 按F12打开开发者工具")
    print("   3. 在Console标签中粘贴positions_fix.js的内容并执行")

if __name__ == "__main__":
    success = diagnose_positions_api()
    create_positions_fix()
    
    if success:
        print("\n🎉 持仓API诊断完成！")
        print("🔄 请使用持仓修复脚本进一步测试")
    else:
        print("\n❌ 持仓API诊断发现问题")
        print("🔧 请检查上述错误信息并使用修复脚本")
