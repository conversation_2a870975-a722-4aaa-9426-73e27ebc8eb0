#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据序列长度修复
"""

import numpy as np

def test_sequence_length_logic():
    """测试序列长度逻辑"""
    
    print("🧪 测试序列长度自动推断逻辑")
    print("=" * 60)
    
    # 模拟不同数据量的情况
    test_cases = [
        (100, "充足数据"),
        (60, "刚好60个数据点"),
        (45, "中等数据量"),
        (30, "较少数据"),
        (20, "最少数据"),
        (15, "极少数据"),
        (5, "严重不足")
    ]
    
    for data_length, description in test_cases:
        print(f"\n📊 测试场景: {description} ({data_length}个数据点)")
        
        # 模拟修复后的逻辑
        sequence_length = None  # 模拟没有配置
        
        if sequence_length is None:
            # 自动推断序列长度
            if data_length >= 60:
                sequence_length = 60
            elif data_length >= 30:
                sequence_length = 30
            elif data_length >= 20:
                sequence_length = 20
            else:
                sequence_length = max(10, data_length)
        
        print(f"   推断的序列长度: {sequence_length}")
        
        # 检查是否需要填充
        if data_length < sequence_length:
            padding_needed = sequence_length - data_length
            print(f"   需要填充: {padding_needed} 个数据点")
            print(f"   填充后总长度: {sequence_length}")
        else:
            print(f"   无需填充，使用最后 {sequence_length} 个数据点")
        
        # 评估合理性
        if sequence_length <= data_length:
            print(f"   ✅ 序列长度合理")
        elif padding_needed <= data_length * 0.5:  # 填充不超过50%
            print(f"   ⚠️ 需要适量填充，可接受")
        else:
            print(f"   ❌ 填充过多，可能影响预测质量")

def test_confidence_type_handling():
    """测试置信度类型处理"""
    
    print(f"\n🧪 测试置信度和预测值类型处理")
    print("=" * 60)
    
    # 模拟不同类型的预测结果
    test_predictions = [
        {'prediction': 'BUY', 'confidence': 0.85},      # 字符串预测，浮点置信度
        {'prediction': 'SELL', 'confidence': '0.75'},   # 字符串预测，字符串置信度
        {'prediction': 0.8, 'confidence': 0.8},         # 数值预测，浮点置信度
        {'prediction': 0.3, 'confidence': '0.65'},      # 数值预测，字符串置信度
        {'prediction': 'HOLD', 'confidence': None},     # 字符串预测，None置信度
        {'prediction': None, 'confidence': 0.5},        # None预测，浮点置信度
    ]
    
    for i, prediction in enumerate(test_predictions, 1):
        print(f"\n📊 测试用例 {i}:")
        print(f"   输入: prediction={prediction['prediction']}, confidence={prediction['confidence']}")
        
        # 模拟修复后的逻辑
        pred_value = prediction.get('prediction', 0)
        confidence_raw = prediction.get('confidence', 0.5)
        
        # 处理预测值
        if isinstance(pred_value, str):
            prediction_str = pred_value
            print(f"   预测值处理: 字符串 '{pred_value}' → '{prediction_str}'")
        else:
            try:
                pred_float = float(pred_value) if pred_value is not None else 0.0
                prediction_str = 'BUY' if pred_float > 0.5 else 'SELL'
                print(f"   预测值处理: 数值 {pred_value} → '{prediction_str}' (阈值0.5)")
            except (ValueError, TypeError):
                prediction_str = 'HOLD'
                print(f"   预测值处理: 转换失败 {pred_value} → 'HOLD'")
        
        # 处理置信度
        try:
            confidence = float(confidence_raw) if confidence_raw is not None else 0.5
            print(f"   置信度处理: {confidence_raw} → {confidence}")
        except (ValueError, TypeError):
            confidence = 0.5
            print(f"   置信度处理: 转换失败 {confidence_raw} → 0.5")
        
        print(f"   ✅ 最终结果: prediction='{prediction_str}', confidence={confidence}")

def main():
    """主函数"""
    
    print("🚀 数据序列长度和类型处理修复测试")
    print("=" * 80)
    
    # 测试序列长度逻辑
    test_sequence_length_logic()
    
    # 测试置信度类型处理
    test_confidence_type_handling()
    
    print(f"\n🎉 测试完成!")
    print(f"💡 修复后的逻辑应该能够:")
    print(f"   1. 根据可用数据自动调整序列长度")
    print(f"   2. 正确处理字符串和数值类型的预测结果")
    print(f"   3. 安全转换各种类型的置信度值")
    print(f"   4. 避免数据不足和类型转换错误")

if __name__ == "__main__":
    main()
