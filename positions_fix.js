
// 持仓问题修复脚本
console.log("🔧 开始修复持仓显示问题...");

// 1. 手动测试持仓API
async function testPositionsAPI() {
    console.log("🔍 测试持仓API...");
    
    try {
        const response = await fetch('/api/mt5/positions');
        console.log("API响应状态:", response.status);
        console.log("API响应头:", Object.fromEntries(response.headers.entries()));
        
        if (response.ok) {
            const data = await response.json();
            console.log("API响应数据:", data);
            
            if (data.success) {
                console.log(`✅ 持仓API正常，获取到 ${data.positions?.length || 0} 个持仓`);
                return data.positions || [];
            } else {
                console.error("❌ 持仓API返回错误:", data.error);
                return null;
            }
        } else {
            console.error("❌ 持仓API HTTP错误:", response.status, response.statusText);
            return null;
        }
    } catch (error) {
        console.error("❌ 持仓API网络异常:", error);
        return null;
    }
}

// 2. 手动刷新持仓
async function manualRefreshPositions() {
    console.log("🔄 手动刷新持仓...");
    
    // 显示加载状态
    const container = document.getElementById('currentPositionsContainer');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在获取持仓数据...</p>
            </div>
        `;
    }
    
    const positions = await testPositionsAPI();
    
    if (positions !== null) {
        if (positions.length === 0) {
            // 无持仓
            if (container) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                        <p class="text-muted">当前无持仓</p>
                    </div>
                `;
            }
            console.log("📋 当前无持仓");
        } else {
            // 有持仓，显示持仓列表
            let positionsHtml = '';
            positions.forEach((pos, index) => {
                const isBuy = pos.type === 0;
                const directionText = isBuy ? '买入' : '卖出';
                const directionClass = isBuy ? 'text-success' : 'text-danger';
                const profitClass = pos.profit >= 0 ? 'text-success' : 'text-danger';
                
                positionsHtml += `
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <div class="row align-items-center">
                                <div class="col-md-2">
                                    <strong>${pos.symbol}</strong>
                                    <div class="small text-muted">票号: ${pos.ticket}</div>
                                </div>
                                <div class="col-md-2">
                                    <span class="badge bg-${isBuy ? 'success' : 'danger'}">${directionText}</span>
                                    <div class="small text-muted">${pos.volume} 手</div>
                                </div>
                                <div class="col-md-2">
                                    <div class="small text-muted">开仓价</div>
                                    <div>${pos.price_open}</div>
                                </div>
                                <div class="col-md-2">
                                    <div class="small text-muted">当前价</div>
                                    <div>${pos.price_current}</div>
                                </div>
                                <div class="col-md-2">
                                    <div class="small text-muted">盈亏</div>
                                    <div class="${profitClass}">$${pos.profit.toFixed(2)}</div>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-sm btn-outline-danger" onclick="closePosition(${pos.ticket})">
                                        <i class="fas fa-times"></i> 平仓
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            if (container) {
                container.innerHTML = positionsHtml;
            }
            console.log(`✅ 显示了 ${positions.length} 个持仓`);
        }
    } else {
        // API调用失败
        if (container) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                    <p class="text-muted mb-3">获取持仓数据失败</p>
                    <button class="btn btn-outline-primary btn-sm" onclick="manualRefreshPositions()">
                        <i class="fas fa-sync-alt me-1"></i>重试
                    </button>
                </div>
            `;
        }
        console.log("❌ 持仓数据获取失败");
    }
}

// 3. 检查MT5连接状态
async function checkMT5Status() {
    console.log("🔍 检查MT5连接状态...");
    
    try {
        const response = await fetch('/api/mt5/connection-status');
        const data = await response.json();
        
        console.log("MT5连接状态:", data);
        
        if (data.success && data.connected) {
            console.log("✅ MT5已连接");
            return true;
        } else {
            console.log("❌ MT5未连接:", data.error);
            return false;
        }
    } catch (error) {
        console.error("❌ 检查MT5连接状态失败:", error);
        return false;
    }
}

// 4. 执行修复
async function executePositionsFix() {
    console.log("🚀 开始执行持仓修复...");
    
    // 检查MT5连接
    const mt5Connected = await checkMT5Status();
    if (!mt5Connected) {
        console.log("⚠️ MT5未连接，尝试自动连接...");
        try {
            const connectResponse = await fetch('/api/mt5/auto-connect', { method: 'POST' });
            const connectData = await connectResponse.json();
            if (connectData.success) {
                console.log("✅ MT5自动连接成功");
            } else {
                console.log("❌ MT5自动连接失败:", connectData.error);
            }
        } catch (error) {
            console.log("❌ MT5自动连接异常:", error);
        }
    }
    
    // 手动刷新持仓
    await manualRefreshPositions();
    
    console.log("🎯 持仓修复完成");
}

// 立即执行修复
executePositionsFix();

// 提供手动调用函数
window.testPositionsAPI = testPositionsAPI;
window.manualRefreshPositions = manualRefreshPositions;
window.checkMT5Status = checkMT5Status;

console.log("✅ 持仓修复脚本加载完成");
console.log("💡 可以手动调用以下函数:");
console.log("   - testPositionsAPI() : 测试持仓API");
console.log("   - manualRefreshPositions() : 手动刷新持仓");
console.log("   - checkMT5Status() : 检查MT5连接状态");
