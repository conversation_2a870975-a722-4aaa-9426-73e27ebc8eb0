#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强力修复25%卡住问题
彻底解决训练开始阶段的死锁
"""

import requests
import sqlite3
import json
from datetime import datetime
import time

def force_stop_current_task():
    """强制停止当前卡住的任务"""
    print('🛑 强制停止当前卡住的任务')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找当前running状态的任务
        cursor.execute('''
            SELECT id FROM training_tasks 
            WHERE status = 'running' AND progress = 25.0
            ORDER BY updated_at DESC LIMIT 1
        ''')
        
        result = cursor.fetchone()
        if result:
            task_id = result[0]
            
            # 强制更新任务状态为stopped
            cursor.execute('''
                UPDATE training_tasks 
                SET status = 'stopped',
                    updated_at = ?,
                    logs = ?
                WHERE id = ?
            ''', (
                datetime.now().isoformat(),
                json.dumps({
                    "stage": "force_stopped",
                    "message": "任务被强制停止 - 卡在25%",
                    "reason": "训练开始阶段卡住，强制停止",
                    "stopped_at": datetime.now().isoformat()
                }),
                task_id
            ))
            
            conn.commit()
            print(f"✅ 任务 {task_id} 已强制停止")
            return task_id
        else:
            print("❌ 没有找到需要停止的任务")
            return None
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 强制停止失败: {e}")
        return None

def create_minimal_test_config():
    """创建极简测试配置"""
    print('\n🔧 创建极简测试配置')
    print('=' * 60)
    
    # 极简配置，专门用于测试训练开始
    minimal_config = {
        'model_name': f'极简测试_{int(time.time())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'data_config': {'mode': 'days', 'training_days': 30},  # 最少数据
        'sequence_length': 10,  # 最短序列
        'hidden_size': 16,      # 最小模型
        'num_layers': 1,        # 单层
        'dropout': 0.1,         # 最小dropout
        'batch_size': 4,        # 最小batch
        'learning_rate': 0.001, # 标准学习率
        'epochs': 5,            # 最少轮次
        'patience': 2,          # 最小耐心
        'early_stopping': True,
        'min_epochs': 1,        # 最少轮次
        'use_gpu': False,       # 强制CPU训练
        'save_checkpoints': False,  # 不保存检查点
        'use_enhanced_features': False,  # 不使用增强特征
        'include_basic_features': True,
        'analyze_feature_importance': False,
        'auto_start_training': False,  # 手动启动
        'validation_split': 0.2,
        # 数据加载器强制设置
        'num_workers': 0,
        'pin_memory': False,
        'drop_last': True,
        'persistent_workers': False
    }
    
    print("📋 极简配置:")
    for key, value in minimal_config.items():
        print(f"   {key}: {value}")
    
    return minimal_config

def test_minimal_training():
    """测试极简训练"""
    print('\n🧪 测试极简训练')
    print('=' * 60)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 创建极简测试任务
    minimal_config = create_minimal_test_config()
    
    try:
        response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                               json=minimal_config,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 极简测试任务创建成功: {task_id}")
                
                # 等待数据准备
                print("⏳ 等待数据准备完成...")
                
                max_wait_time = 120  # 2分钟等待数据准备
                start_time = time.time()
                
                while time.time() - start_time < max_wait_time:
                    progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                    
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        if progress_data.get('success'):
                            progress = progress_data.get('progress', {})
                            status = progress.get('status', 'unknown')
                            progress_percent = progress.get('progress', 0)
                            
                            current_time = time.time() - start_time
                            print(f"   [{current_time:6.1f}s] 状态: {status}, 进度: {progress_percent}%")
                            
                            if status == 'data_ready':
                                print(f"   ✅ 数据准备完成！")
                                
                                # 立即手动启动训练
                                print(f"   🚀 立即启动训练...")
                                train_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/start-model-training/{task_id}',
                                                            headers={'Content-Type': 'application/json'})
                                
                                if train_response.status_code == 200:
                                    train_result = train_response.json()
                                    if train_result.get('success'):
                                        print(f"   ✅ 训练启动成功！")
                                        
                                        # 密切监控前30秒
                                        return monitor_critical_phase(session, task_id)
                                    else:
                                        print(f"   ❌ 训练启动失败: {train_result.get('error')}")
                                        return False
                                else:
                                    print(f"   ❌ 训练启动请求失败: {train_response.status_code}")
                                    return False
                                    
                            elif status == 'failed':
                                print(f"   ❌ 数据准备失败")
                                return False
                            
                            time.sleep(3)
                        else:
                            print(f"   ❌ 获取进度失败: {progress_data.get('error')}")
                            return False
                    else:
                        print(f"   ❌ 进度请求失败: {progress_response.status_code}")
                        return False
                
                print(f"⏰ 数据准备超时")
                return False
                
            else:
                print(f"❌ 任务创建失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_critical_phase(session, task_id):
    """监控关键阶段（前30秒）"""
    print('\n🔍 监控关键阶段（前30秒）')
    print('=' * 60)
    
    critical_time = 30  # 关键30秒
    start_time = time.time()
    last_progress = 0
    last_epoch = 0
    updates = 0
    
    print("📊 密切监控训练开始阶段...")
    
    while time.time() - start_time < critical_time:
        try:
            progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if progress_response.status_code == 200:
                progress_data = progress_response.json()
                if progress_data.get('success'):
                    progress = progress_data.get('progress', {})
                    status = progress.get('status', 'unknown')
                    progress_percent = progress.get('progress', 0)
                    current_epoch = progress.get('epoch', 0)
                    train_loss = progress.get('train_loss', 0)
                    val_loss = progress.get('val_loss', 0)
                    
                    current_time = time.time() - start_time
                    
                    print(f"[{current_time:4.1f}s] 进度: {progress_percent:5.1f}%, 轮次: {current_epoch}, "
                          f"状态: {status}, 训练损失: {train_loss:.4f}, 验证损失: {val_loss:.4f}")
                    
                    # 检查进度更新
                    if progress_percent != last_progress or current_epoch != last_epoch:
                        updates += 1
                        print(f"        ✅ 进度更新 #{updates}")
                        
                        # 如果突破25%，说明修复成功
                        if progress_percent > 25:
                            print(f"        🎉 成功突破25%！当前进度: {progress_percent}%")
                            return True
                    
                    # 检查是否卡在25%
                    if progress_percent == 25.0 and current_time > 15:
                        print(f"        ❌ 仍然卡在25%")
                        return False
                    
                    # 检查训练失败
                    if status == 'failed':
                        print(f"        ❌ 训练失败")
                        return False
                    
                    last_progress = progress_percent
                    last_epoch = current_epoch
                    
                    time.sleep(2)  # 每2秒检查一次
                else:
                    print(f"   ❌ 获取进度失败: {progress_data.get('error')}")
                    return False
            else:
                print(f"   ❌ 进度请求失败: {progress_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            return False
    
    print(f"⏰ 关键阶段监控完成")
    print(f"📊 总进度更新次数: {updates}")
    
    if updates == 0:
        print(f"❌ 没有任何进度更新，训练可能卡住")
        return False
    else:
        print(f"✅ 有进度更新，训练可能正常")
        return True

def provide_emergency_solutions():
    """提供紧急解决方案"""
    print('\n🚨 紧急解决方案')
    print('=' * 60)
    
    solutions = [
        {
            'priority': 'critical',
            'solution': '重启Flask应用',
            'description': '完全重启应用清理所有状态',
            'steps': [
                '1. Ctrl+C 停止当前Flask应用',
                '2. 等待5秒确保完全停止',
                '3. python app.py 重新启动',
                '4. 等待应用完全启动'
            ]
        },
        {
            'priority': 'high',
            'solution': '使用CPU训练',
            'description': '完全避免GPU相关问题',
            'steps': [
                '1. 设置 use_gpu: false',
                '2. 使用极简配置',
                '3. 手动启动训练',
                '4. 观察是否能突破25%'
            ]
        },
        {
            'priority': 'medium',
            'solution': '清理数据库状态',
            'description': '清理可能的状态残留',
            'steps': [
                '1. 停止所有running任务',
                '2. 清理训练缓存',
                '3. 重置任务状态',
                '4. 重新开始训练'
            ]
        }
    ]
    
    print("🚨 紧急解决方案:")
    for solution in solutions:
        priority_icon = '🔴' if solution['priority'] == 'critical' else '🟡' if solution['priority'] == 'high' else '🟢'
        print(f"\n{priority_icon} {solution['solution']} ({solution['priority'].upper()})")
        print(f"   描述: {solution['description']}")
        print(f"   步骤:")
        for step in solution['steps']:
            print(f"     {step}")

def main():
    """主函数"""
    print('🔧 强力修复25%卡住问题')
    print('=' * 80)
    
    # 强制停止当前任务
    stopped_task = force_stop_current_task()
    
    if stopped_task:
        print(f"\n✅ 已停止卡住的任务: {stopped_task}")
        
        # 等待几秒
        print("⏳ 等待3秒...")
        time.sleep(3)
        
        # 测试极简训练
        print(f"\n🧪 开始极简训练测试...")
        success = test_minimal_training()
        
        if success:
            print(f"\n🎉 极简训练测试成功！")
            print(f"💡 问题已解决，可以使用正常配置训练")
        else:
            print(f"\n❌ 极简训练测试失败")
            print(f"🚨 需要使用紧急解决方案")
            provide_emergency_solutions()
    else:
        print(f"\n⚠️ 没有找到需要停止的任务")
        
        # 直接测试极简训练
        print(f"\n🧪 直接测试极简训练...")
        success = test_minimal_training()
        
        if not success:
            print(f"\n❌ 极简训练测试失败")
            provide_emergency_solutions()
    
    print(f"\n🎯 总结:")
    print(f"如果极简测试成功，说明基本功能正常，可以逐步增加配置复杂度")
    print(f"如果极简测试失败，建议重启Flask应用并使用CPU训练")

if __name__ == "__main__":
    main()
