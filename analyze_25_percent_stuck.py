#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析25%进度卡住的原因
深度诊断训练过程中的问题
"""

import sqlite3
import json
import psutil
import subprocess
import time
import os
from datetime import datetime, <PERSON><PERSON><PERSON>

def get_stuck_task_details():
    """获取卡住任务的详细信息"""
    print('🔍 获取25%卡住任务的详细信息')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询running状态且进度为25%的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs, train_loss, val_loss
            FROM training_tasks 
            WHERE status = 'running' AND progress = 25.0
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("❌ 没有找到25%进度的running任务")
            return None
        
        task = tasks[0]  # 取最新的任务
        task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs, train_loss, val_loss = task
        
        print(f"📊 卡住任务详情:")
        print(f"   任务ID: {task_id}")
        print(f"   模型ID: {model_id}")
        print(f"   状态: {status}")
        print(f"   进度: {progress}%")
        print(f"   当前轮次: {current_epoch}/{total_epochs}")
        print(f"   创建时间: {created_at}")
        print(f"   最后更新: {updated_at}")
        print(f"   训练损失: {train_loss}")
        print(f"   验证损失: {val_loss}")
        
        # 计算卡住时间
        if updated_at:
            try:
                last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                now = datetime.now()
                stuck_time = now - last_update.replace(tzinfo=None)
                print(f"   卡住时间: {stuck_time}")
                
                if stuck_time.total_seconds() > 300:  # 5分钟
                    print(f"   ⚠️ 确认任务卡住 (超过5分钟无更新)")
                else:
                    print(f"   ⏳ 可能正在处理中")
            except Exception as e:
                print(f"   ❌ 时间解析失败: {e}")
        
        # 解析日志
        if logs:
            try:
                if isinstance(logs, str):
                    log_data = json.loads(logs)
                else:
                    log_data = logs
                
                print(f"\n📋 最新日志信息:")
                print(f"   阶段: {log_data.get('stage', 'unknown')}")
                print(f"   消息: {log_data.get('message', 'N/A')}")
                
                # 检查训练相关信息
                for key in ['epoch', 'batch', 'train_loss', 'val_loss', 'train_acc', 'val_acc']:
                    if key in log_data:
                        print(f"   {key}: {log_data.get(key, 'N/A')}")
                        
            except Exception as e:
                print(f"   ❌ 日志解析失败: {e}")
                print(f"   原始日志: {logs}")
        
        return {
            'task_id': task_id,
            'model_id': model_id,
            'current_epoch': current_epoch,
            'total_epochs': total_epochs,
            'stuck_time': stuck_time.total_seconds() if 'stuck_time' in locals() else 0,
            'logs': log_data if logs else {}
        }
        
    except Exception as e:
        print(f"❌ 获取任务详情失败: {e}")
        return None

def analyze_system_resources():
    """分析系统资源使用情况"""
    print('\n🔍 分析系统资源使用情况')
    print('=' * 60)
    
    try:
        # CPU使用率 (多次采样)
        cpu_samples = []
        for i in range(3):
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_samples.append(cpu_percent)
            print(f"💻 CPU使用率 (样本{i+1}): {cpu_percent}%")
        
        avg_cpu = sum(cpu_samples) / len(cpu_samples)
        print(f"💻 平均CPU使用率: {avg_cpu:.1f}%")
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        print(f"\n🧠 内存使用情况:")
        print(f"   总内存: {memory.total / (1024**3):.1f} GB")
        print(f"   已用内存: {memory.used / (1024**3):.1f} GB")
        print(f"   可用内存: {memory.available / (1024**3):.1f} GB")
        print(f"   使用率: {memory.percent}%")
        
        # 检查内存是否不足
        if memory.percent > 90:
            print(f"   ⚠️ 内存使用率过高！")
        elif memory.available < 1024**3:  # 小于1GB
            print(f"   ⚠️ 可用内存不足！")
        else:
            print(f"   ✅ 内存使用正常")
        
        # GPU检查
        gpu_info = check_gpu_status()
        
        # 磁盘使用情况
        disk = psutil.disk_usage('.')
        print(f"\n💾 磁盘使用情况:")
        print(f"   总空间: {disk.total / (1024**3):.1f} GB")
        print(f"   已用空间: {disk.used / (1024**3):.1f} GB")
        print(f"   可用空间: {disk.free / (1024**3):.1f} GB")
        print(f"   使用率: {disk.used / disk.total * 100:.1f}%")
        
        return {
            'avg_cpu': avg_cpu,
            'memory_percent': memory.percent,
            'memory_available_gb': memory.available / (1024**3),
            'disk_free_gb': disk.free / (1024**3),
            'gpu_info': gpu_info
        }
        
    except Exception as e:
        print(f"❌ 系统资源分析失败: {e}")
        return {}

def check_gpu_status():
    """检查GPU状态"""
    print(f"\n🎮 GPU状态检查:")
    
    try:
        # 尝试使用nvidia-smi
        result = subprocess.run(['nvidia-smi', '--query-gpu=name,utilization.gpu,memory.used,memory.total,temperature.gpu', 
                               '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            gpu_lines = result.stdout.strip().split('\n')
            gpu_info = []
            
            for i, line in enumerate(gpu_lines):
                if line.strip():
                    parts = line.split(', ')
                    if len(parts) >= 5:
                        name, util, mem_used, mem_total, temp = parts[:5]
                        print(f"   GPU {i}: {name}")
                        print(f"     使用率: {util}%")
                        print(f"     内存: {mem_used}/{mem_total} MB ({float(mem_used)/float(mem_total)*100:.1f}%)")
                        print(f"     温度: {temp}°C")
                        
                        gpu_info.append({
                            'name': name,
                            'utilization': float(util),
                            'memory_used': float(mem_used),
                            'memory_total': float(mem_total),
                            'temperature': float(temp)
                        })
                        
                        # 检查GPU问题
                        if float(util) == 0:
                            print(f"     ⚠️ GPU使用率为0，可能没有在使用GPU")
                        if float(mem_used)/float(mem_total) > 0.95:
                            print(f"     ⚠️ GPU内存使用率过高！")
                        if float(temp) > 80:
                            print(f"     ⚠️ GPU温度过高！")
            
            return gpu_info
        else:
            print(f"   ❌ nvidia-smi命令失败")
            return []
            
    except subprocess.TimeoutExpired:
        print(f"   ⚠️ nvidia-smi命令超时")
        return []
    except FileNotFoundError:
        print(f"   ❌ 未找到nvidia-smi，可能没有NVIDIA GPU")
        return []
    except Exception as e:
        print(f"   ❌ GPU检查失败: {e}")
        return []

def analyze_python_processes():
    """分析Python进程"""
    print(f'\n🔍 分析Python训练进程')
    print('=' * 60)
    
    try:
        training_processes = []
        all_python_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent', 'status']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    
                    proc_info = {
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline,
                        'cpu_percent': proc.info['cpu_percent'],
                        'memory_percent': proc.info['memory_percent'],
                        'status': proc.info['status']
                    }
                    
                    all_python_processes.append(proc_info)
                    
                    # 检查是否是训练相关进程
                    if any(keyword in cmdline.lower() for keyword in ['deep_learning', 'training', 'app.py', 'torch', 'tensorflow']):
                        training_processes.append(proc_info)
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        print(f"🐍 训练相关Python进程 ({len(training_processes)}个):")
        if training_processes:
            for proc in training_processes:
                print(f"   PID {proc['pid']}: {proc['name']}")
                print(f"     状态: {proc['status']}")
                print(f"     CPU: {proc['cpu_percent']}%")
                print(f"     内存: {proc['memory_percent']:.1f}%")
                print(f"     命令: {proc['cmdline'][:80]}...")
                
                # 检查进程状态
                if proc['status'] == 'zombie':
                    print(f"     ⚠️ 僵尸进程！")
                elif proc['status'] == 'stopped':
                    print(f"     ⚠️ 进程已停止！")
                elif proc['cpu_percent'] == 0:
                    print(f"     ⚠️ CPU使用率为0，可能卡住")
        else:
            print(f"   ❌ 没有找到训练相关的Python进程")
        
        print(f"\n🐍 所有Python进程 ({len(all_python_processes)}个):")
        for proc in all_python_processes[:5]:  # 只显示前5个
            print(f"   PID {proc['pid']}: {proc['name']} (CPU: {proc['cpu_percent']}%, 内存: {proc['memory_percent']:.1f}%)")
        
        if len(all_python_processes) > 5:
            print(f"   ... 还有 {len(all_python_processes) - 5} 个进程")
        
        return training_processes
        
    except Exception as e:
        print(f"❌ 进程分析失败: {e}")
        return []

def analyze_25_percent_causes(task_info, system_info, processes):
    """分析25%卡住的具体原因"""
    print(f'\n🔍 分析25%进度卡住的具体原因')
    print('=' * 60)
    
    possible_causes = []
    
    # 1. 分析25%进度的含义
    print(f"📊 25%进度分析:")
    print(f"   25%通常表示: 数据准备完成，开始模型训练阶段")
    print(f"   可能卡住的位置: 模型初始化、第一轮训练、数据加载")
    
    # 2. 基于系统资源分析
    if system_info.get('memory_percent', 0) > 85:
        possible_causes.append({
            'cause': '内存不足',
            'description': f"内存使用率 {system_info['memory_percent']}% 过高，可能导致训练卡住",
            'severity': 'high',
            'solution': '关闭其他程序，减小batch_size，或重启系统'
        })
    
    if system_info.get('avg_cpu', 0) < 5:
        possible_causes.append({
            'cause': 'CPU使用率过低',
            'description': f"平均CPU使用率仅 {system_info['avg_cpu']:.1f}%，训练进程可能已停止",
            'severity': 'high',
            'solution': '检查训练进程状态，可能需要重启训练'
        })
    
    # 3. 基于GPU状态分析
    gpu_info = system_info.get('gpu_info', [])
    if gpu_info:
        for i, gpu in enumerate(gpu_info):
            if gpu['utilization'] == 0:
                possible_causes.append({
                    'cause': f'GPU {i} 未被使用',
                    'description': f"GPU使用率为0%，可能配置了GPU训练但实际在用CPU",
                    'severity': 'medium',
                    'solution': '检查GPU配置，或改为CPU训练'
                })
            
            if gpu['memory_used'] / gpu['memory_total'] > 0.95:
                possible_causes.append({
                    'cause': f'GPU {i} 内存不足',
                    'description': f"GPU内存使用率 {gpu['memory_used']/gpu['memory_total']*100:.1f}%",
                    'severity': 'high',
                    'solution': '减小batch_size或使用CPU训练'
                })
    
    # 4. 基于进程状态分析
    if not processes:
        possible_causes.append({
            'cause': '训练进程丢失',
            'description': '没有找到活跃的训练进程',
            'severity': 'high',
            'solution': '重启训练任务'
        })
    else:
        for proc in processes:
            if proc['status'] == 'zombie':
                possible_causes.append({
                    'cause': '僵尸进程',
                    'description': f"PID {proc['pid']} 是僵尸进程",
                    'severity': 'high',
                    'solution': '杀死僵尸进程，重启训练'
                })
            
            if proc['cpu_percent'] == 0:
                possible_causes.append({
                    'cause': '进程CPU使用率为0',
                    'description': f"PID {proc['pid']} CPU使用率为0，可能卡住",
                    'severity': 'medium',
                    'solution': '检查进程状态，可能需要重启'
                })
    
    # 5. 基于任务信息分析
    if task_info:
        stuck_time = task_info.get('stuck_time', 0)
        if stuck_time > 600:  # 10分钟
            possible_causes.append({
                'cause': '长时间无更新',
                'description': f"任务已 {stuck_time/60:.1f} 分钟无更新",
                'severity': 'high',
                'solution': '强制停止并重启训练'
            })
        
        if task_info.get('current_epoch', 0) == 0:
            possible_causes.append({
                'cause': '第一轮训练卡住',
                'description': '任务卡在第一轮训练，可能是模型初始化问题',
                'severity': 'high',
                'solution': '检查模型配置，降低复杂度'
            })
    
    # 6. 常见的25%卡住原因
    common_causes = [
        {
            'cause': '数据加载器死锁',
            'description': '数据加载器在第一次加载时可能出现死锁',
            'severity': 'medium',
            'solution': '减少num_workers或使用单线程加载'
        },
        {
            'cause': '模型初始化失败',
            'description': '模型参数初始化时可能遇到问题',
            'severity': 'medium',
            'solution': '简化模型结构，检查参数配置'
        },
        {
            'cause': 'CUDA初始化问题',
            'description': 'GPU训练时CUDA初始化可能失败',
            'severity': 'medium',
            'solution': '改为CPU训练或重启GPU驱动'
        }
    ]
    
    possible_causes.extend(common_causes)
    
    # 输出分析结果
    print(f"\n🚨 发现 {len(possible_causes)} 个可能原因:")
    for i, cause in enumerate(possible_causes, 1):
        severity_icon = '🔴' if cause['severity'] == 'high' else '🟡' if cause['severity'] == 'medium' else '🟢'
        print(f"\n{i}. {severity_icon} {cause['cause']} ({cause['severity'].upper()})")
        print(f"   描述: {cause['description']}")
        print(f"   解决方案: {cause['solution']}")
    
    return possible_causes

def provide_detailed_solutions(task_info, causes):
    """提供详细的解决方案"""
    print(f'\n🔧 详细解决方案')
    print('=' * 60)
    
    task_id = task_info.get('task_id', 'unknown') if task_info else 'unknown'
    
    print("🛑 立即解决方案:")
    print(f"1. 强制停止卡住的任务:")
    print(f"   python force_stop_training.py")
    
    print(f"\n2. 清理系统资源:")
    print(f"   - 重启Flask应用 (Ctrl+C 然后 python app.py)")
    print(f"   - 清理GPU内存 (重启或nvidia-smi --gpu-reset)")
    print(f"   - 关闭不必要的程序释放内存")
    
    print(f"\n🔄 优化重启方案:")
    print(f"3. 使用极简配置重新训练:")
    optimized_config = {
        'batch_size': 4,        # 极小的batch_size
        'hidden_size': 16,      # 极小的模型
        'num_layers': 1,        # 单层
        'learning_rate': 0.001, # 较高的学习率
        'epochs': 10,           # 少量轮次
        'use_gpu': False,       # 强制使用CPU
        'sequence_length': 10   # 短序列
    }
    
    print(f"   推荐配置:")
    for key, value in optimized_config.items():
        print(f"     {key}: {value}")
    
    print(f"\n📊 监控方案:")
    print(f"4. 密切监控训练过程:")
    print(f"   - 每30秒检查一次进度")
    print(f"   - 观察CPU和内存使用情况")
    print(f"   - 如果再次卡在25%，立即停止")
    
    # 根据具体原因提供针对性建议
    high_priority_causes = [c for c in causes if c['severity'] == 'high']
    if high_priority_causes:
        print(f"\n🚨 高优先级问题处理:")
        for cause in high_priority_causes:
            print(f"   - {cause['cause']}: {cause['solution']}")

def main():
    """主函数"""
    print('🔧 25%进度卡住问题详细分析')
    print('=' * 80)
    
    # 获取卡住任务详情
    task_info = get_stuck_task_details()
    
    # 分析系统资源
    system_info = analyze_system_resources()
    
    # 分析Python进程
    processes = analyze_python_processes()
    
    # 分析25%卡住的原因
    causes = analyze_25_percent_causes(task_info, system_info, processes)
    
    # 提供详细解决方案
    provide_detailed_solutions(task_info, causes)
    
    print(f"\n✅ 详细分析完成！")
    
    if task_info:
        stuck_time = task_info.get('stuck_time', 0)
        print(f"💡 关键发现:")
        print(f"   - 任务已卡住 {stuck_time/60:.1f} 分钟")
        print(f"   - 卡在模型训练阶段的开始")
        print(f"   - 建议立即停止并使用极简配置重试")
    else:
        print(f"💡 没有发现25%卡住的任务")

if __name__ == "__main__":
    main()
