<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>训练修复工具测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>🔧 训练修复工具测试</h1>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>API测试</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="testCheckStatusAPI()">测试状态检查API</button>
                <button class="btn btn-warning ms-2" onclick="testFix25PercentAPI()">测试25%修复API</button>
                <button class="btn btn-info ms-2" onclick="testForceUpdateAPI()">测试强制更新API</button>
                
                <div id="testResults" class="mt-3"></div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>测试日志</h5>
            </div>
            <div class="card-body">
                <div id="testLog" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                    <p class="text-muted mb-0">等待测试...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addTestLog(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `<div class="mb-1"><small class="text-muted">[${timestamp}]</small> ${message}</div>`;
            
            if (logDiv.innerHTML.includes('等待测试')) {
                logDiv.innerHTML = logEntry;
            } else {
                logDiv.innerHTML += logEntry;
            }
            
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function testCheckStatusAPI() {
            addTestLog('🔍 开始测试状态检查API...');
            
            fetch('/api/training-fix/check-status')
                .then(response => {
                    addTestLog(`📡 HTTP状态码: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    addTestLog('✅ API响应成功');
                    addTestLog(`📊 返回数据: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.success && data.tasks) {
                        addTestLog(`📋 发现 ${data.count} 个训练任务`);
                        
                        data.tasks.forEach((task, index) => {
                            addTestLog(`任务 ${index + 1}: ${task.name} - ${task.progress}% - ${task.status}`);
                        });
                        
                        document.getElementById('testResults').innerHTML = 
                            `<div class="alert alert-success">✅ 状态检查API测试成功，发现 ${data.count} 个任务</div>`;
                    } else {
                        addTestLog(`❌ API返回错误: ${data.error || '未知错误'}`);
                        document.getElementById('testResults').innerHTML = 
                            `<div class="alert alert-danger">❌ 状态检查API测试失败: ${data.error || '未知错误'}</div>`;
                    }
                })
                .catch(error => {
                    addTestLog(`❌ API请求失败: ${error.message}`);
                    document.getElementById('testResults').innerHTML = 
                        `<div class="alert alert-danger">❌ API请求失败: ${error.message}</div>`;
                });
        }

        function testFix25PercentAPI() {
            addTestLog('🔧 开始测试25%修复API...');
            
            fetch('/api/training-fix/fix-25-percent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                addTestLog(`📡 HTTP状态码: ${response.status}`);
                return response.json();
            })
            .then(data => {
                addTestLog('✅ API响应成功');
                addTestLog(`📊 返回数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    addTestLog(`✅ 25%修复成功: ${data.message}`);
                    document.getElementById('testResults').innerHTML = 
                        `<div class="alert alert-success">✅ 25%修复API测试成功: ${data.message}</div>`;
                } else {
                    addTestLog(`❌ 25%修复失败: ${data.error}`);
                    document.getElementById('testResults').innerHTML = 
                        `<div class="alert alert-warning">⚠️ 25%修复API返回: ${data.error}</div>`;
                }
            })
            .catch(error => {
                addTestLog(`❌ API请求失败: ${error.message}`);
                document.getElementById('testResults').innerHTML = 
                    `<div class="alert alert-danger">❌ API请求失败: ${error.message}</div>`;
            });
        }

        function testForceUpdateAPI() {
            addTestLog('🔄 开始测试强制更新API...');
            
            fetch('/api/training-fix/force-progress-update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                addTestLog(`📡 HTTP状态码: ${response.status}`);
                return response.json();
            })
            .then(data => {
                addTestLog('✅ API响应成功');
                addTestLog(`📊 返回数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    addTestLog(`✅ 强制更新成功: ${data.message}`);
                    document.getElementById('testResults').innerHTML = 
                        `<div class="alert alert-success">✅ 强制更新API测试成功: ${data.message}</div>`;
                } else {
                    addTestLog(`❌ 强制更新失败: ${data.error}`);
                    document.getElementById('testResults').innerHTML = 
                        `<div class="alert alert-warning">⚠️ 强制更新API返回: ${data.error}</div>`;
                }
            })
            .catch(error => {
                addTestLog(`❌ API请求失败: ${error.message}`);
                document.getElementById('testResults').innerHTML = 
                    `<div class="alert alert-danger">❌ API请求失败: ${error.message}</div>`;
            });
        }

        // 页面加载时自动测试状态检查API
        window.onload = function() {
            addTestLog('🚀 页面加载完成，开始自动测试...');
            setTimeout(() => {
                testCheckStatusAPI();
            }, 1000);
        };
    </script>
</body>
</html>
