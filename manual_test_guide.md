# AI推理交易功能手动测试指南

## 🎯 测试目标
验证AI推理交易页面的模型选择和MT5连接功能是否正常工作。

## 📋 测试步骤

### 1. 页面访问测试
- [ ] 访问: http://127.0.0.1:5000/deep-learning/inference
- [ ] 页面正常加载，无明显错误
- [ ] 页面布局完整，所有区域都可见

### 2. 模型选择功能测试
- [ ] 找到"交易配置"区域中的"交易模型"下拉框
- [ ] 下拉框中应该有模型选项（不只是"请选择交易模型..."）
- [ ] 选择一个模型后，下方应显示模型信息
- [ ] 模型信息应包含模型名称、类型、交易品种等

### 3. MT5连接功能测试
- [ ] 页面顶部应显示MT5连接状态（绿色"已连接"或红色"未连接"）
- [ ] 点击"检查连接"按钮，状态应该更新
- [ ] 如果未连接，点击"自动连接"按钮尝试连接
- [ ] 连接成功后状态应变为绿色"MT5已连接"

### 4. 浏览器控制台检查
- [ ] 按F12打开开发者工具
- [ ] 切换到Console标签
- [ ] 检查是否有红色错误信息
- [ ] 特别注意是否有JavaScript错误

### 5. 功能交互测试
- [ ] 选择模型后，其他配置选项应该可以正常设置
- [ ] 增强特征开关应该可以切换
- [ ] 各种交易参数应该可以输入和修改

## 🔧 故障排除

### 如果模型选择没有选项：
1. 检查浏览器控制台是否有错误
2. 手动刷新页面 (Ctrl+F5)
3. 检查是否有训练完成的模型
4. 在控制台执行: `loadTradingModels()`

### 如果MT5连接失败：
1. 确保MT5软件已启动
2. 检查MT5设置中的"允许自动交易"
3. 检查防火墙设置
4. 在控制台执行: `checkMT5Connection()`

### 如果页面功能异常：
1. 清除浏览器缓存
2. 尝试无痕模式
3. 检查网络连接
4. 重启应用服务器

## 📞 报告问题
如果发现问题，请提供：
1. 具体的错误现象描述
2. 浏览器控制台的错误信息截图
3. 页面显示的截图
4. 使用的浏览器类型和版本
