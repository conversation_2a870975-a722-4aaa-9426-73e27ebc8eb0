#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度诊断训练卡住问题
"""

import sqlite3
import json
import psutil
import time
import os
from datetime import datetime, timed<PERSON>ta

def check_training_progress():
    """检查训练进度变化"""
    print('🔍 检查训练进度变化')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取正在运行的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        running_tasks = cursor.fetchall()
        
        if not running_tasks:
            print("❌ 没有正在运行的训练任务")
            return []
        
        stuck_tasks = []
        
        for task in running_tasks:
            task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs = task
            
            print(f"\n📊 任务: {task_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            
            # 检查最后更新时间
            try:
                if updated_at:
                    last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    now = datetime.now()
                    time_diff = now - last_update.replace(tzinfo=None)
                    
                    print(f"   距离上次更新: {time_diff}")
                    
                    # 如果超过5分钟没有更新，认为可能卡住
                    if time_diff > timedelta(minutes=5):
                        print(f"   ⚠️ 可能卡住 (超过5分钟无更新)")
                        stuck_tasks.append(task_id)
                    else:
                        print(f"   ✅ 正常运行")
                        
            except Exception as e:
                print(f"   ❌ 时间解析失败: {e}")
            
            # 解析最新日志
            if logs:
                try:
                    log_data = json.loads(logs)
                    print(f"   最新轮次: {log_data.get('epoch', 'N/A')}")
                    print(f"   训练损失: {log_data.get('train_loss', 'N/A')}")
                    print(f"   验证损失: {log_data.get('val_loss', 'N/A')}")
                    print(f"   训练准确率: {log_data.get('train_acc', 'N/A')}")
                    print(f"   验证准确率: {log_data.get('val_acc', 'N/A')}")
                except:
                    print(f"   原始日志: {logs[:100]}...")
        
        conn.close()
        return stuck_tasks
        
    except Exception as e:
        print(f"❌ 检查训练进度失败: {e}")
        return []

def check_system_resources():
    """检查系统资源"""
    print('\n💻 检查系统资源')
    print('=' * 50)
    
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=2)
        print(f"🖥️ CPU使用率: {cpu_percent}%")
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        print(f"💾 内存使用率: {memory.percent}%")
        print(f"   可用内存: {memory.available / (1024**3):.1f}GB")
        
        # 检查是否内存不足
        if memory.percent > 90:
            print("   ⚠️ 内存使用率过高，可能导致训练卡住")
        elif memory.available < 1 * (1024**3):  # 小于1GB
            print("   ⚠️ 可用内存不足，可能影响训练")
        else:
            print("   ✅ 内存状态正常")
        
        # 磁盘使用情况
        disk = psutil.disk_usage('.')
        print(f"💽 磁盘使用率: {disk.percent}%")
        print(f"   可用空间: {disk.free / (1024**3):.1f}GB")
        
        if disk.percent > 95:
            print("   ⚠️ 磁盘空间不足，可能导致训练失败")
        else:
            print("   ✅ 磁盘空间充足")
            
    except Exception as e:
        print(f"❌ 检查系统资源失败: {e}")

def check_python_processes():
    """检查Python训练进程"""
    print('\n🐍 检查Python训练进程')
    print('=' * 50)
    
    try:
        training_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent', 'status']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    
                    # 检查是否是训练相关进程
                    if any(keyword in cmdline.lower() for keyword in ['train', 'deep_learning', 'model']):
                        training_processes.append(proc.info)
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if training_processes:
            for proc in training_processes:
                print(f"🔹 PID: {proc['pid']}")
                print(f"   状态: {proc['status']}")
                print(f"   CPU: {proc['cpu_percent']}%")
                print(f"   内存: {proc['memory_percent']:.1f}%")
                
                # 检查进程状态
                if proc['status'] == 'sleeping':
                    print("   ⚠️ 进程处于睡眠状态，可能在等待IO或资源")
                elif proc['status'] == 'zombie':
                    print("   ❌ 僵尸进程，需要清理")
                elif proc['cpu_percent'] < 1:
                    print("   ⚠️ CPU使用率很低，可能卡住或等待")
                else:
                    print("   ✅ 进程正常运行")
                print()
        else:
            print("❌ 没有找到训练相关的Python进程")
            
    except Exception as e:
        print(f"❌ 检查Python进程失败: {e}")

def check_gpu_usage():
    """检查GPU使用情况"""
    print('\n🎮 检查GPU使用情况')
    print('=' * 50)
    
    try:
        import torch
        
        if torch.cuda.is_available():
            print(f"✅ CUDA可用")
            
            for i in range(torch.cuda.device_count()):
                print(f"\n🎯 GPU {i}: {torch.cuda.get_device_name(i)}")
                
                # 内存使用情况
                memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
                memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
                memory_total = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                
                print(f"   已分配内存: {memory_allocated:.2f}GB")
                print(f"   已保留内存: {memory_reserved:.2f}GB")
                print(f"   总内存: {memory_total:.2f}GB")
                print(f"   使用率: {(memory_allocated/memory_total)*100:.1f}%")
                
                # 检查GPU状态
                if memory_allocated > memory_total * 0.9:
                    print("   ⚠️ GPU内存使用率过高，可能导致OOM")
                elif memory_allocated == 0:
                    print("   ⚠️ GPU内存未使用，训练可能未启动或已停止")
                else:
                    print("   ✅ GPU内存使用正常")
        else:
            print("❌ CUDA不可用，使用CPU训练")
            
    except ImportError:
        print("❌ PyTorch未安装，无法检查GPU状态")
    except Exception as e:
        print(f"❌ 检查GPU失败: {e}")

def check_log_files():
    """检查最新日志"""
    print('\n📋 检查最新日志')
    print('=' * 50)
    
    log_patterns = ['*.log', 'training*.log', 'error*.log']
    
    try:
        import glob
        
        all_logs = []
        for pattern in log_patterns:
            all_logs.extend(glob.glob(pattern))
        
        if all_logs:
            # 按修改时间排序
            all_logs.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            for log_file in all_logs[:3]:  # 只检查最新的3个日志文件
                try:
                    mod_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                    print(f"\n📄 {log_file} (修改时间: {mod_time})")
                    
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                        
                    # 显示最后几行
                    print("   最后5行:")
                    for line in lines[-5:]:
                        print(f"   {line.strip()}")
                        
                except Exception as e:
                    print(f"   ❌ 读取失败: {e}")
        else:
            print("❌ 没有找到日志文件")
            
    except Exception as e:
        print(f"❌ 检查日志失败: {e}")

def suggest_solutions(stuck_tasks):
    """建议解决方案"""
    print('\n🔧 解决方案建议')
    print('=' * 50)
    
    if stuck_tasks:
        print(f"❌ 发现 {len(stuck_tasks)} 个可能卡住的任务")
        print("\n建议操作:")
        print("1. 停止卡住的训练任务")
        print("2. 检查系统资源是否充足")
        print("3. 清理GPU内存")
        print("4. 重新启动训练")
        
        print(f"\n🔧 停止卡住任务的命令:")
        for task_id in stuck_tasks:
            print(f"   curl -X POST http://127.0.0.1:5000/api/deep-learning/stop-training/{task_id}")
    else:
        print("✅ 没有发现明显卡住的任务")
        print("\n如果仍然觉得训练卡住，可能的原因:")
        print("1. 训练数据量大，单个epoch时间长")
        print("2. 模型复杂度高，计算时间长")
        print("3. 系统资源不足，训练速度慢")
        print("4. GPU内存不足，频繁交换")
        
        print("\n建议操作:")
        print("1. 耐心等待，观察进度变化")
        print("2. 减少batch_size降低内存使用")
        print("3. 使用更简单的模型结构")
        print("4. 检查数据预处理是否有问题")

def main():
    """主函数"""
    print('🔧 训练卡住问题深度诊断')
    print('=' * 80)
    
    # 检查训练进度
    stuck_tasks = check_training_progress()
    
    # 检查系统资源
    check_system_resources()
    
    # 检查Python进程
    check_python_processes()
    
    # 检查GPU使用情况
    check_gpu_usage()
    
    # 检查日志文件
    check_log_files()
    
    # 建议解决方案
    suggest_solutions(stuck_tasks)

if __name__ == "__main__":
    main()
