#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI推理回测数据显示修复
"""

import requests
import time
import json

def test_backtest_data_fix():
    """测试回测数据显示修复"""
    print("🧪 测试AI推理回测数据显示修复")
    print("=" * 50)
    
    # 等待应用启动
    time.sleep(3)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 1. 获取可用模型
        print(f"\n🔍 获取可用模型...")
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if models_response.status_code != 200:
            print(f"❌ 获取模型失败: {models_response.status_code}")
            return False
        
        models_data = models_response.json()
        if not models_data.get('success') or not models_data.get('models'):
            print(f"❌ 没有可用模型")
            return False
        
        # 选择第一个训练完成的模型
        test_model = None
        for model in models_data['models']:
            if model.get('status') == 'completed':
                test_model = model
                break
        
        if not test_model:
            print(f"❌ 没有训练完成的模型")
            return False
        
        print(f"✅ 选择测试模型: {test_model['name']}")
        
        # 2. 执行回测
        print(f"\n🔄 执行回测...")
        backtest_data = {
            'model_id': test_model['id'],
            'symbol': test_model.get('symbol', 'XAUUSD'),
            'timeframe': test_model.get('timeframe', 'H1'),
            'start_date': '2025-07-24',
            'end_date': '2025-08-02',
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 1000,
            'take_profit_pips': 2000,
            'min_confidence': 0.3,
            'cliff_brake_enabled': False,
            'trailing_stop_enabled': False
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', 
                               json=backtest_data)
        
        if response.status_code != 200:
            print(f"❌ 回测请求失败: {response.status_code}")
            return False
        
        result = response.json()
        if not result.get('success'):
            print(f"❌ 回测失败: {result.get('error')}")
            return False
        
        trades = result.get('trades', [])
        statistics = result.get('statistics', {})
        
        print(f"✅ 回测成功: {len(trades)} 笔交易")
        print(f"📊 统计: 胜率{statistics.get('win_rate', 0):.1f}%, 总收益{statistics.get('total_return', 0):.2f}%")
        
        # 3. 检查交易数据结构
        print(f"\n🔍 检查交易数据结构:")
        if trades:
            first_trade = trades[0]
            print(f"第一笔交易数据字段:")
            for key, value in first_trade.items():
                print(f"   {key}: {value}")
            
            # 检查修复的字段
            required_fields = [
                ('open_time', '开仓时间'),
                ('type', '交易类型'),
                ('open_price', '开仓价'),
                ('close_price', '平仓价'),
                ('stop_loss', '止损'),
                ('take_profit', '止盈'),
                ('lot_size', '手数'),
                ('profit', '盈亏'),
                ('confidence', '置信度')
            ]
            
            print(f"\n📋 字段检查:")
            for field, name in required_fields:
                if field in first_trade:
                    value = first_trade[field]
                    print(f"   ✅ {name} ({field}): {value}")
                else:
                    print(f"   ❌ {name} ({field}): 缺失")
            
            # 检查数据质量
            print(f"\n📋 数据质量检查:")
            issues = []
            
            for i, trade in enumerate(trades[:3]):  # 检查前3笔交易
                trade_issues = []
                
                # 检查交易类型
                trade_type = trade.get('type') or trade.get('prediction')
                if not trade_type or trade_type in ['undefined', None]:
                    trade_issues.append("交易类型为空或undefined")
                
                # 检查止损止盈
                stop_loss = trade.get('stop_loss')
                take_profit = trade.get('take_profit')
                if not stop_loss or stop_loss in ['N/A', None]:
                    trade_issues.append("止损为空或N/A")
                if not take_profit or take_profit in ['N/A', None]:
                    trade_issues.append("止盈为空或N/A")
                
                # 检查时间
                open_time = trade.get('open_time') or trade.get('timestamp')
                if not open_time:
                    trade_issues.append("开仓时间缺失")
                
                if trade_issues:
                    issues.extend([f"交易{i+1}: {issue}" for issue in trade_issues])
                else:
                    print(f"   ✅ 交易{i+1}: 数据完整")
            
            if issues:
                print(f"\n❌ 发现数据问题:")
                for issue in issues:
                    print(f"   - {issue}")
            else:
                print(f"\n✅ 所有交易数据完整")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def create_browser_data_test_script():
    """创建浏览器数据测试脚本"""
    print(f"\n🔧 创建浏览器数据测试脚本...")
    
    test_script = '''
// AI推理回测数据显示修复测试脚本
console.log("🧪 开始测试AI推理回测数据显示修复...");

// 1. 测试交易类型格式化函数
console.log("📋 测试交易类型格式化:");
if (typeof formatTradeType === 'function') {
    const testTypes = [
        'BUY', 'SELL', 'buy', 'sell',
        'LONG', 'SHORT', 'long', 'short',
        '0', '1', 0, 1,
        'ORDER_TYPE_BUY', 'ORDER_TYPE_SELL',
        null, undefined, '', 'undefined'
    ];
    
    testTypes.forEach(type => {
        const result = formatTradeType(type);
        console.log(`   输入: ${type} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatTradeType函数不存在");
}

// 2. 测试模拟交易数据显示
console.log("📋 测试模拟交易数据显示:");
if (typeof displayTradeDetails === 'function') {
    // 创建修复后的模拟交易数据
    const mockTrades = [
        {
            open_time: '2025-07-28T17:30:00Z',
            type: 'BUY',
            lot_size: 0.01,
            open_price: 3302.25000,
            close_price: 3312.85000,
            stop_loss: 3292.25000,  // 修复：添加止损价格
            take_profit: 3322.25000, // 修复：添加止盈价格
            profit: 10.60,
            confidence: 0.585
        },
        {
            open_time: '2025-07-29T11:15:00Z',
            type: 'SELL',
            lot_size: 0.01,
            open_price: 3308.51000,
            close_price: 3327.55000,
            stop_loss: 3318.51000,  // 修复：添加止损价格
            take_profit: 3288.51000, // 修复：添加止盈价格
            profit: -19.04,
            confidence: 0.50
        },
        {
            open_time: '2025-07-30T09:45:00Z',
            type: 'BUY',
            lot_size: 0.01,
            open_price: 3315.75000,
            close_price: 3325.25000,
            stop_loss: 3305.75000,  // 修复：添加止损价格
            take_profit: 3335.75000, // 修复：添加止盈价格
            profit: 9.50,
            confidence: 0.72
        }
    ];
    
    console.log("🔄 显示修复后的模拟交易数据...");
    displayTradeDetails(mockTrades);
    console.log("✅ 交易详情显示完成");
    
    // 检查显示结果
    setTimeout(() => {
        const resultsContainer = document.getElementById('backtestResults');
        if (resultsContainer) {
            const tableRows = resultsContainer.querySelectorAll('tbody tr');
            console.log(`📊 显示了 ${tableRows.length} 行交易数据`);
            
            // 检查第一行数据
            if (tableRows.length > 0) {
                const firstRow = tableRows[0];
                const cells = firstRow.querySelectorAll('td');
                if (cells.length >= 9) {
                    console.log("📋 第一行数据检查:");
                    console.log(`   时间: ${cells[0].textContent}`);
                    console.log(`   类型: ${cells[1].textContent}`);
                    console.log(`   手数: ${cells[2].textContent}`);
                    console.log(`   开仓价: ${cells[3].textContent}`);
                    console.log(`   平仓价: ${cells[4].textContent}`);
                    console.log(`   止损: ${cells[5].textContent}`);
                    console.log(`   止盈: ${cells[6].textContent}`);
                    console.log(`   盈亏: ${cells[7].textContent}`);
                    console.log(`   置信度: ${cells[8].textContent}`);
                    
                    // 检查是否还有undefined或N/A
                    const hasUndefined = Array.from(cells).some(cell => 
                        cell.textContent.includes('undefined') || 
                        cell.textContent.includes('N/A')
                    );
                    
                    if (hasUndefined) {
                        console.log("❌ 仍然存在undefined或N/A显示");
                    } else {
                        console.log("✅ 所有数据显示正常");
                    }
                }
            }
        }
    }, 1000);
} else {
    console.log("❌ displayTradeDetails函数不存在");
}

console.log("🎉 AI推理回测数据显示修复测试完成！");
console.log("💡 修复内容:");
console.log("   1. 交易类型：支持多种格式，统一显示为中文");
console.log("   2. 止损止盈：后端返回实际价格，前端正确显示");
console.log("   3. 时间格式：统一使用open_time字段");
console.log("   4. 数据兼容：支持多种字段名称");
'''
    
    with open('browser_backtest_data_test.js', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 浏览器数据测试脚本已创建: browser_backtest_data_test.js")
    print("📋 使用方法:")
    print("   1. 打开AI推理回测页面: http://127.0.0.1:5000/deep-learning/backtest")
    print("   2. 按F12打开开发者工具")
    print("   3. 在Console标签中粘贴browser_backtest_data_test.js的内容并执行")

if __name__ == "__main__":
    success = test_backtest_data_fix()
    create_browser_data_test_script()
    
    if success:
        print("\n🎉 AI推理回测数据显示修复测试完成！")
        print("📋 修复总结:")
        print("   1. ✅ 后端返回数据：添加了type、open_time、stop_loss、take_profit字段")
        print("   2. ✅ 前端格式化：改进了formatTradeType函数，支持多种格式")
        print("   3. ✅ 字段兼容性：支持多种字段名称，提高兼容性")
        print("   4. ✅ 数据完整性：确保止损止盈价格正确计算和显示")
        print("\n🔄 请访问回测页面并执行回测验证修复效果")
    else:
        print("\n❌ AI推理回测数据显示修复测试失败")
        print("🔧 请检查上述错误信息")
