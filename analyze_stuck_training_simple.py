#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版训练卡住问题分析
专门分析第8轮卡住的问题
"""

import sqlite3
import json
import psutil
import time
from datetime import datetime, timed<PERSON><PERSON>

def get_running_tasks():
    """获取正在运行的任务"""
    print('🔍 获取正在运行的训练任务')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 先查看表结构
        cursor.execute("PRAGMA table_info(training_tasks)")
        columns = cursor.fetchall()
        print("📋 数据库表结构:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # 查询running状态的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("\n❌ 没有找到正在运行的任务")
            return None
        
        print(f"\n📊 找到 {len(tasks)} 个正在运行的任务:")
        
        for i, task in enumerate(tasks, 1):
            task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs = task
            
            print(f"\n任务 {i}:")
            print(f"   ID: {task_id}")
            print(f"   模型ID: {model_id}")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            print(f"   轮次: {current_epoch}/{total_epochs}")
            print(f"   创建时间: {created_at}")
            print(f"   更新时间: {updated_at}")
            
            # 计算卡住时间
            if updated_at:
                try:
                    last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    now = datetime.now()
                    stuck_time = now - last_update.replace(tzinfo=None)
                    print(f"   距离上次更新: {stuck_time}")
                    
                    if stuck_time.total_seconds() > 300:  # 5分钟
                        print(f"   ⚠️ 任务卡住 (超过5分钟无更新)")
                    else:
                        print(f"   ✅ 任务正常")
                except Exception as e:
                    print(f"   ❌ 时间解析失败: {e}")
            
            # 解析日志
            if logs:
                try:
                    if isinstance(logs, str):
                        log_data = json.loads(logs)
                    else:
                        log_data = logs
                    
                    print(f"   最新日志:")
                    if 'epoch' in log_data:
                        print(f"     轮次: {log_data.get('epoch', 'N/A')}")
                    if 'train_loss' in log_data:
                        print(f"     训练损失: {log_data.get('train_loss', 'N/A'):.6f}")
                    if 'val_loss' in log_data:
                        print(f"     验证损失: {log_data.get('val_loss', 'N/A'):.6f}")
                    if 'train_acc' in log_data:
                        print(f"     训练准确率: {log_data.get('train_acc', 'N/A'):.4f}")
                    if 'val_acc' in log_data:
                        print(f"     验证准确率: {log_data.get('val_acc', 'N/A'):.4f}")
                    if 'patience_counter' in log_data:
                        print(f"     早停计数: {log_data.get('patience_counter', 'N/A')}")
                        
                except Exception as e:
                    print(f"   ❌ 日志解析失败: {e}")
        
        return tasks[0] if tasks else None  # 返回第一个任务
        
    except Exception as e:
        print(f"❌ 获取任务失败: {e}")
        return None

def check_system_status():
    """检查系统状态"""
    print('\n🔍 检查系统状态')
    print('=' * 50)
    
    try:
        # CPU和内存
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        print(f"💻 CPU使用率: {cpu_percent}%")
        print(f"🧠 内存使用率: {memory.percent}%")
        print(f"   总内存: {memory.total / (1024**3):.1f} GB")
        print(f"   已用内存: {memory.used / (1024**3):.1f} GB")
        print(f"   可用内存: {memory.available / (1024**3):.1f} GB")
        
        # 检查Python进程
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if any(keyword in cmdline.lower() for keyword in ['deep_learning', 'training', 'app.py']):
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cpu_percent': proc.info['cpu_percent'],
                            'memory_percent': proc.info['memory_percent'],
                            'cmdline': cmdline
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print(f"\n🐍 相关Python进程:")
        if python_processes:
            for proc in python_processes:
                print(f"   PID {proc['pid']}: {proc['name']}")
                print(f"     CPU: {proc['cpu_percent']}%, 内存: {proc['memory_percent']:.1f}%")
                print(f"     命令: {proc['cmdline'][:80]}...")
        else:
            print("   没有找到相关的Python进程")
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_available_gb': memory.available / (1024**3),
            'python_processes': len(python_processes)
        }
        
    except Exception as e:
        print(f"❌ 系统状态检查失败: {e}")
        return {}

def analyze_epoch_8_issue():
    """分析第8轮特定问题"""
    print('\n🔍 分析第8轮训练卡住的可能原因')
    print('=' * 50)
    
    possible_causes = [
        {
            'cause': '梯度爆炸',
            'description': '在第8轮训练时可能出现梯度爆炸，导致数值不稳定',
            'probability': 'high',
            'solution': '降低学习率，添加梯度裁剪 (max_grad_norm=1.0)'
        },
        {
            'cause': '内存泄漏',
            'description': '随着训练轮次增加，内存使用逐渐增长，在第8轮达到临界点',
            'probability': 'medium',
            'solution': '重启训练进程，减小batch_size'
        },
        {
            'cause': '数据加载问题',
            'description': '在第8轮时数据加载器可能遇到问题或死锁',
            'probability': 'medium',
            'solution': '检查数据完整性，重新准备数据'
        },
        {
            'cause': 'GPU内存不足',
            'description': '模型在第8轮时GPU内存使用达到上限',
            'probability': 'high',
            'solution': '减小batch_size，使用CPU训练，或清理GPU内存'
        },
        {
            'cause': '早停机制异常',
            'description': '早停机制在第8轮时可能出现异常判断',
            'probability': 'low',
            'solution': '检查早停逻辑，临时禁用早停'
        },
        {
            'cause': '模型复杂度过高',
            'description': '模型在第8轮时计算复杂度过高，导致单轮训练时间过长',
            'probability': 'medium',
            'solution': '减少模型层数，降低hidden_size'
        }
    ]
    
    print("🚨 第8轮训练卡住的可能原因:")
    for i, cause in enumerate(possible_causes, 1):
        prob_icon = '🔴' if cause['probability'] == 'high' else '🟡' if cause['probability'] == 'medium' else '🟢'
        print(f"\n{i}. {prob_icon} {cause['cause']} ({cause['probability'].upper()})")
        print(f"   描述: {cause['description']}")
        print(f"   解决方案: {cause['solution']}")
    
    return possible_causes

def provide_immediate_solutions():
    """提供立即解决方案"""
    print('\n🔧 立即解决方案')
    print('=' * 50)
    
    print("1. 🛑 立即停止卡住的训练:")
    print("   - 在训练页面点击'停止训练'按钮")
    print("   - 或者重启Flask应用")
    
    print("\n2. 🔄 重新开始训练 (优化参数):")
    print("   推荐参数调整:")
    print("   - batch_size: 改为 8 或 16 (降低内存使用)")
    print("   - learning_rate: 改为 0.0001 (降低学习率)")
    print("   - 添加梯度裁剪: max_grad_norm = 1.0")
    print("   - 减少epochs: 改为 50 (避免长时间训练)")
    
    print("\n3. 🧹 清理系统资源:")
    print("   - 重启Flask应用: Ctrl+C 然后重新运行 python app.py")
    print("   - 清理GPU内存: 重启系统或重启GPU驱动")
    print("   - 关闭其他占用内存的程序")
    
    print("\n4. 📊 监控训练过程:")
    print("   - 密切关注前几轮的训练指标")
    print("   - 如果在第8轮前出现异常，立即停止")
    print("   - 使用更小的数据集进行测试")

def create_optimized_config():
    """创建优化的训练配置"""
    print('\n⚙️ 推荐的优化训练配置')
    print('=' * 50)
    
    optimized_config = {
        'model_type': 'lstm',
        'sequence_length': 20,  # 保持不变
        'hidden_size': 32,      # 从64降低到32
        'num_layers': 1,        # 从2降低到1
        'dropout': 0.3,         # 增加dropout防止过拟合
        'batch_size': 8,        # 从16降低到8
        'learning_rate': 0.0001, # 降低学习率
        'epochs': 50,           # 减少总轮次
        'patience': 5,          # 增加早停耐心
        'use_gpu': False,       # 暂时使用CPU避免GPU问题
        'gradient_clip_norm': 1.0  # 添加梯度裁剪
    }
    
    print("📋 优化后的配置:")
    for key, value in optimized_config.items():
        print(f"   {key}: {value}")
    
    print("\n💡 配置说明:")
    print("   - 降低模型复杂度减少计算量")
    print("   - 减小batch_size降低内存使用")
    print("   - 降低学习率提高训练稳定性")
    print("   - 添加梯度裁剪防止梯度爆炸")
    print("   - 使用CPU训练避免GPU相关问题")

def main():
    """主函数"""
    print('🔧 第8轮训练卡住问题分析')
    print('=' * 80)
    
    # 获取运行中的任务
    running_task = get_running_tasks()
    
    # 检查系统状态
    system_status = check_system_status()
    
    # 分析第8轮特定问题
    causes = analyze_epoch_8_issue()
    
    # 提供解决方案
    provide_immediate_solutions()
    
    # 创建优化配置
    create_optimized_config()
    
    print(f"\n✅ 分析完成！")
    
    if running_task:
        print(f"💡 发现卡住的训练任务，建议:")
        print(f"   1. 立即停止当前训练")
        print(f"   2. 使用优化后的参数重新训练")
        print(f"   3. 密切监控前8轮的训练过程")
    else:
        print(f"💡 没有发现正在运行的任务")
        print(f"   可能任务已经自动停止或崩溃")

if __name__ == "__main__":
    main()
