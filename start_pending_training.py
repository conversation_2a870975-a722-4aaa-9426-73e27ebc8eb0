#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动等待中的训练任务
"""

import requests
import time
import sqlite3

def login_session():
    """登录并返回会话"""
    session = requests.Session()
    
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def get_data_ready_tasks():
    """获取数据准备完成的任务"""
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, model_id, status, progress
            FROM training_tasks 
            WHERE status = 'data_ready'
            ORDER BY created_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        return tasks
        
    except Exception as e:
        print(f"❌ 获取任务失败: {e}")
        return []

def start_model_training(session, task_id):
    """启动模型训练"""
    try:
        print(f"🚀 启动模型训练: {task_id}")
        
        response = session.post(f'http://127.0.0.1:5000/api/deep-learning/start-model-training/{task_id}',
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 模型训练启动成功: {task_id}")
                return True
            else:
                print(f"❌ 模型训练启动失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 启动训练异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 启动等待中的训练任务")
    print("=" * 50)
    
    # 登录
    session = login_session()
    if not session:
        return
    
    # 获取数据准备完成的任务
    tasks = get_data_ready_tasks()
    
    if not tasks:
        print("❌ 没有找到等待训练的任务")
        return
    
    print(f"📋 找到 {len(tasks)} 个等待训练的任务")
    
    # 启动每个任务的模型训练
    success_count = 0
    for task in tasks:
        task_id, model_id, status, progress = task
        
        print(f"\n📊 任务: {task_id}")
        print(f"   模型ID: {model_id}")
        print(f"   状态: {status}")
        print(f"   进度: {progress}%")
        
        if start_model_training(session, task_id):
            success_count += 1
            time.sleep(2)  # 等待2秒再启动下一个
    
    print(f"\n🎯 总结:")
    print(f"   总任务数: {len(tasks)}")
    print(f"   成功启动: {success_count}")
    print(f"   失败数量: {len(tasks) - success_count}")
    
    if success_count > 0:
        print(f"\n✅ 已启动 {success_count} 个训练任务")
        print(f"💡 可以在训练页面查看进度")
    else:
        print(f"\n❌ 没有成功启动任何训练任务")

if __name__ == "__main__":
    main()
