#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标库
基于MT4/MT5原生算法实现的专业技术指标
"""

import pandas as pd
import numpy as np
from typing import Dict, Union, Tuple
import logging

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """技术指标计算类"""
    
    def __init__(self):
        pass
    
    def sma(self, data: pd.Series, period: int) -> pd.Series:
        """
        简单移动平均线 (Simple Moving Average)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: SMA值
        """
        return data.rolling(window=period).mean()
    
    def ema(self, data: pd.Series, period: int) -> pd.Series:
        """
        指数移动平均线 (Exponential Moving Average)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: EMA值
        """
        return data.ewm(span=period, adjust=False).mean()
    
    def wma(self, data: pd.Series, period: int) -> pd.Series:
        """
        加权移动平均线 (Weighted Moving Average)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: WMA值
        """
        weights = np.arange(1, period + 1)
        return data.rolling(window=period).apply(
            lambda x: np.dot(x, weights) / weights.sum(), raw=True
        )
    
    def macd(self, data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """
        MACD指标 (Moving Average Convergence Divergence)
        
        Args:
            data: 价格数据
            fast: 快线周期
            slow: 慢线周期
            signal: 信号线周期
            
        Returns:
            Dict: 包含MACD线、信号线和柱状图的字典
        """
        ema_fast = self.ema(data, fast)
        ema_slow = self.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = self.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    def rsi(self, data: pd.Series, period: int = 14) -> pd.Series:
        """
        相对强弱指数 (Relative Strength Index)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: RSI值
        """
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def bollinger_bands(self, data: pd.Series, period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """
        布林带 (Bollinger Bands)
        
        Args:
            data: 价格数据
            period: 周期
            std_dev: 标准差倍数
            
        Returns:
            Dict: 包含上轨、中轨、下轨的字典
        """
        sma = self.sma(data, period)
        std = data.rolling(window=period).std()
        
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        return {
            'upper': upper,
            'middle': sma,
            'lower': lower
        }
    
    def stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                   k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """
        随机指标 (Stochastic Oscillator)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            k_period: %K周期
            d_period: %D周期
            
        Returns:
            Dict: 包含%K和%D的字典
        """
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return {
            '%K': k_percent,
            '%D': d_percent
        }
    
    def williams_r(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        威廉指标 (Williams %R)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            pd.Series: Williams %R值
        """
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
        
        return williams_r
    
    def atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        平均真实波幅 (Average True Range)

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期

        Returns:
            pd.Series: ATR值
        """
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())

        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()

        return atr

    def enhanced_bollinger_bands(self, data: pd.Series, period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """
        增强的布林带特征 - 专为深度学习模型优化

        Args:
            data: 价格数据
            period: 周期
            std_dev: 标准差倍数

        Returns:
            Dict: 包含所有布林带相关特征的字典
        """
        # 基础布林带
        sma = self.sma(data, period)
        std = data.rolling(window=period).std()

        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)

        # 布林带宽度
        band_width = (upper_band - lower_band) / sma

        # %B指标 (价格在布林带中的位置)
        percent_b = (data - lower_band) / (upper_band - lower_band)

        # 布林带挤压信号 - 当带宽处于历史低点时触发
        bb_squeeze = band_width < band_width.rolling(20).quantile(0.2)

        # 布林带突破信号
        bb_breakout = (data > upper_band) | (data < lower_band)

        # 价格与中轨的相对距离
        distance_from_middle = (data - sma) / sma

        # %B的变化率 (动量特征)
        percent_b_change = percent_b.diff()

        # 带宽的变化率 (波动性变化)
        band_width_change = band_width.diff()

        return {
            'upper': upper_band,
            'middle': sma,
            'lower': lower_band,
            'band_width': band_width,
            'percent_b': percent_b,
            'bb_squeeze': bb_squeeze,
            'bb_breakout': bb_breakout,
            'distance_from_middle': distance_from_middle,
            'percent_b_change': percent_b_change,
            'band_width_change': band_width_change
        }

    def enhanced_stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series,
                           k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """
        增强的随机指标 - 包含更多特征

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            k_period: %K周期
            d_period: %D周期

        Returns:
            Dict: 包含增强随机指标特征的字典
        """
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()

        # 基础随机指标
        stoch_k = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        stoch_d = stoch_k.rolling(window=d_period).mean()

        # %K和%D的差值 (动量特征)
        k_d_diff = stoch_k - stoch_d

        # %K的变化率
        stoch_k_change = stoch_k.diff()

        # 超买超卖信号
        overbought = stoch_k > 80
        oversold = stoch_k < 20

        # 金叉死叉信号
        golden_cross = (stoch_k > stoch_d) & (stoch_k.shift(1) <= stoch_d.shift(1))
        death_cross = (stoch_k < stoch_d) & (stoch_k.shift(1) >= stoch_d.shift(1))

        return {
            'stoch_k': stoch_k,
            'stoch_d': stoch_d,
            'k_d_diff': k_d_diff,
            'stoch_k_change': stoch_k_change,
            'overbought': overbought,
            'oversold': oversold,
            'golden_cross': golden_cross,
            'death_cross': death_cross
        }

    def enhanced_atr(self, high: pd.Series, low: pd.Series, close: pd.Series,
                     period: int = 14) -> Dict[str, pd.Series]:
        """
        增强的ATR特征 - 包含波动性分析

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期

        Returns:
            Dict: 包含ATR相关特征的字典
        """
        # 基础ATR
        atr = self.atr(high, low, close, period)

        # ATR的变化率
        atr_change = atr.diff()

        # ATR相对于价格的比率
        atr_ratio = atr / close

        # 波动性状态分类
        atr_percentile = atr.rolling(50).rank(pct=True)
        low_volatility = atr_percentile < 0.3
        high_volatility = atr_percentile > 0.7

        # 动态止损位 (基于ATR)
        dynamic_stop_long = close - (atr * 2)
        dynamic_stop_short = close + (atr * 2)

        return {
            'atr': atr,
            'atr_change': atr_change,
            'atr_ratio': atr_ratio,
            'atr_percentile': atr_percentile,
            'low_volatility': low_volatility,
            'high_volatility': high_volatility,
            'dynamic_stop_long': dynamic_stop_long,
            'dynamic_stop_short': dynamic_stop_short
        }

    def combined_signals(self, high: pd.Series, low: pd.Series, close: pd.Series) -> Dict[str, pd.Series]:
        """
        组合信号 - 多指标确认机制

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价

        Returns:
            Dict: 包含组合信号的字典
        """
        # 获取各个指标
        bb_data = self.enhanced_bollinger_bands(close)
        stoch_data = self.enhanced_stochastic(high, low, close)
        atr_data = self.enhanced_atr(high, low, close)

        # 组合信号1: 布林带挤压 + 低波动性
        squeeze_low_vol = bb_data['bb_squeeze'] & atr_data['low_volatility']

        # 组合信号2: 布林带突破 + 高波动性确认
        breakout_confirmed = bb_data['bb_breakout'] & atr_data['high_volatility']

        # 组合信号3: 随机指标超买超卖 + 布林带位置确认
        oversold_confirmed = stoch_data['oversold'] & (bb_data['percent_b'] < 0.2)
        overbought_confirmed = stoch_data['overbought'] & (bb_data['percent_b'] > 0.8)

        # 组合信号4: 多重确认的趋势信号
        bullish_confluence = (
            (bb_data['percent_b'] > 0.5) &
            (stoch_data['stoch_k'] > 50) &
            (bb_data['distance_from_middle'] > 0)
        )

        bearish_confluence = (
            (bb_data['percent_b'] < 0.5) &
            (stoch_data['stoch_k'] < 50) &
            (bb_data['distance_from_middle'] < 0)
        )

        # 组合信号5: 突破预警信号
        breakout_setup = (
            squeeze_low_vol &
            (bb_data['band_width'] < bb_data['band_width'].rolling(50).quantile(0.1))
        )

        return {
            'squeeze_low_vol': squeeze_low_vol,
            'breakout_confirmed': breakout_confirmed,
            'oversold_confirmed': oversold_confirmed,
            'overbought_confirmed': overbought_confirmed,
            'bullish_confluence': bullish_confluence,
            'bearish_confluence': bearish_confluence,
            'breakout_setup': breakout_setup
        }

    def market_regime_analysis(self, high: pd.Series, low: pd.Series, close: pd.Series,
                              volume: pd.Series = None) -> Dict[str, pd.Series]:
        """
        市场状态分析 - 识别不同的市场环境

        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            volume: 成交量 (可选)

        Returns:
            Dict: 包含市场状态特征的字典
        """
        # 获取技术指标
        bb_data = self.enhanced_bollinger_bands(close)
        atr_data = self.enhanced_atr(high, low, close)

        # 趋势强度 (基于价格与布林带中轨的关系)
        trend_strength = np.abs(bb_data['distance_from_middle'])

        # 市场状态分类
        trending_market = trend_strength > trend_strength.rolling(20).quantile(0.7)
        ranging_market = trend_strength < trend_strength.rolling(20).quantile(0.3)

        # 波动性状态
        volatility_regime = pd.cut(atr_data['atr_percentile'],
                                 bins=[0, 0.3, 0.7, 1.0],
                                 labels=['low_vol', 'medium_vol', 'high_vol'])

        # 趋势方向
        trend_direction = np.where(bb_data['distance_from_middle'] > 0, 1,
                                 np.where(bb_data['distance_from_middle'] < 0, -1, 0))

        # 市场效率 (基于价格在布林带中的分布)
        market_efficiency = 1 - np.abs(bb_data['percent_b'] - 0.5) * 2

        # 动量状态
        momentum_state = pd.Series(index=close.index, dtype='object')
        momentum_state.loc[bb_data['percent_b_change'] > 0.1] = 'accelerating_up'
        momentum_state.loc[bb_data['percent_b_change'] < -0.1] = 'accelerating_down'
        momentum_state.loc[np.abs(bb_data['percent_b_change']) <= 0.1] = 'stable'

        result = {
            'trend_strength': trend_strength,
            'trending_market': trending_market,
            'ranging_market': ranging_market,
            'trend_direction': pd.Series(trend_direction, index=close.index),
            'market_efficiency': market_efficiency,
            'momentum_state': momentum_state
        }

        # 如果有成交量数据，添加成交量相关特征
        if volume is not None:
            volume_ma = volume.rolling(20).mean()
            volume_ratio = volume / volume_ma

            # 成交量确认
            volume_confirmed_trend = (
                (trend_direction != 0) & (volume_ratio > 1.2)
            )

            result.update({
                'volume_ratio': volume_ratio,
                'volume_confirmed_trend': volume_confirmed_trend
            })

        return result
    
    def cci(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """
        商品通道指数 (Commodity Channel Index)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            pd.Series: CCI值
        """
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mean_deviation = typical_price.rolling(window=period).apply(
            lambda x: np.mean(np.abs(x - x.mean())), raw=True
        )
        
        cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
        
        return cci
    
    def adx(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> Dict[str, pd.Series]:
        """
        平均趋向指数 (Average Directional Index)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            Dict: 包含ADX、DI+、DI-的字典
        """
        # 计算真实波幅
        atr_values = self.atr(high, low, close, period)
        
        # 计算方向移动
        up_move = high - high.shift()
        down_move = low.shift() - low
        
        plus_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
        minus_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)
        
        plus_dm = pd.Series(plus_dm, index=high.index)
        minus_dm = pd.Series(minus_dm, index=high.index)
        
        # 平滑处理
        plus_dm_smooth = plus_dm.rolling(window=period).mean()
        minus_dm_smooth = minus_dm.rolling(window=period).mean()
        
        # 计算DI
        plus_di = 100 * (plus_dm_smooth / atr_values)
        minus_di = 100 * (minus_dm_smooth / atr_values)
        
        # 计算ADX
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=period).mean()
        
        return {
            'ADX': adx,
            'DI+': plus_di,
            'DI-': minus_di
        }
    
    def obv(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        能量潮 (On Balance Volume)
        
        Args:
            close: 收盘价
            volume: 成交量
            
        Returns:
            pd.Series: OBV值
        """
        price_change = close.diff()
        obv_values = []
        obv_current = 0
        
        for i, (price_diff, vol) in enumerate(zip(price_change, volume)):
            if i == 0:
                obv_values.append(vol)
                obv_current = vol
            elif price_diff > 0:
                obv_current += vol
                obv_values.append(obv_current)
            elif price_diff < 0:
                obv_current -= vol
                obv_values.append(obv_current)
            else:
                obv_values.append(obv_current)
        
        return pd.Series(obv_values, index=close.index)
    
    def momentum(self, data: pd.Series, period: int = 10) -> pd.Series:
        """
        动量指标 (Momentum)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: 动量值
        """
        return data - data.shift(period)
    
    def roc(self, data: pd.Series, period: int = 10) -> pd.Series:
        """
        变化率 (Rate of Change)
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            pd.Series: ROC值
        """
        return ((data - data.shift(period)) / data.shift(period)) * 100
    
    def ichimoku(self, high: pd.Series, low: pd.Series, close: pd.Series) -> Dict[str, pd.Series]:
        """
        一目均衡表 (Ichimoku Cloud)
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            
        Returns:
            Dict: 包含各条线的字典
        """
        # 转换线 (Tenkan-sen)
        tenkan_sen = (high.rolling(9).max() + low.rolling(9).min()) / 2
        
        # 基准线 (Kijun-sen)
        kijun_sen = (high.rolling(26).max() + low.rolling(26).min()) / 2
        
        # 先行带A (Senkou Span A)
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(26)
        
        # 先行带B (Senkou Span B)
        senkou_span_b = ((high.rolling(52).max() + low.rolling(52).min()) / 2).shift(26)
        
        # 滞后线 (Chikou Span)
        chikou_span = close.shift(-26)
        
        return {
            'tenkan_sen': tenkan_sen,
            'kijun_sen': kijun_sen,
            'senkou_span_a': senkou_span_a,
            'senkou_span_b': senkou_span_b,
            'chikou_span': chikou_span
        }
    
    def fibonacci_retracement(self, high_price: float, low_price: float) -> Dict[str, float]:
        """
        斐波那契回调 (Fibonacci Retracement)
        
        Args:
            high_price: 最高价
            low_price: 最低价
            
        Returns:
            Dict: 斐波那契回调水平
        """
        diff = high_price - low_price
        
        levels = {
            '0%': high_price,
            '23.6%': high_price - 0.236 * diff,
            '38.2%': high_price - 0.382 * diff,
            '50%': high_price - 0.5 * diff,
            '61.8%': high_price - 0.618 * diff,
            '78.6%': high_price - 0.786 * diff,
            '100%': low_price
        }
        
        return levels
