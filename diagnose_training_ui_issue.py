#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断训练界面问题
检查为什么没有显示"开始模型训练"按钮
"""

import sqlite3
import json
from datetime import datetime

def check_data_ready_tasks():
    """检查data_ready状态的任务"""
    print('🔍 检查data_ready状态的任务')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询data_ready状态的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs
            FROM training_tasks 
            WHERE status = 'data_ready'
            ORDER BY created_at DESC
        ''')
        
        tasks = cursor.fetchall()
        
        if tasks:
            print(f"📋 找到 {len(tasks)} 个data_ready状态的任务:")
            
            for i, task in enumerate(tasks, 1):
                task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs = task
                
                print(f"\n📊 任务 {i}: {task_id}")
                print(f"   模型ID: {model_id}")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   轮次: {current_epoch}/{total_epochs}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                
                # 解析日志
                if logs:
                    try:
                        if isinstance(logs, str):
                            log_data = json.loads(logs)
                        else:
                            log_data = logs
                        
                        print(f"   阶段: {log_data.get('stage', 'unknown')}")
                        print(f"   消息: {log_data.get('message', 'N/A')}")
                        
                        # 检查数据信息
                        data_info = log_data.get('data_info', {})
                        if data_info:
                            print(f"   数据形状: {data_info}")
                            print(f"   ✅ 数据准备完成，应该显示'开始模型训练'按钮")
                        else:
                            print(f"   ⚠️ 缺少数据信息")
                            
                    except Exception as e:
                        print(f"   ❌ 日志解析失败: {e}")
                        print(f"   原始日志: {logs}")
        else:
            print("❌ 没有找到data_ready状态的任务")
        
        conn.close()
        return tasks
        
    except Exception as e:
        print(f"❌ 检查data_ready任务失败: {e}")
        return []

def check_running_task():
    """检查正在运行但卡住的任务"""
    print('\n🔍 检查正在运行的任务')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询running状态的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY created_at DESC
        ''')
        
        tasks = cursor.fetchall()
        
        if tasks:
            print(f"📋 找到 {len(tasks)} 个running状态的任务:")
            
            for i, task in enumerate(tasks, 1):
                task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs = task
                
                print(f"\n📊 任务 {i}: {task_id}")
                print(f"   模型ID: {model_id}")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   轮次: {current_epoch}/{total_epochs}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                
                # 检查是否卡住
                if updated_at:
                    try:
                        last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                        now = datetime.now()
                        time_diff = now - last_update.replace(tzinfo=None)
                        
                        print(f"   距离上次更新: {time_diff}")
                        
                        if time_diff.total_seconds() > 300:  # 5分钟
                            print(f"   ⚠️ 任务可能卡住 (超过5分钟无更新)")
                        else:
                            print(f"   ✅ 任务正常运行")
                            
                    except Exception as e:
                        print(f"   ❌ 时间解析失败: {e}")
                
                # 解析日志
                if logs:
                    try:
                        if isinstance(logs, str):
                            log_data = json.loads(logs)
                        else:
                            log_data = logs
                        
                        print(f"   阶段: {log_data.get('stage', 'unknown')}")
                        print(f"   消息: {log_data.get('message', 'N/A')}")
                        
                        # 检查训练信息
                        if 'epoch' in log_data:
                            print(f"   当前轮次: {log_data.get('epoch', 'N/A')}")
                        if 'train_loss' in log_data:
                            print(f"   训练损失: {log_data.get('train_loss', 'N/A')}")
                            
                    except Exception as e:
                        print(f"   ❌ 日志解析失败: {e}")
                        print(f"   原始日志: {logs}")
        else:
            print("❌ 没有找到running状态的任务")
        
        conn.close()
        return tasks
        
    except Exception as e:
        print(f"❌ 检查running任务失败: {e}")
        return []

def check_frontend_api():
    """检查前端API响应"""
    print('\n🔍 检查前端API响应')
    print('=' * 50)
    
    try:
        import requests
        
        # 登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                     data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code == 200:
            print("✅ 登录成功")
            
            # 获取训练任务列表
            response = session.get('http://127.0.0.1:5000/api/deep-learning/training-tasks')
            
            if response.status_code == 200:
                tasks = response.json()
                print(f"📋 API返回 {len(tasks)} 个任务:")
                
                for task in tasks:
                    task_id = task.get('id', 'unknown')
                    status = task.get('status', 'unknown')
                    progress = task.get('progress', 0)
                    
                    print(f"   任务 {task_id[:8]}...: {status} ({progress}%)")
                    
                    # 检查是否应该显示开始训练按钮
                    if status == 'data_ready' and progress == 100:
                        print(f"     ✅ 应该显示'开始模型训练'按钮")
                    elif status == 'running' and progress == 25:
                        print(f"     ⚠️ 训练可能卡住在25%")
                        
            else:
                print(f"❌ API请求失败: {response.status_code}")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查前端API失败: {e}")

def analyze_ui_issue():
    """分析UI问题"""
    print('\n🔍 分析UI问题')
    print('=' * 50)
    
    print("可能的原因:")
    print("1. 前端JavaScript没有正确处理data_ready状态")
    print("2. 前端轮询间隔太长，没有及时更新状态")
    print("3. 前端条件判断有误，没有显示按钮")
    print("4. CSS样式问题，按钮被隐藏")
    print("5. 后端API返回的数据格式有问题")
    
    print("\n建议的解决方案:")
    print("1. 检查前端JavaScript中的状态处理逻辑")
    print("2. 检查按钮显示的条件判断")
    print("3. 手动启动模型训练API")
    print("4. 重新加载训练页面")
    print("5. 清除浏览器缓存")

def provide_manual_solutions():
    """提供手动解决方案"""
    print('\n🔧 手动解决方案')
    print('=' * 50)
    
    # 获取data_ready任务
    conn = sqlite3.connect('trading_system.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id FROM training_tasks 
        WHERE status = 'data_ready'
        ORDER BY created_at DESC
    ''')
    
    data_ready_tasks = cursor.fetchall()
    
    if data_ready_tasks:
        print("📋 可以手动启动的任务:")
        for task in data_ready_tasks:
            task_id = task[0]
            print(f"   任务ID: {task_id}")
            print(f"   启动命令: python start_pending_training.py")
            print(f"   或者直接调用API:")
            print(f"   curl -X POST http://127.0.0.1:5000/api/deep-learning/start-model-training/{task_id}")
    
    # 检查卡住的running任务
    cursor.execute('''
        SELECT id FROM training_tasks 
        WHERE status = 'running'
        ORDER BY created_at DESC
    ''')
    
    running_tasks = cursor.fetchall()
    
    if running_tasks:
        print("\n📋 可能需要重启的任务:")
        for task in running_tasks:
            task_id = task[0]
            print(f"   任务ID: {task_id}")
            print(f"   停止命令: 更新数据库状态为failed")
            print(f"   重启命令: 重新创建训练任务")
    
    conn.close()

def main():
    """主函数"""
    print('🔧 训练界面问题诊断')
    print('=' * 80)
    
    # 检查data_ready任务
    data_ready_tasks = check_data_ready_tasks()
    
    # 检查running任务
    running_tasks = check_running_task()
    
    # 检查前端API
    check_frontend_api()
    
    # 分析UI问题
    analyze_ui_issue()
    
    # 提供手动解决方案
    provide_manual_solutions()
    
    print(f"\n✅ 诊断完成！")
    
    if data_ready_tasks:
        print(f"💡 主要问题: {len(data_ready_tasks)} 个任务数据准备完成但没有显示开始训练按钮")
        print(f"🔧 建议: 使用 python start_pending_training.py 手动启动")
    
    if running_tasks:
        print(f"💡 次要问题: {len(running_tasks)} 个任务可能卡在训练阶段")
        print(f"🔧 建议: 检查训练进程和GPU使用情况")

if __name__ == "__main__":
    main()
