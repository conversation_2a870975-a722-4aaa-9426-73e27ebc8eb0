#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态风险管理服务
基于ATR和市场波动性的自适应风险管理系统
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
from services.technical_indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

class DynamicRiskManager:
    """动态风险管理器"""
    
    def __init__(self):
        self.ti = TechnicalIndicators()
    
    def calculate_dynamic_stops(self, high: pd.Series, low: pd.Series, close: pd.Series,
                              atr_multiplier: float = 2.0, min_stop_pips: int = 10) -> Dict[str, pd.Series]:
        """
        计算动态止损位
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            atr_multiplier: ATR倍数
            min_stop_pips: 最小止损点数
            
        Returns:
            Dict: 包含动态止损位的字典
        """
        logger.info(f"📏 计算动态止损位 (ATR倍数: {atr_multiplier})")
        
        # 计算ATR
        atr = self.ti.atr(high, low, close)
        
        # 动态止损距离 (以ATR为基础)
        stop_distance = atr * atr_multiplier
        
        # 确保最小止损距离
        min_stop_distance = close * (min_stop_pips / 10000)  # 转换为价格单位
        stop_distance = np.maximum(stop_distance, min_stop_distance)
        
        # 计算止损位
        long_stop = close - stop_distance
        short_stop = close + stop_distance
        
        # 动态止盈位 (1.5倍止损距离)
        take_profit_distance = stop_distance * 1.5
        long_take_profit = close + take_profit_distance
        short_take_profit = close - take_profit_distance
        
        return {
            'atr': atr,
            'stop_distance': stop_distance,
            'long_stop': long_stop,
            'short_stop': short_stop,
            'long_take_profit': long_take_profit,
            'short_take_profit': short_take_profit
        }
    
    def calculate_position_sizing(self, account_balance: float, risk_per_trade: float,
                                stop_distance: pd.Series, close: pd.Series) -> pd.Series:
        """
        计算基于风险的仓位大小
        
        Args:
            account_balance: 账户余额
            risk_per_trade: 每笔交易风险比例 (如0.02表示2%)
            stop_distance: 止损距离
            close: 收盘价
            
        Returns:
            pd.Series: 建议的仓位大小
        """
        logger.info(f"💰 计算动态仓位大小 (风险比例: {risk_per_trade*100:.1f}%)")
        
        # 每笔交易的风险金额
        risk_amount = account_balance * risk_per_trade
        
        # 计算仓位大小 = 风险金额 / 止损距离
        position_size = risk_amount / stop_distance
        
        # 限制最大仓位 (不超过账户的10%)
        max_position_value = account_balance * 0.1
        max_position_size = max_position_value / close
        
        position_size = np.minimum(position_size, max_position_size)
        
        return position_size
    
    def calculate_volatility_adjusted_parameters(self, high: pd.Series, low: pd.Series, 
                                               close: pd.Series) -> Dict[str, pd.Series]:
        """
        计算基于波动性调整的参数
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            
        Returns:
            Dict: 包含波动性调整参数的字典
        """
        logger.info("📊 计算波动性调整参数...")
        
        # 获取增强ATR特征
        atr_features = self.ti.enhanced_atr(high, low, close)
        
        # 波动性状态
        volatility_state = pd.Series(index=close.index, dtype='object')
        volatility_state.loc[atr_features['low_volatility']] = 'low'
        volatility_state.loc[atr_features['high_volatility']] = 'high'
        volatility_state.loc[~(atr_features['low_volatility'] | atr_features['high_volatility'])] = 'medium'
        
        # 根据波动性调整参数
        atr_multiplier = pd.Series(index=close.index, dtype=float)
        atr_multiplier.loc[volatility_state == 'low'] = 1.5    # 低波动性时使用较小的止损
        atr_multiplier.loc[volatility_state == 'medium'] = 2.0  # 中等波动性时使用标准止损
        atr_multiplier.loc[volatility_state == 'high'] = 2.5   # 高波动性时使用较大的止损
        
        # 风险调整系数
        risk_adjustment = pd.Series(index=close.index, dtype=float)
        risk_adjustment.loc[volatility_state == 'low'] = 1.2    # 低波动性时可以增加风险
        risk_adjustment.loc[volatility_state == 'medium'] = 1.0  # 中等波动性时标准风险
        risk_adjustment.loc[volatility_state == 'high'] = 0.8   # 高波动性时降低风险
        
        return {
            'volatility_state': volatility_state,
            'atr_multiplier': atr_multiplier,
            'risk_adjustment': risk_adjustment,
            'atr_percentile': atr_features['atr_percentile']
        }
    
    def generate_risk_signals(self, high: pd.Series, low: pd.Series, close: pd.Series,
                            current_position: str = 'none') -> Dict[str, pd.Series]:
        """
        生成风险管理信号
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            current_position: 当前持仓状态 ('long', 'short', 'none')
            
        Returns:
            Dict: 包含风险管理信号的字典
        """
        logger.info("🚨 生成风险管理信号...")
        
        # 获取动态止损位
        stop_data = self.calculate_dynamic_stops(high, low, close)
        
        # 获取波动性参数
        vol_data = self.calculate_volatility_adjusted_parameters(high, low, close)
        
        # 风险警告信号
        risk_warnings = pd.Series(False, index=close.index)
        
        # 高波动性警告
        high_vol_warning = vol_data['volatility_state'] == 'high'
        risk_warnings |= high_vol_warning
        
        # ATR急剧上升警告
        atr_spike = stop_data['atr'].pct_change() > 0.5  # ATR增长超过50%
        risk_warnings |= atr_spike
        
        # 止损建议
        stop_loss_signals = pd.Series('hold', index=close.index)
        
        if current_position == 'long':
            # 多头止损信号
            stop_loss_signals.loc[close <= stop_data['long_stop']] = 'close_long'
            stop_loss_signals.loc[close >= stop_data['long_take_profit']] = 'take_profit_long'
        elif current_position == 'short':
            # 空头止损信号
            stop_loss_signals.loc[close >= stop_data['short_stop']] = 'close_short'
            stop_loss_signals.loc[close <= stop_data['short_take_profit']] = 'take_profit_short'
        
        # 仓位调整建议
        position_adjustment = pd.Series('maintain', index=close.index)
        position_adjustment.loc[vol_data['volatility_state'] == 'high'] = 'reduce'
        position_adjustment.loc[vol_data['volatility_state'] == 'low'] = 'increase'
        
        return {
            'risk_warnings': risk_warnings,
            'stop_loss_signals': stop_loss_signals,
            'position_adjustment': position_adjustment,
            'volatility_state': vol_data['volatility_state'],
            'atr_multiplier': vol_data['atr_multiplier'],
            'dynamic_stops': stop_data
        }
    
    def calculate_risk_metrics(self, returns: pd.Series, benchmark_returns: pd.Series = None) -> Dict[str, float]:
        """
        计算风险指标
        
        Args:
            returns: 策略收益率序列
            benchmark_returns: 基准收益率序列 (可选)
            
        Returns:
            Dict: 包含各种风险指标的字典
        """
        logger.info("📈 计算风险指标...")
        
        metrics = {}
        
        # 基础统计指标
        metrics['total_return'] = (1 + returns).prod() - 1
        metrics['annualized_return'] = (1 + returns.mean()) ** 252 - 1
        metrics['volatility'] = returns.std() * np.sqrt(252)
        
        # 夏普比率
        risk_free_rate = 0.02  # 假设无风险利率为2%
        excess_returns = returns.mean() * 252 - risk_free_rate
        metrics['sharpe_ratio'] = excess_returns / metrics['volatility'] if metrics['volatility'] > 0 else 0
        
        # 最大回撤
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        metrics['max_drawdown'] = drawdown.min()
        
        # VaR (Value at Risk) - 95%置信度
        metrics['var_95'] = returns.quantile(0.05)
        
        # 胜率
        win_rate = (returns > 0).mean()
        metrics['win_rate'] = win_rate
        
        # 盈亏比
        winning_trades = returns[returns > 0]
        losing_trades = returns[returns < 0]
        
        if len(winning_trades) > 0 and len(losing_trades) > 0:
            avg_win = winning_trades.mean()
            avg_loss = abs(losing_trades.mean())
            metrics['profit_loss_ratio'] = avg_win / avg_loss if avg_loss > 0 else 0
        else:
            metrics['profit_loss_ratio'] = 0
        
        # 卡尔马比率 (年化收益率 / 最大回撤)
        metrics['calmar_ratio'] = (metrics['annualized_return'] / abs(metrics['max_drawdown']) 
                                 if metrics['max_drawdown'] != 0 else 0)
        
        # 如果有基准收益率，计算相对指标
        if benchmark_returns is not None:
            benchmark_total_return = (1 + benchmark_returns).prod() - 1
            metrics['excess_return'] = metrics['total_return'] - benchmark_total_return
            
            # 信息比率
            active_returns = returns - benchmark_returns
            tracking_error = active_returns.std() * np.sqrt(252)
            metrics['information_ratio'] = (active_returns.mean() * 252 / tracking_error 
                                          if tracking_error > 0 else 0)
        
        return metrics
    
    def create_risk_report(self, high: pd.Series, low: pd.Series, close: pd.Series,
                          returns: pd.Series = None) -> Dict[str, any]:
        """
        创建综合风险报告
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            returns: 收益率序列 (可选)
            
        Returns:
            Dict: 综合风险报告
        """
        logger.info("📋 生成综合风险报告...")
        
        report = {}
        
        # 市场风险分析
        vol_data = self.calculate_volatility_adjusted_parameters(high, low, close)
        report['market_risk'] = {
            'current_volatility_state': vol_data['volatility_state'].iloc[-1],
            'atr_percentile': vol_data['atr_percentile'].iloc[-1],
            'recommended_atr_multiplier': vol_data['atr_multiplier'].iloc[-1]
        }
        
        # 动态止损建议
        stop_data = self.calculate_dynamic_stops(high, low, close)
        current_price = close.iloc[-1]
        report['stop_loss_recommendations'] = {
            'long_stop_loss': stop_data['long_stop'].iloc[-1],
            'short_stop_loss': stop_data['short_stop'].iloc[-1],
            'long_take_profit': stop_data['long_take_profit'].iloc[-1],
            'short_take_profit': stop_data['short_take_profit'].iloc[-1],
            'stop_distance_pips': int((stop_data['stop_distance'].iloc[-1] / current_price) * 10000)
        }
        
        # 如果有收益率数据，计算风险指标
        if returns is not None:
            report['performance_metrics'] = self.calculate_risk_metrics(returns)
        
        # 风险建议
        risk_signals = self.generate_risk_signals(high, low, close)
        report['risk_recommendations'] = {
            'risk_warning': bool(risk_signals['risk_warnings'].iloc[-1]),
            'position_adjustment': risk_signals['position_adjustment'].iloc[-1],
            'volatility_regime': risk_signals['volatility_state'].iloc[-1]
        }
        
        logger.info("✅ 风险报告生成完成")
        return report
