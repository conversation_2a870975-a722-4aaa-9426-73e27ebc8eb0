#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

import sqlite3

def check_training_tasks_schema():
    """检查training_tasks表结构"""
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(training_tasks)")
        columns = cursor.fetchall()
        
        print("📊 training_tasks表结构:")
        for column in columns:
            cid, name, type_, notnull, default_value, pk = column
            print(f"   {name}: {type_}")
        
        # 检查是否有运行中的任务
        cursor.execute("SELECT * FROM training_tasks WHERE status = 'running' LIMIT 5")
        tasks = cursor.fetchall()
        
        if tasks:
            print(f"\n📋 运行中的任务示例:")
            for task in tasks:
                print(f"   任务: {task}")
        else:
            print(f"\n✅ 没有运行中的任务")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_training_tasks_schema()
