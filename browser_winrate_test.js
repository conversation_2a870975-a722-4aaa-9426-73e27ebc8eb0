
// 胜率显示修复测试脚本
console.log("🧪 开始测试胜率显示修复...");

// 1. 检查修复函数是否存在
console.log("📋 检查修复函数:");
const functions = ['formatWinRate', 'formatMaxDrawdown'];
functions.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName}: 函数存在`);
    } else {
        console.log(`❌ ${funcName}: 函数不存在`);
    }
});

// 2. 测试胜率格式化函数
console.log("📋 测试胜率格式化:");
if (typeof formatWinRate === 'function') {
    const testWinRates = [
        0.65,      // 小数形式 (65%)
        65,        // 百分比形式 (65%)
        0.4081,    // 小数形式 (40.81%)
        40.81,     // 百分比形式 (40.81%)
        4081.6,    // 错误的百分比 (4081.6%)
        0,         // 零值
        1,         // 边界值
        null,      // 空值
        undefined, // 未定义
        'invalid'  // 无效值
    ];
    
    testWinRates.forEach(rate => {
        const result = formatWinRate(rate);
        console.log(`   输入: ${rate} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatWinRate函数不存在");
}

// 3. 测试最大回撤格式化
console.log("📋 测试最大回撤格式化:");
if (typeof formatMaxDrawdown === 'function') {
    const testDrawdowns = [
        -0.05,     // 小数形式 (-5%)
        -5,        // 百分比形式 (5%)
        0.05,      // 正数小数形式 (5%)
        5,         // 正数百分比形式 (5%)
        -0.1234,   // 小数形式 (-12.34%)
        -12.34,    // 百分比形式 (12.34%)
        0,         // 零值
        null,      // 空值
        undefined, // 未定义
        'invalid'  // 无效值
    ];
    
    testDrawdowns.forEach(drawdown => {
        const result = formatMaxDrawdown(drawdown);
        console.log(`   输入: ${drawdown} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatMaxDrawdown函数不存在");
}

// 4. 测试统计显示函数
console.log("📋 测试统计显示函数:");
if (typeof displayBacktestStats === 'function') {
    // 创建模拟统计数据
    const mockStats1 = {
        total_profit: 1000.50,
        win_rate: 0.65,        // 小数形式
        profit_factor: 1.73,
        max_drawdown: -0.1046, // 小数形式
        final_balance: 11000.50,
        total_trades: 49,
        winning_trades: 30,
        losing_trades: 19
    };
    
    const mockStats2 = {
        total_profit: -500.25,
        win_rate: 40.81,       // 百分比形式（错误）
        profit_factor: 0.85,
        max_drawdown: -10.46,  // 百分比形式
        final_balance: 9500.25,
        total_trades: 49,
        winning_trades: 20,
        losing_trades: 29
    };
    
    console.log("🔄 测试小数形式数据...");
    console.log("模拟数据1:", mockStats1);
    displayBacktestStats(mockStats1);
    
    setTimeout(() => {
        console.log("🔄 测试百分比形式数据...");
        console.log("模拟数据2:", mockStats2);
        displayBacktestStats(mockStats2);
        
        setTimeout(() => {
            console.log("✅ 统计显示测试完成");
            console.log("💡 请检查页面上的胜率和最大回撤显示是否正确");
        }, 1000);
    }, 2000);
} else {
    console.log("❌ displayBacktestStats函数不存在");
}

// 5. 检查页面元素
console.log("📋 检查页面元素:");
const elements = ['backtestStats'];
elements.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ ${id}: 元素存在`);
    } else {
        console.log(`❌ ${id}: 元素不存在`);
    }
});

console.log("🎉 胜率显示修复测试完成！");
console.log("💡 预期结果:");
console.log("   - 胜率应该在0-100%之间显示");
console.log("   - 最大回撤应该显示为正数百分比");
console.log("   - 不应该出现4081.6%这样的错误显示");
