#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标在深度学习模型中的简化演示
展示布林带等技术指标如何计算并输入模型
"""

import numpy as np
import pandas as pd

def calculate_bollinger_bands(prices, period=20, std_multiplier=2):
    """
    计算布林带
    这是深度学习模型中最重要的技术指标之一
    """
    print(f"📈 计算布林带 (周期={period}, 标准差倍数={std_multiplier})")
    
    # 转换为pandas Series
    price_series = pd.Series(prices)
    
    # 1. 中轨 = 简单移动平均
    middle_band = price_series.rolling(window=period).mean()
    
    # 2. 标准差
    rolling_std = price_series.rolling(window=period).std()
    
    # 3. 上轨和下轨
    upper_band = middle_band + (rolling_std * std_multiplier)
    lower_band = middle_band - (rolling_std * std_multiplier)
    
    # 4. %B指标 (价格在布林带中的位置)
    percent_b = (price_series - lower_band) / (upper_band - lower_band)
    
    # 5. 带宽
    band_width = (upper_band - lower_band) / middle_band
    
    print(f"✅ 布林带计算完成")
    print(f"   上轨范围: {upper_band.min():.2f} - {upper_band.max():.2f}")
    print(f"   下轨范围: {lower_band.min():.2f} - {lower_band.max():.2f}")
    print(f"   %B范围: {percent_b.min():.3f} - {percent_b.max():.3f}")
    
    return {
        'upper': upper_band,
        'middle': middle_band,
        'lower': lower_band,
        'percent_b': percent_b,
        'band_width': band_width
    }

def calculate_other_indicators(prices):
    """计算其他重要技术指标"""
    print(f"\n📊 计算其他技术指标")
    
    price_series = pd.Series(prices)
    indicators = {}
    
    # 1. RSI
    price_changes = price_series.diff()
    gains = price_changes.where(price_changes > 0, 0)
    losses = -price_changes.where(price_changes < 0, 0)
    
    avg_gains = gains.rolling(14).mean()
    avg_losses = losses.rolling(14).mean()
    
    rs = avg_gains / (avg_losses + 1e-10)
    rsi = 100 - (100 / (1 + rs))
    indicators['rsi'] = rsi.fillna(50)
    
    # 2. MACD
    ema_12 = price_series.ewm(span=12).mean()
    ema_26 = price_series.ewm(span=26).mean()
    macd_line = ema_12 - ema_26
    macd_signal = macd_line.ewm(span=9).mean()
    
    indicators['macd_line'] = macd_line
    indicators['macd_signal'] = macd_signal
    indicators['macd_histogram'] = macd_line - macd_signal
    
    # 3. 移动平均线
    indicators['sma_5'] = price_series.rolling(5).mean()
    indicators['sma_20'] = price_series.rolling(20).mean()
    indicators['ema_12'] = ema_12
    indicators['ema_26'] = ema_26
    
    print(f"✅ 其他指标计算完成: RSI, MACD, 移动平均线")
    
    return indicators

def prepare_model_features(prices, bb_data, other_indicators):
    """
    为深度学习模型准备特征
    这是模型训练的核心步骤
    """
    print(f"\n🤖 为深度学习模型准备特征")
    print("=" * 40)
    
    features = []
    feature_names = []
    
    # 1. 原始价格特征 (标准化)
    price_normalized = (prices - prices.mean()) / (prices.std() + 1e-8)
    features.append(price_normalized.reshape(-1, 1))
    feature_names.append('price_normalized')
    
    # 2. 布林带特征 (最重要)
    bb_features = ['percent_b', 'band_width']
    for feature_name in bb_features:
        if feature_name in bb_data:
            feature_data = bb_data[feature_name].fillna(0).values
            # 标准化
            normalized = (feature_data - feature_data.mean()) / (feature_data.std() + 1e-8)
            features.append(normalized.reshape(-1, 1))
            feature_names.append(f'bb_{feature_name}')
            print(f"   布林带{feature_name}: 范围 {normalized.min():.3f} - {normalized.max():.3f}")
    
    # 3. 其他技术指标特征
    important_indicators = ['rsi', 'macd_line', 'macd_histogram']
    for indicator in important_indicators:
        if indicator in other_indicators:
            feature_data = other_indicators[indicator].fillna(0).values
            # 标准化
            normalized = (feature_data - feature_data.mean()) / (feature_data.std() + 1e-8)
            features.append(normalized.reshape(-1, 1))
            feature_names.append(indicator)
            print(f"   {indicator}: 范围 {normalized.min():.3f} - {normalized.max():.3f}")
    
    # 4. 合并所有特征
    feature_matrix = np.concatenate(features, axis=1)
    
    print(f"\n✅ 特征矩阵准备完成:")
    print(f"   形状: {feature_matrix.shape}")
    print(f"   特征列表: {feature_names}")
    
    return feature_matrix, feature_names

def create_lstm_sequences(feature_matrix, sequence_length=60):
    """
    为LSTM模型创建序列数据
    这是时间序列深度学习的标准做法
    """
    print(f"\n🔄 创建LSTM序列 (序列长度: {sequence_length})")
    print("-" * 40)
    
    X, y = [], []
    
    for i in range(sequence_length, len(feature_matrix)):
        # 输入序列：过去sequence_length个时间步的所有特征
        sequence = feature_matrix[i-sequence_length:i]
        X.append(sequence)
        
        # 目标：下一个时间步的价格变化方向
        current_price = feature_matrix[i-1, 0]  # 标准化价格特征
        next_price = feature_matrix[i, 0]
        
        # 1: 上涨, 0: 下跌
        target = 1 if next_price > current_price else 0
        y.append(target)
    
    X = np.array(X)
    y = np.array(y)
    
    print(f"✅ LSTM序列创建完成:")
    print(f"   输入形状: {X.shape} (样本数, 序列长度, 特征数)")
    print(f"   目标形状: {y.shape}")
    print(f"   上涨样本: {np.sum(y)} ({np.mean(y)*100:.1f}%)")
    print(f"   下跌样本: {len(y) - np.sum(y)} ({(1-np.mean(y))*100:.1f}%)")
    
    return X, y

def show_actual_code_implementation():
    """展示实际代码中的实现"""
    print(f"\n💻 实际代码实现展示")
    print("=" * 50)
    
    code_example = '''
# 在 services/deep_learning_service.py 中的实际实现

def _calculate_features_from_dict(self, price_data: np.ndarray, features_config: dict):
    """计算技术指标特征"""
    
    features = []
    
    # 1. 价格特征
    if features_config.get('price', True):
        normalized_prices = (price_data - price_data.mean(axis=0)) / (price_data.std(axis=0) + 1e-10)
        features.append(normalized_prices)
    
    # 2. 技术指标特征
    if features_config.get('technical', True):
        close_prices = price_data[:, 3]  # 收盘价
        
        # 简单移动平均
        sma_5 = np.convolve(close_prices, np.ones(5)/5, mode='same')
        sma_20 = np.convolve(close_prices, np.ones(20)/20, mode='same')
        
        # RSI计算
        price_changes = np.diff(close_prices)
        gains = np.where(price_changes > 0, price_changes, 0)
        losses = np.where(price_changes < 0, -price_changes, 0)
        
        avg_gains = np.convolve(gains, np.ones(14)/14, mode='same')
        avg_losses = np.convolve(losses, np.ones(14)/14, mode='same')
        
        rs = avg_gains / (avg_losses + 1e-10)
        rsi = 100 - (100 / (1 + rs))
        rsi = np.concatenate([[50], rsi])
        
        # 组合技术指标
        technical_features = np.column_stack([sma_5, sma_20, rsi])
        
        # 标准化
        technical_features = (technical_features - technical_features.mean(axis=0)) / (technical_features.std(axis=0) + 1e-10)
        features.append(technical_features)
    
    # 合并所有特征
    return np.concatenate(features, axis=1)

# 在 services/technical_indicators.py 中的布林带实现

def bollinger_bands(self, data: pd.Series, period: int = 20, std_dev: float = 2):
    """布林带计算"""
    sma = data.rolling(window=period).mean()
    std = data.rolling(window=period).std()
    
    upper = sma + (std * std_dev)
    lower = sma - (std * std_dev)
    
    return {
        'upper': upper,
        'middle': sma,
        'lower': lower
    }
    '''
    
    print(code_example)

def main():
    """主演示函数"""
    print("🚀 技术指标深度学习应用演示")
    print("=" * 60)
    
    # 1. 生成示例价格数据
    np.random.seed(42)
    base_price = 2000
    price_changes = np.random.normal(0, 10, 150)
    prices = [base_price]
    
    for change in price_changes:
        new_price = prices[-1] + change
        prices.append(max(new_price, 1800))
    
    prices = np.array(prices[1:])
    print(f"📊 生成价格数据: {len(prices)} 个数据点")
    
    # 2. 计算布林带
    bb_data = calculate_bollinger_bands(prices)
    
    # 3. 计算其他技术指标
    other_indicators = calculate_other_indicators(prices)
    
    # 4. 准备模型特征
    feature_matrix, feature_names = prepare_model_features(prices, bb_data, other_indicators)
    
    # 5. 创建LSTM序列
    X, y = create_lstm_sequences(feature_matrix)
    
    # 6. 展示实际代码实现
    show_actual_code_implementation()
    
    print(f"\n🎉 演示完成！")
    print(f"📋 关键要点:")
    print(f"   • 布林带%B是最重要的技术指标特征")
    print(f"   • 所有特征都需要标准化处理")
    print(f"   • LSTM使用过去60个时间步预测下一步")
    print(f"   • 技术指标提供了价格之外的重要信息")

if __name__ == "__main__":
    main()
