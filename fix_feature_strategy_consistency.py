#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复特征选择策略一致性问题
统一所有页面的特征策略配置
"""

def create_unified_feature_strategies():
    """创建统一的特征策略配置"""
    print('🔧 创建统一的特征策略配置')
    print('=' * 60)
    
    # 统一的特征策略配置
    unified_strategies = {
        'minimal': {
            'value': 'minimal',
            'display_name': '最小特征集 (10个核心指标)',
            'description': '用于快速训练和测试的最小特征集',
            'feature_count': 10,
            'includes': ['基础价格指标', '核心技术指标']
        },
        'recommended': {
            'value': 'recommended',
            'display_name': '推荐特征集 (26个核心特征)',
            'description': '经过优化的核心技术指标组合',
            'feature_count': 26,
            'includes': ['布林带', 'ATR', '随机指标', 'RSI', 'MACD', '移动平均']
        },
        'enhanced': {
            'value': 'enhanced',
            'display_name': '增强特征集 (52个特征)',
            'description': '包含所有可用的技术指标和组合信号',
            'feature_count': 52,
            'includes': ['所有基础指标', '组合信号', '市场结构', '波动率指标']
        },
        'top_importance': {
            'value': 'top_importance',
            'display_name': '重要性前15个特征',
            'description': '基于特征重要性分析的前15个特征',
            'feature_count': 15,
            'includes': ['最重要的技术指标', '高预测能力特征']
        },
        'custom': {
            'value': 'custom',
            'display_name': '自定义特征选择',
            'description': '手动选择特定的技术指标组合',
            'feature_count': '可变',
            'includes': ['用户自定义选择']
        }
    }
    
    return unified_strategies

def generate_html_options(strategies):
    """生成HTML选项代码"""
    html_options = []
    for strategy_id, config in strategies.items():
        html_options.append(f'<option value="{config["value"]}">{config["display_name"]}</option>')
    return '\n                                        '.join(html_options)

def update_training_page():
    """更新模型训练页面"""
    print('\n🔧 更新模型训练页面')
    print('=' * 60)
    
    strategies = create_unified_feature_strategies()
    html_options = generate_html_options(strategies)
    
    # 读取当前文件
    try:
        with open('templates/model_training.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换特征选择策略选项
        import re
        
        # 查找特征选择策略的select元素
        pattern = r'(<select[^>]*id="featureSelectionStrategy"[^>]*>)(.*?)(</select>)'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            new_options = f'''
                                        {html_options}
                                    '''
            
            new_select = match.group(1) + new_options + match.group(3)
            content = content.replace(match.group(0), new_select)
            
            # 保存更新后的文件
            with open('templates/model_training.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 模型训练页面已更新")
            return True
        else:
            print("❌ 未找到特征选择策略选项")
            return False
            
    except Exception as e:
        print(f"❌ 更新模型训练页面失败: {e}")
        return False

def update_backtest_page():
    """更新模型回测页面"""
    print('\n🔧 更新模型回测页面')
    print('=' * 60)
    
    strategies = create_unified_feature_strategies()
    html_options = generate_html_options(strategies)
    
    try:
        with open('templates/model_backtest.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换特征选择策略选项
        import re
        
        pattern = r'(<select[^>]*id="featureSelectionStrategy"[^>]*>)(.*?)(</select>)'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            new_options = f'''
                                        {html_options}
                                    '''
            
            new_select = match.group(1) + new_options + match.group(3)
            content = content.replace(match.group(0), new_select)
            
            # 保存更新后的文件
            with open('templates/model_backtest.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 模型回测页面已更新")
            return True
        else:
            print("❌ 未找到特征选择策略选项")
            return False
            
    except Exception as e:
        print(f"❌ 更新模型回测页面失败: {e}")
        return False

def update_inference_page():
    """更新模型推理页面"""
    print('\n🔧 更新模型推理页面')
    print('=' * 60)
    
    strategies = create_unified_feature_strategies()
    
    try:
        with open('templates/model_inference.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找特征策略相关的JavaScript代码
        import re
        
        # 生成JavaScript策略映射
        js_mapping = "{\n"
        for strategy_id, config in strategies.items():
            js_mapping += f"        '{config['value']}': '{config['display_name']}',\n"
        js_mapping += "    }"
        
        # 查找并替换策略映射
        pattern = r'(const\s+strategyNames\s*=\s*){[^}]*}'
        if re.search(pattern, content):
            content = re.sub(pattern, f'\\1{js_mapping}', content)
            print("✅ 找到并更新了策略映射")
        else:
            print("⚠️ 未找到策略映射，需要手动添加")
        
        # 查找并更新HTML选项（如果存在）
        html_pattern = r'(<select[^>]*id="[^"]*[Ff]eature[^"]*"[^>]*>)(.*?)(</select>)'
        html_match = re.search(html_pattern, content, re.DOTALL)
        
        if html_match:
            html_options = generate_html_options(strategies)
            new_options = f'''
                                        {html_options}
                                    '''
            
            new_select = html_match.group(1) + new_options + html_match.group(3)
            content = content.replace(html_match.group(0), new_select)
            print("✅ 找到并更新了HTML选项")
        
        # 保存更新后的文件
        with open('templates/model_inference.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 模型推理页面已更新")
        return True
        
    except Exception as e:
        print(f"❌ 更新模型推理页面失败: {e}")
        return False

def create_feature_strategy_documentation():
    """创建特征策略文档"""
    print('\n📝 创建特征策略文档')
    print('=' * 60)
    
    strategies = create_unified_feature_strategies()
    
    doc_content = """# 特征选择策略统一配置

## 概述
为确保模型训练、回测和推理的一致性，所有页面都使用相同的特征选择策略配置。

## 统一策略配置

"""
    
    for strategy_id, config in strategies.items():
        doc_content += f"""### {config['display_name']}
- **值**: `{config['value']}`
- **特征数量**: {config['feature_count']}
- **描述**: {config['description']}
- **包含**: {', '.join(config['includes'])}

"""
    
    doc_content += """## 使用说明

### 模型训练
在模型训练页面选择特征策略时，系统会根据选择的策略生成对应数量和类型的特征。

### 模型回测
回测时必须选择与训练时相同的特征策略，否则会导致特征维度不匹配。

### 模型推理
推理时也必须使用与训练时相同的特征策略，确保输入数据的一致性。

## 技术实现

### HTML选项
```html
<select class="form-select" id="featureSelectionStrategy">
"""
    
    for strategy_id, config in strategies.items():
        doc_content += f'    <option value="{config["value"]}">{config["display_name"]}</option>\n'
    
    doc_content += """</select>
```

### JavaScript映射
```javascript
const strategyNames = {
"""
    
    for strategy_id, config in strategies.items():
        doc_content += f"    '{config['value']}': '{config['display_name']}',\n"
    
    doc_content += """};
```

## 注意事项

1. **一致性要求**: 训练、回测、推理必须使用相同的特征策略
2. **特征数量**: 不同策略的特征数量不同，模型输入维度会相应变化
3. **兼容性**: 旧模型可能使用旧的策略配置，需要注意兼容性
4. **性能影响**: 特征数量越多，训练时间越长，但可能提高预测精度

## 更新历史

- 2025-08-03: 统一所有页面的特征策略配置
- 修复了不同页面策略不一致的问题
- 确保模型训练和推理的特征匹配
"""
    
    # 保存文档
    with open('特征策略统一配置.md', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print("✅ 特征策略文档已创建: 特征策略统一配置.md")

def verify_consistency():
    """验证一致性修复效果"""
    print('\n🔍 验证一致性修复效果')
    print('=' * 60)
    
    # 重新运行一致性分析
    import subprocess
    try:
        result = subprocess.run(['python', 'analyze_feature_consistency.py'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 一致性分析完成")
            # 检查输出中是否还有不一致问题
            if "⚠️" in result.stdout:
                print("⚠️ 仍存在一些不一致问题，请检查输出")
            else:
                print("🎉 所有一致性问题已解决")
        else:
            print(f"❌ 一致性分析失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")

def main():
    """主函数"""
    print('🔧 修复特征选择策略一致性问题')
    print('=' * 80)
    
    print("🎯 问题确认:")
    print("   您的观察完全正确！不同页面的特征策略配置确实不一致")
    print("   这会导致模型训练和推理时的特征维度不匹配")
    
    # 创建统一配置
    strategies = create_unified_feature_strategies()
    
    print(f"\n📋 统一策略配置:")
    for strategy_id, config in strategies.items():
        print(f"   {config['display_name']} ({config['feature_count']}个特征)")
    
    # 更新各个页面
    training_success = update_training_page()
    backtest_success = update_backtest_page()
    inference_success = update_inference_page()
    
    # 创建文档
    create_feature_strategy_documentation()
    
    # 验证修复效果
    verify_consistency()
    
    print(f"\n🎯 修复结果:")
    print(f"   模型训练页面: {'✅ 成功' if training_success else '❌ 失败'}")
    print(f"   模型回测页面: {'✅ 成功' if backtest_success else '❌ 失败'}")
    print(f"   模型推理页面: {'✅ 成功' if inference_success else '❌ 失败'}")
    
    if all([training_success, backtest_success, inference_success]):
        print(f"\n🎉 特征策略一致性问题修复完成！")
        print(f"💡 关键改进:")
        print(f"   - 统一了所有页面的特征策略选项")
        print(f"   - 确保了策略值和描述的一致性")
        print(f"   - 添加了详细的特征数量说明")
        print(f"   - 创建了统一配置文档")
        
        print(f"\n🚀 现在的优势:")
        print(f"   - 模型训练和推理特征完全匹配")
        print(f"   - 避免了维度不匹配错误")
        print(f"   - 提供了清晰的特征策略说明")
        print(f"   - 支持从最小到完整的特征集")
    else:
        print(f"\n⚠️ 部分修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
