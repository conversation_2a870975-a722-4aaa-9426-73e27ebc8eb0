#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试优化后的特征处理
"""

import requests
import time
import json

def test_basic_features():
    """测试基础特征"""
    print('🧪 测试基础特征处理')
    
    # 登录
    session = requests.Session()
    login_response = session.post('http://127.0.0.1:5000/login', data={'username': 'admin', 'password': 'admin123'})

    if login_response.status_code == 200:
        print('✅ 登录成功')
        
        # 测试仅基础特征
        config = {
            'model_name': f'基础特征测试_{int(time.time())}',
            'model_type': 'lstm',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {'mode': 'days', 'training_days': 30},
            'sequence_length': 20,
            'hidden_size': 64,
            'num_layers': 2,
            'dropout': 0.2,
            'batch_size': 16,
            'learning_rate': 0.001,
            'epochs': 5,
            'patience': 3,
            'early_stopping': True,
            'min_epochs': 2,
            'use_gpu': True,
            'save_checkpoints': True,
            'use_enhanced_features': False,
            'features': {'price': True, 'volume': True, 'technical': True, 'time': True}
        }
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                               json=config, headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f'✅ 基础特征测试启动成功: {task_id}')
                
                # 等待数据准备
                print('⏳ 等待数据准备完成...')
                time.sleep(15)
                
                # 检查结果
                progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                if progress_response.status_code == 200:
                    progress_data = progress_response.json()
                    if progress_data.get('success'):
                        progress = progress_data.get('progress', {})
                        logs = progress.get('logs')
                        if logs:
                            try:
                                log_data = json.loads(logs) if isinstance(logs, str) else logs
                                data_info = log_data.get('data_info', {})
                                if 'X_train_shape' in data_info:
                                    shape = data_info['X_train_shape']
                                    feature_count = shape[2]
                                    print(f'📊 基础特征测试结果:')
                                    print(f'   数据形状: {shape}')
                                    print(f'   特征数量: {feature_count}')
                                    
                                    if feature_count == 8:
                                        print('✅ 基础特征数量正确（8个）')
                                        return True
                                    else:
                                        print(f'❌ 基础特征数量不正确，预期8个，实际{feature_count}个')
                                        return False
                                else:
                                    print('⏳ 数据准备尚未完成')
                                    return False
                            except Exception as e:
                                print(f'❌ 日志解析失败: {e}')
                                return False
                        else:
                            print('⚠️ 暂无日志信息')
                            return False
                    else:
                        print(f'❌ 获取进度失败: {progress_data.get("error")}')
                        return False
                else:
                    print(f'❌ 进度请求失败: {progress_response.status_code}')
                    return False
            else:
                print(f'❌ 启动失败: {result.get("error")}')
                return False
        else:
            print(f'❌ 请求失败: {response.status_code}')
            return False
    else:
        print('❌ 登录失败')
        return False

def main():
    """主函数"""
    print('🔧 快速测试优化后的特征处理')
    print('=' * 50)
    
    success = test_basic_features()
    
    if success:
        print('\n🎉 基础特征测试成功！')
    else:
        print('\n❌ 基础特征测试失败')

if __name__ == "__main__":
    main()
