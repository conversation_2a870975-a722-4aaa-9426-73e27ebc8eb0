#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复模型训练状态不一致问题的工具
"""

import sqlite3
import json
import os
from datetime import datetime

def fix_training_status():
    """修复训练状态问题"""
    print("🔧 模型训练状态修复工具")
    print("=" * 50)
    
    if not os.path.exists('trading_system.db'):
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 检查是否有需要修复的任务
        print("🔍 检查需要修复的任务...")
        
        # 查找状态为running但日志显示model_training的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, logs, created_at, updated_at
            FROM training_tasks
            WHERE status = 'running'
            ORDER BY created_at DESC
        ''')
        
        running_tasks = cursor.fetchall()
        
        if not running_tasks:
            print("✅ 没有找到需要修复的running状态任务")
            conn.close()
            return
        
        print(f"📊 找到 {len(running_tasks)} 个running状态的任务")
        
        fixed_count = 0
        
        for task in running_tasks:
            task_id, model_id, status, progress, logs, created_at, updated_at = task
            
            print(f"\n📋 检查任务: {task_id[:8]}...")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            
            # 分析日志
            if logs:
                try:
                    log_data = json.loads(logs)
                    stage = log_data.get('stage')
                    message = log_data.get('message', '')
                    
                    print(f"   日志阶段: {stage}")
                    print(f"   日志消息: {message}")
                    
                    # 如果是model_training阶段，这是正常的，不需要修复
                    if stage == 'model_training':
                        print("   ✅ 模型训练阶段，状态正常")
                        continue
                    
                    # 如果是data_ready阶段但状态是running，需要修复
                    elif stage == 'data_ready':
                        print("   🔧 发现状态不一致：日志显示data_ready但状态为running")
                        
                        # 修复状态为data_ready
                        cursor.execute('''
                            UPDATE training_tasks
                            SET status = 'data_ready',
                                updated_at = ?
                            WHERE id = ?
                        ''', (datetime.now().isoformat(), task_id))
                        
                        # 同时更新模型状态
                        cursor.execute('''
                            UPDATE deep_learning_models
                            SET status = 'data_ready'
                            WHERE id = ?
                        ''', (model_id,))
                        
                        print("   ✅ 已修复状态为data_ready")
                        fixed_count += 1
                    
                    # 如果是data_preparation阶段但进度很高，可能需要修复
                    elif stage == 'data_preparation' and progress >= 90:
                        print(f"   🔧 数据准备进度{progress}%，可能已完成但状态未更新")
                        
                        # 修复状态为data_ready
                        cursor.execute('''
                            UPDATE training_tasks
                            SET status = 'data_ready',
                                progress = 100,
                                logs = ?,
                                updated_at = ?
                            WHERE id = ?
                        ''', (
                            json.dumps({
                                'stage': 'data_ready',
                                'message': '数据准备完成，可以开始模型训练（自动修复）'
                            }),
                            datetime.now().isoformat(),
                            task_id
                        ))
                        
                        # 同时更新模型状态
                        cursor.execute('''
                            UPDATE deep_learning_models
                            SET status = 'data_ready'
                            WHERE id = ?
                        ''', (model_id,))
                        
                        print("   ✅ 已修复状态为data_ready")
                        fixed_count += 1
                    
                    else:
                        print(f"   ℹ️ 阶段{stage}，无需修复")
                        
                except json.JSONDecodeError:
                    print("   ⚠️ 日志解析失败")
                    
                    # 如果进度很高，可能数据准备已完成
                    if progress >= 90:
                        print(f"   🔧 进度{progress}%，尝试修复为data_ready")
                        
                        cursor.execute('''
                            UPDATE training_tasks
                            SET status = 'data_ready',
                                progress = 100,
                                logs = ?,
                                updated_at = ?
                            WHERE id = ?
                        ''', (
                            json.dumps({
                                'stage': 'data_ready',
                                'message': '数据准备完成，可以开始模型训练（自动修复）'
                            }),
                            datetime.now().isoformat(),
                            task_id
                        ))
                        
                        cursor.execute('''
                            UPDATE deep_learning_models
                            SET status = 'data_ready'
                            WHERE id = ?
                        ''', (model_id,))
                        
                        print("   ✅ 已修复状态为data_ready")
                        fixed_count += 1
            else:
                print("   ⚠️ 无日志信息")
                
                # 如果进度很高，可能数据准备已完成
                if progress >= 90:
                    print(f"   🔧 进度{progress}%，尝试修复为data_ready")
                    
                    cursor.execute('''
                        UPDATE training_tasks
                        SET status = 'data_ready',
                            progress = 100,
                            logs = ?,
                            updated_at = ?
                        WHERE id = ?
                    ''', (
                        json.dumps({
                            'stage': 'data_ready',
                            'message': '数据准备完成，可以开始模型训练（自动修复）'
                        }),
                        datetime.now().isoformat(),
                        task_id
                    ))
                    
                    cursor.execute('''
                        UPDATE deep_learning_models
                        SET status = 'data_ready'
                        WHERE id = ?
                    ''', (model_id,))
                    
                    print("   ✅ 已修复状态为data_ready")
                    fixed_count += 1
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n🎉 修复完成！")
        print(f"   检查任务数: {len(running_tasks)}")
        print(f"   修复任务数: {fixed_count}")
        
        if fixed_count > 0:
            print("\n✅ 建议刷新前端页面以查看最新状态")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

def show_current_status():
    """显示当前所有任务状态"""
    print("\n📊 当前任务状态概览")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT status, COUNT(*) as count
            FROM training_tasks
            GROUP BY status
            ORDER BY count DESC
        ''')
        
        status_counts = cursor.fetchall()
        
        print("状态分布:")
        for status, count in status_counts:
            print(f"  {status}: {count} 个任务")
        
        # 显示最近的任务
        print("\n最近的5个任务:")
        cursor.execute('''
            SELECT id, status, progress, created_at
            FROM training_tasks
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        recent_tasks = cursor.fetchall()
        for task in recent_tasks:
            task_id, status, progress, created_at = task
            print(f"  {task_id[:8]}... | {status} | {progress}% | {created_at}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")

def main():
    """主函数"""
    show_current_status()
    
    print("\n" + "=" * 60)
    response = input("是否要执行状态修复？(y/n): ").lower().strip()
    
    if response in ['y', 'yes', '是']:
        fix_training_status()
    else:
        print("取消修复操作")

if __name__ == "__main__":
    main()
