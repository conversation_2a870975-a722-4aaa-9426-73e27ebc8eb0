#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI推理回测显示修复
"""

import requests
import time
import json

def test_backtest_display_fix():
    """测试回测显示修复"""
    print("🧪 测试AI推理回测显示修复")
    print("=" * 50)
    
    # 等待应用启动
    time.sleep(3)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 获取AI推理回测页面
        print(f"\n🔍 获取AI推理回测页面...")
        backtest_response = session.get('http://127.0.0.1:5000/deep-learning/backtest')
        
        if backtest_response.status_code != 200:
            print(f"❌ 页面访问失败: {backtest_response.status_code}")
            return False
        
        content = backtest_response.text
        print(f"✅ 页面访问成功，大小: {len(content):,} 字符")
        
        # 检查修复内容
        print(f"\n🔍 检查修复内容:")
        
        # 1. 检查格式化函数
        if 'function formatDateTime(' in content:
            print("✅ formatDateTime函数已添加")
        else:
            print("❌ formatDateTime函数未添加")
        
        # 2. 检查交易类型格式化
        if 'function formatTradeType(' in content:
            print("✅ formatTradeType函数已添加")
        else:
            print("❌ formatTradeType函数未添加")
        
        # 3. 检查价格格式化
        if 'function formatPrice(' in content:
            print("✅ formatPrice函数已添加")
        else:
            print("❌ formatPrice函数未添加")
        
        # 4. 检查盈亏格式化
        if 'function formatProfit(' in content:
            print("✅ formatProfit函数已添加")
        else:
            print("❌ formatProfit函数未添加")
        
        # 5. 检查改进的displayTradeDetails函数
        if 'formatDateTime(trade.open_time' in content:
            print("✅ displayTradeDetails函数已改进")
        else:
            print("❌ displayTradeDetails函数未改进")
        
        # 检查JavaScript文件
        print(f"\n🔍 检查JavaScript文件:")
        try:
            js_response = session.get('http://127.0.0.1:5000/static/js/backtest_functions.js')
            if js_response.status_code == 200:
                js_content = js_response.text
                print(f"✅ backtest_functions.js访问成功，大小: {len(js_content):,} 字符")
                
                # 检查修复函数
                fixes = [
                    ('formatDateTime', 'formatDateTime函数'),
                    ('formatTradeType', 'formatTradeType函数'),
                    ('formatPrice', 'formatPrice函数'),
                    ('formatProfit', 'formatProfit函数'),
                    ('timeZone: \'Asia/Shanghai\'', '北京时间格式化'),
                    ('Invalid Date', '无效日期处理'),
                    ('Format Error', '格式错误处理')
                ]
                
                for check, name in fixes:
                    if check in js_content:
                        print(f"   ✅ {name}: 已修复")
                    else:
                        print(f"   ❌ {name}: 未修复")
            else:
                print(f"❌ backtest_functions.js访问失败: {js_response.status_code}")
        except Exception as e:
            print(f"❌ 检查JavaScript文件失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def create_browser_test_script():
    """创建浏览器测试脚本"""
    print(f"\n🔧 创建浏览器测试脚本...")
    
    test_script = '''
// AI推理回测显示修复测试脚本
console.log("🧪 开始测试AI推理回测显示修复...");

// 1. 检查修复函数是否存在
console.log("📋 检查修复函数:");
const functions = ['formatDateTime', 'formatTradeType', 'formatPrice', 'formatProfit'];
functions.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName}: 函数存在`);
    } else {
        console.log(`❌ ${funcName}: 函数不存在`);
    }
});

// 2. 测试日期格式化函数
console.log("📋 测试日期格式化:");
if (typeof formatDateTime === 'function') {
    const testDates = [
        '2024-01-15T10:30:00Z',
        '2024-01-15 10:30:00',
        1705312200000,  // 毫秒时间戳
        1705312200,     // 秒时间戳
        null,
        undefined,
        'invalid-date'
    ];
    
    testDates.forEach(date => {
        const result = formatDateTime(date);
        console.log(`   输入: ${date} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatDateTime函数不存在");
}

// 3. 测试交易类型格式化
console.log("📋 测试交易类型格式化:");
if (typeof formatTradeType === 'function') {
    const testTypes = ['BUY', 'SELL', 'buy', 'sell', 0, 1, '0', '1', null, undefined];
    
    testTypes.forEach(type => {
        const result = formatTradeType(type);
        console.log(`   输入: ${type} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatTradeType函数不存在");
}

// 4. 测试价格格式化
console.log("📋 测试价格格式化:");
if (typeof formatPrice === 'function') {
    const testPrices = [1.23456, 0, null, undefined, 'invalid', 1000.123456];
    
    testPrices.forEach(price => {
        const result = formatPrice(price);
        console.log(`   输入: ${price} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatPrice函数不存在");
}

// 5. 测试盈亏格式化
console.log("📋 测试盈亏格式化:");
if (typeof formatProfit === 'function') {
    const testProfits = [100.50, -50.25, 0, null, undefined, 'invalid'];
    
    testProfits.forEach(profit => {
        const result = formatProfit(profit);
        console.log(`   输入: ${profit} -> 输出: ${result}`);
    });
} else {
    console.log("❌ formatProfit函数不存在");
}

// 6. 创建模拟交易数据并测试显示
console.log("📋 测试交易详情显示:");
if (typeof displayTradeDetails === 'function') {
    const mockTrades = [
        {
            open_time: '2024-01-15T10:30:00Z',
            type: 'BUY',
            lot_size: 0.1,
            open_price: 1.23456,
            close_price: 1.23556,
            stop_loss: 1.23356,
            take_profit: 1.23656,
            profit: 10.50,
            confidence: 0.85
        },
        {
            open_time: 1705312200000,
            type: 'SELL',
            volume: 0.2,
            entry_price: 1.23456,
            exit_price: 1.23356,
            sl: 1.23556,
            tp: 1.23256,
            profit: -20.25,
            confidence: 0.75
        },
        {
            time: null,
            type: null,
            lot_size: null,
            open_price: null,
            close_price: null,
            stop_loss: null,
            take_profit: null,
            profit: null,
            confidence: null
        }
    ];
    
    console.log("🔄 显示模拟交易数据...");
    displayTradeDetails(mockTrades);
    console.log("✅ 交易详情显示完成");
} else {
    console.log("❌ displayTradeDetails函数不存在");
}

// 7. 检查页面元素
console.log("📋 检查页面元素:");
const elements = ['backtestResults', 'backtestStats', 'backtestCard'];
elements.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ ${id}: 元素存在`);
    } else {
        console.log(`❌ ${id}: 元素不存在`);
    }
});

console.log("🎉 AI推理回测显示修复测试完成！");
console.log("💡 如果看到'Invalid Date'或'undefined'错误，说明修复未完全生效");
console.log("💡 请刷新页面并重新运行测试");
'''
    
    with open('browser_backtest_test.js', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 浏览器测试脚本已创建: browser_backtest_test.js")
    print("📋 使用方法:")
    print("   1. 打开AI推理回测页面: http://127.0.0.1:5000/deep-learning/backtest")
    print("   2. 按F12打开开发者工具")
    print("   3. 在Console标签中粘贴browser_backtest_test.js的内容并执行")

if __name__ == "__main__":
    success = test_backtest_display_fix()
    create_browser_test_script()
    
    if success:
        print("\n🎉 AI推理回测显示修复测试完成！")
        print("📋 修复总结:")
        print("   1. ✅ 添加了formatDateTime函数处理日期显示")
        print("   2. ✅ 添加了formatTradeType函数处理交易类型")
        print("   3. ✅ 添加了formatPrice函数处理价格显示")
        print("   4. ✅ 添加了formatProfit函数处理盈亏显示")
        print("   5. ✅ 改进了displayTradeDetails函数")
        print("   6. ✅ 添加了错误处理和数据验证")
        print("\n🔄 请访问回测页面并使用测试脚本验证修复效果")
    else:
        print("\n❌ AI推理回测显示修复测试失败")
        print("🔧 请检查上述错误信息")
