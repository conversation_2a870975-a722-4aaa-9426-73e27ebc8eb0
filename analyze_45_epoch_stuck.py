#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析第45轮训练卡住问题
检查训练完成但未正确结束的原因
"""

import sqlite3
import json
from datetime import datetime
import time

def analyze_45_epoch_issue():
    """分析第45轮训练卡住问题"""
    print('🔍 分析第45轮训练卡住问题')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询当前running状态的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs, train_loss, val_loss
            FROM training_tasks 
            WHERE status = 'running' AND current_epoch >= 45
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("❌ 没有找到第45轮的running任务")
            return None
        
        task = tasks[0]  # 取最新的任务
        task_id, model_id, status, progress, current_epoch, total_epochs, created_at, updated_at, logs, train_loss, val_loss = task
        
        print(f"📊 卡住任务详情:")
        print(f"   任务ID: {task_id}")
        print(f"   模型ID: {model_id}")
        print(f"   状态: {status}")
        print(f"   进度: {progress}%")
        print(f"   当前轮次: {current_epoch}/{total_epochs}")
        print(f"   训练损失: {train_loss}")
        print(f"   验证损失: {val_loss}")
        print(f"   创建时间: {created_at}")
        print(f"   最后更新: {updated_at}")
        
        # 分析问题
        print(f"\n🔍 问题分析:")
        
        if current_epoch >= total_epochs:
            print(f"   ⚠️ 训练已达到最大轮次 ({current_epoch}/{total_epochs})")
            print(f"   🎯 问题：训练完成但未正确结束")
        
        if val_loss == 0.0:
            print(f"   ⚠️ 验证损失为0.0，可能验证阶段有问题")
        
        # 计算卡住时间
        if updated_at:
            try:
                last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                now = datetime.now()
                stuck_time = now - last_update.replace(tzinfo=None)
                print(f"   距离上次更新: {stuck_time}")
                
                if stuck_time.total_seconds() > 300:  # 5分钟
                    print(f"   ⚠️ 任务确实卡住了 (超过5分钟无更新)")
                else:
                    print(f"   ✅ 任务可能还在处理中")
            except Exception as e:
                print(f"   ❌ 时间解析失败: {e}")
        
        # 解析日志
        if logs:
            try:
                if isinstance(logs, str):
                    log_data = json.loads(logs)
                else:
                    log_data = logs
                
                print(f"\n📋 最新日志信息:")
                print(f"   阶段: {log_data.get('stage', 'unknown')}")
                print(f"   消息: {log_data.get('message', 'N/A')}")
                
                # 检查训练相关信息
                for key in ['epoch', 'batch', 'train_loss', 'val_loss', 'train_acc', 'val_acc']:
                    if key in log_data:
                        print(f"   {key}: {log_data.get(key, 'N/A')}")
                        
            except Exception as e:
                print(f"   ❌ 日志解析失败: {e}")
        
        return {
            'task_id': task_id,
            'current_epoch': current_epoch,
            'total_epochs': total_epochs,
            'progress': progress,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'stuck_time': stuck_time.total_seconds() if 'stuck_time' in locals() else 0
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def identify_45_epoch_causes():
    """识别第45轮卡住的可能原因"""
    print('\n🔍 识别第45轮卡住的可能原因')
    print('=' * 60)
    
    possible_causes = [
        {
            'cause': '训练完成但未正确结束',
            'description': '达到最大轮次(45/45)但训练循环未正确退出',
            'probability': 'high',
            'indicators': ['current_epoch >= total_epochs', 'status仍为running'],
            'solution': '强制结束训练并标记为完成'
        },
        {
            'cause': '验证阶段卡住',
            'description': '验证损失为0.0，可能验证数据加载或计算有问题',
            'probability': 'high',
            'indicators': ['val_loss = 0.0', '训练损失正常'],
            'solution': '跳过验证或修复验证逻辑'
        },
        {
            'cause': '模型保存阶段卡住',
            'description': '训练完成后在保存模型时卡住',
            'probability': 'medium',
            'indicators': ['最后一轮完成', '长时间无更新'],
            'solution': '检查磁盘空间和文件权限'
        },
        {
            'cause': '早停机制异常',
            'description': '早停逻辑可能有问题，无法正确判断训练结束',
            'probability': 'medium',
            'indicators': ['达到最大轮次', '早停未触发'],
            'solution': '检查早停逻辑或禁用早停'
        },
        {
            'cause': '批次处理未完成',
            'description': '最后一轮的批次处理(426/585)未完成',
            'probability': 'medium',
            'indicators': ['批次进度未达到100%', '数据加载卡住'],
            'solution': '检查数据加载器和GPU内存'
        },
        {
            'cause': '状态更新机制问题',
            'description': '训练实际已完成但状态未更新',
            'probability': 'low',
            'indicators': ['进度停止更新', '日志无变化'],
            'solution': '手动更新任务状态'
        }
    ]
    
    print("🚨 可能的原因分析:")
    for i, cause in enumerate(possible_causes, 1):
        prob_icon = '🔴' if cause['probability'] == 'high' else '🟡' if cause['probability'] == 'medium' else '🟢'
        print(f"\n{i}. {prob_icon} {cause['cause']} ({cause['probability'].upper()})")
        print(f"   描述: {cause['description']}")
        print(f"   指标: {', '.join(cause['indicators'])}")
        print(f"   解决方案: {cause['solution']}")
    
    return possible_causes

def provide_immediate_solutions():
    """提供立即解决方案"""
    print('\n🔧 立即解决方案')
    print('=' * 60)
    
    solutions = [
        {
            'priority': 'high',
            'action': '强制完成训练',
            'description': '将任务状态更新为completed',
            'steps': [
                '1. 停止当前训练进程',
                '2. 更新数据库状态为completed',
                '3. 设置完成时间',
                '4. 保存当前模型状态'
            ],
            'command': 'python force_complete_training.py'
        },
        {
            'priority': 'high',
            'action': '检查模型保存',
            'description': '验证模型是否已正确保存',
            'steps': [
                '1. 检查models目录下的文件',
                '2. 验证模型文件完整性',
                '3. 测试模型加载',
                '4. 确认训练结果'
            ],
            'command': 'python check_model_files.py'
        },
        {
            'priority': 'medium',
            'action': '清理GPU内存',
            'description': '释放可能占用的GPU资源',
            'steps': [
                '1. 检查GPU内存使用',
                '2. 清理PyTorch缓存',
                '3. 重启训练服务',
                '4. 验证GPU状态'
            ],
            'command': 'nvidia-smi --gpu-reset'
        }
    ]
    
    print("🎯 推荐解决方案:")
    for solution in solutions:
        priority_icon = '🔴' if solution['priority'] == 'high' else '🟡'
        print(f"\n{priority_icon} {solution['action']} ({solution['priority'].upper()})")
        print(f"   描述: {solution['description']}")
        print(f"   步骤:")
        for step in solution['steps']:
            print(f"     {step}")
        print(f"   命令: {solution['command']}")

def create_force_complete_script():
    """创建强制完成训练的脚本"""
    print('\n🔧 创建强制完成训练脚本')
    print('=' * 60)
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制完成第45轮训练
将卡住的训练标记为完成
"""

import sqlite3
from datetime import datetime

def force_complete_training():
    """强制完成训练"""
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找卡住的任务
        cursor.execute("""
            SELECT id FROM training_tasks 
            WHERE status = 'running' AND current_epoch >= total_epochs
            ORDER BY updated_at DESC LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            task_id = result[0]
            
            # 更新任务状态为completed
            cursor.execute("""
                UPDATE training_tasks 
                SET status = 'completed',
                    completed_at = ?,
                    updated_at = ?,
                    progress = 100.0
                WHERE id = ?
            """, (datetime.now().isoformat(), datetime.now().isoformat(), task_id))
            
            conn.commit()
            print(f"✅ 任务 {task_id} 已强制标记为完成")
        else:
            print("❌ 没有找到需要完成的任务")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 强制完成失败: {e}")

if __name__ == "__main__":
    force_complete_training()
'''
    
    # 保存脚本
    with open('force_complete_training.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("📝 已创建 force_complete_training.py 脚本")
    print("💡 使用方法: python force_complete_training.py")

def analyze_training_completion():
    """分析训练完成情况"""
    print('\n🔍 分析训练完成情况')
    print('=' * 60)
    
    completion_analysis = {
        'expected_behavior': [
            '第45轮训练完成',
            '验证阶段完成',
            '模型保存完成',
            '状态更新为completed',
            '设置完成时间',
            '清理训练资源'
        ],
        'current_status': [
            '✅ 第45轮训练已完成 (45/45)',
            '❌ 验证损失异常 (0.0)',
            '❓ 模型保存状态未知',
            '❌ 状态仍为running',
            '❌ 未设置完成时间',
            '❓ 资源清理状态未知'
        ],
        'missing_steps': [
            '验证阶段正确完成',
            '状态更新为completed',
            '设置完成时间戳',
            '清理训练资源'
        ]
    }
    
    print("📋 预期行为:")
    for behavior in completion_analysis['expected_behavior']:
        print(f"   - {behavior}")
    
    print(f"\n📊 当前状态:")
    for status in completion_analysis['current_status']:
        print(f"   {status}")
    
    print(f"\n🔧 缺失步骤:")
    for step in completion_analysis['missing_steps']:
        print(f"   ❌ {step}")

def main():
    """主函数"""
    print('🔧 第45轮训练卡住问题分析')
    print('=' * 80)
    
    # 分析第45轮问题
    task_info = analyze_45_epoch_issue()
    
    # 识别可能原因
    causes = identify_45_epoch_causes()
    
    # 分析训练完成情况
    analyze_training_completion()
    
    # 提供解决方案
    provide_immediate_solutions()
    
    # 创建强制完成脚本
    create_force_complete_script()
    
    print(f"\n✅ 分析完成！")
    
    if task_info:
        print(f"💡 关键发现:")
        print(f"   - 训练已达到最大轮次 ({task_info['current_epoch']}/{task_info['total_epochs']})")
        print(f"   - 验证损失异常 ({task_info['val_loss']})")
        print(f"   - 需要手动完成训练")
        
        print(f"\n🎯 推荐行动:")
        print(f"   1. 运行: python force_complete_training.py")
        print(f"   2. 检查模型文件是否已保存")
        print(f"   3. 验证训练结果")
    else:
        print(f"💡 没有发现第45轮卡住的任务")

if __name__ == "__main__":
    main()
