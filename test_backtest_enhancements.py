#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI推理回测增强功能
"""

import requests
import time

def test_backtest_enhancements():
    """测试回测增强功能"""
    print("🧪 测试AI推理回测增强功能")
    print("=" * 50)
    
    # 等待应用启动
    time.sleep(3)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 1. 测试AI推理回测页面访问
        print(f"\n🔍 测试AI推理回测页面...")
        backtest_response = session.get('http://127.0.0.1:5000/deep-learning/backtest')
        
        if backtest_response.status_code != 200:
            print(f"❌ 页面访问失败: {backtest_response.status_code}")
            return False
        
        content = backtest_response.text
        print(f"✅ 页面访问成功，大小: {len(content):,} 字符")
        
        # 2. 检查增强特征配置
        print(f"\n🔍 检查增强特征配置:")
        enhancements = [
            ('增强特征配置', 'id="enhancedFeaturesCard"'),
            ('使用增强特征开关', 'id="useEnhancedFeatures"'),
            ('特征选择策略', 'id="featureSelectionStrategy"'),
            ('特征重要性分析', 'id="analyzeFeatureImportance"'),
            ('自定义特征配置', 'id="customFeaturesConfig"'),
            ('布林带特征', 'feature_bb_percent_b'),
            ('ATR特征', 'feature_atr_atr'),
            ('随机指标特征', 'feature_stoch_stoch_k'),
            ('组合信号', 'feature_combined_squeeze_low_vol')
        ]
        
        for name, check in enhancements:
            if check in content:
                print(f"   ✅ {name}: 已添加")
            else:
                print(f"   ❌ {name}: 未添加")
        
        # 3. 检查MT5自动重连功能
        print(f"\n🔍 检查MT5自动重连功能:")
        mt5_features = [
            ('MT5连接状态显示', 'id="mt5ConnectionStatus"'),
            ('重新连接按钮', 'id="reconnectMT5Btn"'),
            ('检查MT5连接函数', 'function checkMT5Connection'),
            ('自动重连函数', 'function autoReconnectMT5'),
            ('手动重连函数', 'function reconnectMT5'),
            ('连接监控', 'startMT5ConnectionMonitoring'),
            ('回测前连接检查', '回测前检查MT5连接状态')
        ]
        
        for name, check in mt5_features:
            if check in content:
                print(f"   ✅ {name}: 已添加")
            else:
                print(f"   ❌ {name}: 未添加")
        
        # 4. 检查JavaScript函数
        print(f"\n🔍 检查JavaScript函数:")
        js_functions = [
            'toggleEnhancedFeaturesConfig',
            'toggleCustomFeaturesConfig', 
            'getEnhancedFeaturesConfig',
            'checkMT5Connection',
            'autoReconnectMT5',
            'reconnectMT5',
            'startMT5ConnectionMonitoring'
        ]
        
        for func in js_functions:
            if f'function {func}(' in content:
                print(f"   ✅ {func}: 函数存在")
            else:
                print(f"   ❌ {func}: 函数缺失")
        
        # 5. 测试MT5连接状态API
        print(f"\n🔍 测试MT5连接状态API:")
        try:
            mt5_response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
            if mt5_response.status_code == 200:
                mt5_data = mt5_response.json()
                print(f"   ✅ MT5连接状态API正常")
                print(f"   连接状态: {mt5_data.get('connected', False)}")
                print(f"   成功标志: {mt5_data.get('success', False)}")
            else:
                print(f"   ❌ MT5连接状态API失败: {mt5_response.status_code}")
        except Exception as e:
            print(f"   ❌ MT5连接状态API异常: {e}")
        
        # 6. 测试MT5自动连接API
        print(f"\n🔍 测试MT5自动连接API:")
        try:
            auto_connect_response = session.post('http://127.0.0.1:5000/api/mt5/auto-connect')
            if auto_connect_response.status_code == 200:
                auto_connect_data = auto_connect_response.json()
                print(f"   ✅ MT5自动连接API正常")
                print(f"   连接结果: {auto_connect_data.get('success', False)}")
            else:
                print(f"   ❌ MT5自动连接API失败: {auto_connect_response.status_code}")
        except Exception as e:
            print(f"   ❌ MT5自动连接API异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def create_browser_test_script():
    """创建浏览器测试脚本"""
    print(f"\n🔧 创建浏览器测试脚本...")
    
    test_script = '''
// AI推理回测增强功能测试脚本
console.log("🧪 开始测试AI推理回测增强功能...");

// 1. 测试增强特征配置功能
console.log("📋 测试增强特征配置:");
const enhancedFeaturesTests = [
    'useEnhancedFeatures',
    'featureSelectionStrategy', 
    'analyzeFeatureImportance',
    'customFeaturesConfig'
];

enhancedFeaturesTests.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ ${id}: 元素存在`);
    } else {
        console.log(`❌ ${id}: 元素不存在`);
    }
});

// 2. 测试增强特征配置函数
console.log("📋 测试增强特征配置函数:");
const enhancedFunctionTests = [
    'toggleEnhancedFeaturesConfig',
    'toggleCustomFeaturesConfig',
    'getEnhancedFeaturesConfig'
];

enhancedFunctionTests.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName}: 函数存在`);
    } else {
        console.log(`❌ ${funcName}: 函数不存在`);
    }
});

// 3. 测试MT5连接功能
console.log("📋 测试MT5连接功能:");
const mt5Tests = [
    'mt5ConnectionStatus',
    'reconnectMT5Btn'
];

mt5Tests.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        console.log(`✅ ${id}: 元素存在`);
    } else {
        console.log(`❌ ${id}: 元素不存在`);
    }
});

// 4. 测试MT5连接函数
console.log("📋 测试MT5连接函数:");
const mt5FunctionTests = [
    'checkMT5Connection',
    'autoReconnectMT5',
    'reconnectMT5',
    'startMT5ConnectionMonitoring'
];

mt5FunctionTests.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ ${funcName}: 函数存在`);
    } else {
        console.log(`❌ ${funcName}: 函数不存在`);
    }
});

// 5. 测试增强特征配置切换
console.log("📋 测试增强特征配置切换:");
if (typeof toggleEnhancedFeaturesConfig === 'function') {
    console.log("🔄 测试增强特征开关...");
    const checkbox = document.getElementById('useEnhancedFeatures');
    if (checkbox) {
        checkbox.checked = true;
        toggleEnhancedFeaturesConfig();
        console.log("✅ 增强特征配置已启用");
        
        // 测试自定义特征配置
        const strategySelect = document.getElementById('featureSelectionStrategy');
        if (strategySelect) {
            strategySelect.value = 'custom';
            toggleCustomFeaturesConfig();
            console.log("✅ 自定义特征配置已显示");
        }
    }
}

// 6. 测试获取增强特征配置
console.log("📋 测试获取增强特征配置:");
if (typeof getEnhancedFeaturesConfig === 'function') {
    const config = getEnhancedFeaturesConfig();
    console.log("增强特征配置:", config);
}

// 7. 手动测试MT5连接检查
console.log("📋 测试MT5连接检查:");
if (typeof checkMT5Connection === 'function') {
    console.log("🔄 检查MT5连接状态...");
    checkMT5Connection();
}

console.log("🎉 AI推理回测增强功能测试完成！");
console.log("💡 功能说明:");
console.log("   1. 增强特征配置：提升回测预测准确性");
console.log("   2. MT5自动重连：确保数据连接稳定");
console.log("   3. 连接状态监控：实时显示连接状态");
'''
    
    with open('browser_backtest_enhancements_test.js', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 浏览器测试脚本已创建: browser_backtest_enhancements_test.js")
    print("📋 使用方法:")
    print("   1. 打开AI推理回测页面: http://127.0.0.1:5000/deep-learning/backtest")
    print("   2. 按F12打开开发者工具")
    print("   3. 在Console标签中粘贴browser_backtest_enhancements_test.js的内容并执行")

if __name__ == "__main__":
    success = test_backtest_enhancements()
    create_browser_test_script()
    
    if success:
        print("\n🎉 AI推理回测增强功能测试完成！")
        print("📋 增强功能总结:")
        print("   1. ✅ 增强特征配置：52个高级技术指标特征")
        print("   2. ✅ 特征选择策略：推荐、全部、重要性、自定义")
        print("   3. ✅ MT5自动重连：连接断开时自动尝试重连")
        print("   4. ✅ 连接状态监控：实时显示MT5连接状态")
        print("   5. ✅ 回测前检查：确保MT5连接正常再开始回测")
        print("\n🔄 请访问回测页面并使用测试脚本验证功能")
    else:
        print("\n❌ AI推理回测增强功能测试失败")
        print("🔧 请检查上述错误信息")
