#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断手动按钮不能点击的问题
检查按钮启用条件和状态
"""

import requests
import json

def login_session():
    """登录获取会话"""
    session = requests.Session()
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    response = session.post('http://127.0.0.1:5000/login', data=login_data)
    if response.status_code == 200:
        print("✅ 登录成功")
        return session
    else:
        print(f"❌ 登录失败: {response.status_code}")
        return None

def check_button_enable_conditions(session):
    """检查按钮启用条件"""
    print("\n🔍 检查手动按钮启用条件")
    print("=" * 60)
    
    conditions = {}
    
    # 1. 检查MT5连接状态
    print("1. 检查MT5连接状态")
    try:
        response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
        if response.status_code == 200:
            result = response.json()
            mt5_connected = result.get('success') and result.get('connected')
            conditions['mt5_connected'] = mt5_connected
            print(f"   MT5连接: {'✅ 已连接' if mt5_connected else '❌ 未连接'}")
            
            if not mt5_connected:
                print(f"   错误信息: {result.get('error', '未知错误')}")
        else:
            print(f"   ❌ 无法获取MT5连接状态: HTTP {response.status_code}")
            conditions['mt5_connected'] = False
    except Exception as e:
        print(f"   ❌ 检查MT5连接异常: {e}")
        conditions['mt5_connected'] = False
    
    # 2. 检查AI交易状态（选择的交易模型）
    print("\n2. 检查AI交易状态")
    try:
        response = session.get('http://127.0.0.1:5000/api/deep-learning/auto-trading/status')
        if response.status_code == 200:
            result = response.json()
            ai_trading_active = result.get('success') and result.get('active')
            conditions['ai_trading_active'] = ai_trading_active
            print(f"   AI交易状态: {'✅ 活跃' if ai_trading_active else '⚠️ 未活跃'}")
            
            if ai_trading_active:
                model_info = result.get('model_info', {})
                conditions['selected_model'] = model_info
                print(f"   使用模型: {model_info.get('name', 'Unknown')}")
                print(f"   交易品种: {model_info.get('symbol', 'Unknown')}")
            else:
                conditions['selected_model'] = None
                print(f"   提示: 需要选择交易模型并启动AI交易")
        else:
            print(f"   ❌ 无法获取AI交易状态: HTTP {response.status_code}")
            conditions['ai_trading_active'] = False
            conditions['selected_model'] = None
    except Exception as e:
        print(f"   ❌ 检查AI交易状态异常: {e}")
        conditions['ai_trading_active'] = False
        conditions['selected_model'] = None
    
    # 3. 检查市场数据
    print("\n3. 检查市场数据")
    symbol = 'XAUUSD'  # 默认品种
    if conditions.get('selected_model'):
        symbol = conditions['selected_model'].get('symbol', 'XAUUSD')
    
    try:
        response = session.get(f'http://127.0.0.1:5000/api/mt5/market-data/{symbol}')
        if response.status_code == 200:
            result = response.json()
            market_data_ok = result.get('success') and result.get('market_data')
            conditions['market_data_ok'] = market_data_ok
            
            if market_data_ok:
                market_data = result['market_data']
                conditions['market_data'] = market_data
                print(f"   市场数据: ✅ 正常")
                print(f"   品种: {symbol}")
                print(f"   买价: {market_data.get('bid', 0):.5f}")
                print(f"   卖价: {market_data.get('ask', 0):.5f}")
                print(f"   点差: {((market_data.get('ask', 0) - market_data.get('bid', 0)) * 100000):.1f} 点")
            else:
                conditions['market_data'] = None
                print(f"   ❌ 市场数据异常: {result.get('error', '未知错误')}")
        else:
            print(f"   ❌ 无法获取市场数据: HTTP {response.status_code}")
            conditions['market_data_ok'] = False
            conditions['market_data'] = None
    except Exception as e:
        print(f"   ❌ 检查市场数据异常: {e}")
        conditions['market_data_ok'] = False
        conditions['market_data'] = None
    
    return conditions

def analyze_button_status(conditions):
    """分析按钮状态"""
    print(f"\n📊 按钮状态分析")
    print("=" * 60)
    
    # 按照代码逻辑检查条件
    mt5_connected = conditions.get('mt5_connected', False)
    selected_model = conditions.get('selected_model') is not None
    market_data_ok = conditions.get('market_data_ok', False)
    
    print(f"条件检查:")
    print(f"   MT5连接: {'✅' if mt5_connected else '❌'} {mt5_connected}")
    print(f"   选择模型: {'✅' if selected_model else '❌'} {selected_model}")
    print(f"   市场数据: {'✅' if market_data_ok else '❌'} {market_data_ok}")
    
    # 根据代码逻辑：canTrade = mt5Connected && selectedTradingModel && currentMarketData
    can_trade = mt5_connected and selected_model and market_data_ok
    
    print(f"\n按钮应该的状态:")
    print(f"   可以交易: {'✅ 是' if can_trade else '❌ 否'}")
    print(f"   按钮状态: {'启用' if can_trade else '禁用'}")
    
    if not can_trade:
        print(f"\n❌ 按钮被禁用的原因:")
        if not mt5_connected:
            print(f"   • MT5未连接")
        if not selected_model:
            print(f"   • 未选择交易模型")
        if not market_data_ok:
            print(f"   • 市场数据异常")
    
    return can_trade

def provide_solution(conditions, can_trade):
    """提供解决方案"""
    print(f"\n🔧 解决方案")
    print("=" * 60)
    
    if can_trade:
        print("✅ 按钮应该是可点击的")
        print("\n如果按钮仍然不能点击，请尝试:")
        print("1. 刷新页面")
        print("2. 运行以下控制台代码强制启用按钮:")
        
        print("\n// 强制启用按钮的代码")
        print("const buyBtn = document.getElementById('manualBuyBtn');")
        print("const sellBtn = document.getElementById('manualSellBtn');")
        print("if (buyBtn && sellBtn) {")
        print("    buyBtn.disabled = false;")
        print("    buyBtn.className = 'btn btn-success w-100';")
        print("    sellBtn.disabled = false;")
        print("    sellBtn.className = 'btn btn-danger w-100';")
        print("    console.log('✅ 按钮已强制启用');")
        print("}")
        
    else:
        print("❌ 按钮被禁用是正常的，需要满足以下条件:")
        
        if not conditions.get('mt5_connected', False):
            print("\n1. 修复MT5连接:")
            print("   • 确保MT5终端正在运行")
            print("   • 检查MT5是否允许自动交易")
            print("   • 重启MT5终端")
            print("   • 运行: python fix_mt5_service_connection.py")
        
        if not conditions.get('selected_model'):
            print("\n2. 选择交易模型:")
            print("   • 在AI推理交易页面选择一个训练完成的模型")
            print("   • 点击'启动AI交易'按钮")
            print("   • 确保AI交易状态显示为'活跃'")
        
        if not conditions.get('market_data_ok', False):
            print("\n3. 修复市场数据:")
            print("   • 检查网络连接")
            print("   • 确保MT5连接正常")
            print("   • 检查交易品种是否可用")
        
        print("\n💡 快速修复步骤:")
        print("1. 重启MT5终端")
        print("2. 刷新AI推理交易页面")
        print("3. 选择交易模型并启动AI交易")
        print("4. 等待市场数据加载完成")

def main():
    """主函数"""
    print("🔧 诊断手动按钮不能点击的问题")
    print("=" * 80)
    
    # 1. 登录
    session = login_session()
    if not session:
        return
    
    # 2. 检查按钮启用条件
    conditions = check_button_enable_conditions(session)
    
    # 3. 分析按钮状态
    can_trade = analyze_button_status(conditions)
    
    # 4. 提供解决方案
    provide_solution(conditions, can_trade)
    
    print(f"\n📋 总结")
    print("=" * 80)
    
    if can_trade:
        print("✅ 所有条件都满足，按钮应该可以点击")
        print("如果仍然不能点击，可能是前端状态同步问题，建议刷新页面")
    else:
        print("❌ 条件不满足，按钮被正确禁用")
        print("请按照上面的解决方案修复相关问题")
    
    return 0

if __name__ == "__main__":
    main()
