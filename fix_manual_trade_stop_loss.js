// 修复手动下单止损配置的JavaScript代码
// 请在浏览器控制台中运行此代码

console.log('🔧 开始诊断和修复手动下单止损配置...');

// 1. 检查当前配置
console.log('\n📊 检查当前AI交易配置:');

const stopLossElement = document.getElementById('stopLossPips');
const takeProfitElement = document.getElementById('takeProfitPips');
const tradingLotSizeElement = document.getElementById('tradingLotSize');
const minConfidenceElement = document.getElementById('minConfidence');

if (stopLossElement) {
    const currentStopLoss = parseInt(stopLossElement.value);
    console.log('止损 (stopLossPips):', currentStopLoss, 'pips');
    
    if (currentStopLoss < 20) {
        console.log('⚠️ 止损过小！当前值:', currentStopLoss, 'pips (建议至少20 pips)');
    } else if (currentStopLoss > 200) {
        console.log('⚠️ 止损过大！当前值:', currentStopLoss, 'pips (建议不超过200 pips)');
    } else {
        console.log('✅ 止损配置合理');
    }
} else {
    console.log('❌ 找不到stopLossPips元素');
}

if (takeProfitElement) {
    const currentTakeProfit = parseInt(takeProfitElement.value);
    console.log('止盈 (takeProfitPips):', currentTakeProfit, 'pips');
} else {
    console.log('❌ 找不到takeProfitPips元素');
}

if (tradingLotSizeElement) {
    const currentLotSize = parseFloat(tradingLotSizeElement.value);
    console.log('手数 (tradingLotSize):', currentLotSize);
} else {
    console.log('❌ 找不到tradingLotSize元素');
}

if (minConfidenceElement) {
    const currentMinConfidence = parseFloat(minConfidenceElement.value);
    console.log('最低置信度 (minConfidence):', currentMinConfidence);
} else {
    console.log('❌ 找不到minConfidence元素');
}

// 2. 检查getTradingConfig函数
console.log('\n🔍 检查getTradingConfig函数:');
if (typeof getTradingConfig === 'function') {
    try {
        const config = getTradingConfig();
        console.log('getTradingConfig()返回:', config);
        
        if (config.stop_loss_pips < 20) {
            console.log('⚠️ 配置中的止损过小:', config.stop_loss_pips, 'pips');
        }
    } catch (error) {
        console.log('❌ 调用getTradingConfig失败:', error);
    }
} else {
    console.log('❌ getTradingConfig函数不存在');
}

// 3. 修复配置
console.log('\n🔧 修复止损配置...');

let needsFix = false;

if (stopLossElement) {
    const currentStopLoss = parseInt(stopLossElement.value);
    if (currentStopLoss < 20) {
        stopLossElement.value = '50';  // 设置为50 pips
        console.log('✅ 止损已修正: ' + currentStopLoss + ' pips → 50 pips');
        needsFix = true;
    }
}

if (takeProfitElement) {
    const currentTakeProfit = parseInt(takeProfitElement.value);
    if (currentTakeProfit < 30) {
        takeProfitElement.value = '100';  // 设置为100 pips
        console.log('✅ 止盈已修正: ' + currentTakeProfit + ' pips → 100 pips');
        needsFix = true;
    }
}

if (tradingLotSizeElement) {
    const currentLotSize = parseFloat(tradingLotSizeElement.value);
    if (currentLotSize <= 0 || isNaN(currentLotSize)) {
        tradingLotSizeElement.value = '0.01';
        console.log('✅ 手数已修正: ' + currentLotSize + ' → 0.01');
        needsFix = true;
    }
}

if (minConfidenceElement) {
    const currentMinConfidence = parseFloat(minConfidenceElement.value);
    if (currentMinConfidence <= 0 || currentMinConfidence >= 1 || isNaN(currentMinConfidence)) {
        minConfidenceElement.value = '0.3';
        console.log('✅ 最低置信度已修正: ' + currentMinConfidence + ' → 0.3');
        needsFix = true;
    }
}

// 4. 验证修复后的配置
if (needsFix) {
    console.log('\n✅ 配置已修复，验证新配置:');
    
    if (typeof getTradingConfig === 'function') {
        try {
            const newConfig = getTradingConfig();
            console.log('修复后的配置:', newConfig);
            
            console.log('\n📊 配置详情:');
            console.log('• 止损:', newConfig.stop_loss_pips, 'pips');
            console.log('• 止盈:', newConfig.take_profit_pips, 'pips');
            console.log('• 手数:', newConfig.lot_size);
            console.log('• 最低置信度:', newConfig.min_confidence);
            console.log('• 最大持仓:', newConfig.max_positions);
            
        } catch (error) {
            console.log('❌ 验证配置失败:', error);
        }
    }
} else {
    console.log('\n✅ 配置正常，无需修复');
}

// 5. 应用推荐的XAUUSD配置
console.log('\n🎯 应用XAUUSD(黄金)推荐配置...');

function applyXAUUSDConfig() {
    // XAUUSD推荐配置
    const xauusdConfig = {
        stopLoss: 80,      // 黄金波动大，需要较大止损
        takeProfit: 160,   // 2:1的风险回报比
        lotSize: 0.01,     // 小手数控制风险
        minConfidence: 0.3, // 中等置信度
        maxPositions: 3     // 限制持仓数量
    };
    
    if (stopLossElement) {
        stopLossElement.value = xauusdConfig.stopLoss.toString();
    }
    if (takeProfitElement) {
        takeProfitElement.value = xauusdConfig.takeProfit.toString();
    }
    if (tradingLotSizeElement) {
        tradingLotSizeElement.value = xauusdConfig.lotSize.toString();
    }
    if (minConfidenceElement) {
        minConfidenceElement.value = xauusdConfig.minConfidence.toString();
    }
    
    const maxPositionsElement = document.getElementById('maxPositions');
    if (maxPositionsElement) {
        maxPositionsElement.value = xauusdConfig.maxPositions.toString();
    }
    
    console.log('✅ XAUUSD配置已应用:');
    console.log('• 止损: 80 pips (适合黄金波动)');
    console.log('• 止盈: 160 pips (2:1风险回报比)');
    console.log('• 手数: 0.01 (控制风险)');
    console.log('• 置信度: 0.3 (中等要求)');
    console.log('• 最大持仓: 3 (分散风险)');
    
    return xauusdConfig;
}

// 询问是否应用XAUUSD配置
if (confirm('是否应用XAUUSD(黄金)推荐配置？\n止损: 80 pips\n止盈: 160 pips\n手数: 0.01')) {
    const appliedConfig = applyXAUUSDConfig();
    
    // 验证应用后的配置
    if (typeof getTradingConfig === 'function') {
        try {
            const finalConfig = getTradingConfig();
            console.log('\n🎉 最终配置:', finalConfig);
        } catch (error) {
            console.log('❌ 验证最终配置失败:', error);
        }
    }
} else {
    console.log('⏭️ 跳过XAUUSD配置应用');
}

// 6. 提供测试建议
console.log('\n💡 测试建议:');
console.log('1. 现在可以尝试手动下单');
console.log('2. 检查MT5中的实际止损价格');
console.log('3. 确认止损距离是否合理');
console.log('4. 如果还是太小，检查后端计算逻辑');

console.log('\n🔍 调试命令:');
console.log('• 查看当前配置: getTradingConfig()');
console.log('• 重新应用配置: applyXAUUSDConfig()');
console.log('• 检查元素值: document.getElementById("stopLossPips").value');

console.log('\n🎉 手动下单止损配置修复完成！');
console.log('如果问题仍然存在，可能需要检查后端的止损计算逻辑。');
