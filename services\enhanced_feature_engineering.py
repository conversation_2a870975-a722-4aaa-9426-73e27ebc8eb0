#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的特征工程服务 - 专为深度学习模型优化
实现您提到的所有优化建议：
1. 增强的布林带特征 (挤压信号、突破信号)
2. ATR (平均真实波幅)
3. 随机指标 (Stochastic Oscillator)
4. 组合信号和确认机制
5. 动态风险管理
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
from services.technical_indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

class EnhancedFeatureEngineering:
    """增强的特征工程服务"""
    
    def __init__(self):
        self.ti = TechnicalIndicators()
    
    def calculate_all_enhanced_features(self, price_data: np.ndarray) -> Dict[str, np.ndarray]:
        """
        计算所有增强的技术指标特征
        
        Args:
            price_data: 价格数据 [open, high, low, close, volume]
            
        Returns:
            Dict: 包含所有特征的字典
        """
        logger.info("🔧 开始计算增强技术指标特征...")
        
        # 提取价格数据
        open_prices = pd.Series(price_data[:, 0])
        high_prices = pd.Series(price_data[:, 1])
        low_prices = pd.Series(price_data[:, 2])
        close_prices = pd.Series(price_data[:, 3])
        volumes = pd.Series(price_data[:, 4]) if price_data.shape[1] > 4 else None
        
        features = {}
        
        # 1. 增强的布林带特征
        logger.info("📈 计算增强布林带特征...")
        bb_features = self.ti.enhanced_bollinger_bands(close_prices)
        for key, value in bb_features.items():
            features[f'bb_{key}'] = value.fillna(0).values
        
        # 2. 增强的随机指标特征
        logger.info("🎯 计算增强随机指标特征...")
        stoch_features = self.ti.enhanced_stochastic(high_prices, low_prices, close_prices)
        for key, value in stoch_features.items():
            features[f'stoch_{key}'] = value.fillna(0).values
        
        # 3. 增强的ATR特征
        logger.info("📏 计算增强ATR特征...")
        atr_features = self.ti.enhanced_atr(high_prices, low_prices, close_prices)
        for key, value in atr_features.items():
            features[f'atr_{key}'] = value.fillna(0).values
        
        # 4. 组合信号
        logger.info("🔗 计算组合信号...")
        combined_features = self.ti.combined_signals(high_prices, low_prices, close_prices)
        for key, value in combined_features.items():
            features[f'combined_{key}'] = value.astype(int).values
        
        # 5. 市场状态分析
        logger.info("🌊 计算市场状态特征...")
        market_features = self.ti.market_regime_analysis(high_prices, low_prices, close_prices, volumes)
        for key, value in market_features.items():
            if isinstance(value, pd.Series):
                if value.dtype == 'object':
                    # 将分类特征转换为数值
                    features[f'market_{key}'] = pd.Categorical(value).codes
                else:
                    features[f'market_{key}'] = value.fillna(0).values
        
        # 6. 基础价格特征 (标准化)
        logger.info("💰 计算基础价格特征...")
        price_features = self._calculate_price_features(price_data)
        features.update(price_features)
        
        # 7. 动量和趋势特征
        logger.info("🚀 计算动量和趋势特征...")
        momentum_features = self._calculate_momentum_features(close_prices)
        features.update(momentum_features)
        
        logger.info(f"✅ 特征计算完成，共生成 {len(features)} 个特征")
        return features
    
    def _calculate_price_features(self, price_data: np.ndarray) -> Dict[str, np.ndarray]:
        """计算基础价格特征"""
        features = {}
        
        # 价格变化率
        for i, name in enumerate(['open', 'high', 'low', 'close']):
            prices = price_data[:, i]
            returns = np.diff(prices) / prices[:-1]
            returns = np.concatenate([[0], returns])  # 添加第一个值
            features[f'{name}_returns'] = returns
        
        # 价格相对位置 (收盘价在当日价格范围内的位置)
        high_low_range = price_data[:, 1] - price_data[:, 2]
        close_position = np.where(high_low_range != 0, 
                                (price_data[:, 3] - price_data[:, 2]) / high_low_range, 
                                0.5)
        features['close_position'] = close_position
        
        # 价格间隙 (开盘价与前一日收盘价的差异)
        gap = np.diff(price_data[:, 0]) / price_data[:-1, 3]
        gap = np.concatenate([[0], gap])
        features['price_gap'] = gap
        
        return features
    
    def _calculate_momentum_features(self, close_prices: pd.Series) -> Dict[str, np.ndarray]:
        """计算动量和趋势特征"""
        features = {}
        
        # 多周期动量
        for period in [5, 10, 20]:
            momentum = close_prices.pct_change(period)
            features[f'momentum_{period}'] = momentum.fillna(0).values
        
        # 价格加速度 (二阶导数)
        returns = close_prices.pct_change()
        acceleration = returns.diff()
        features['price_acceleration'] = acceleration.fillna(0).values
        
        # 趋势强度 (基于线性回归斜率)
        trend_strength = self._calculate_trend_strength(close_prices)
        features['trend_strength'] = trend_strength
        
        return features
    
    def _calculate_trend_strength(self, prices: pd.Series, window: int = 20) -> np.ndarray:
        """计算趋势强度"""
        trend_strength = np.zeros(len(prices))
        
        for i in range(window, len(prices)):
            y = prices.iloc[i-window:i].values
            x = np.arange(window)
            
            # 计算线性回归斜率
            slope = np.polyfit(x, y, 1)[0]
            trend_strength[i] = slope / prices.iloc[i] if prices.iloc[i] != 0 else 0
        
        return trend_strength
    
    def prepare_features_for_model(self, features_dict: Dict[str, np.ndarray], 
                                 feature_selection: Optional[List[str]] = None) -> np.ndarray:
        """
        为深度学习模型准备特征矩阵
        
        Args:
            features_dict: 特征字典
            feature_selection: 选择的特征列表 (None表示使用所有特征)
            
        Returns:
            np.ndarray: 标准化的特征矩阵
        """
        logger.info("🤖 为深度学习模型准备特征矩阵...")
        
        # 选择特征
        if feature_selection is None:
            selected_features = list(features_dict.keys())
        else:
            selected_features = [f for f in feature_selection if f in features_dict]
        
        logger.info(f"📊 选择了 {len(selected_features)} 个特征")
        
        # 构建特征矩阵
        feature_arrays = []
        feature_names = []
        
        for feature_name in selected_features:
            feature_data = features_dict[feature_name]
            
            # 确保是1维数组
            if len(feature_data.shape) > 1:
                feature_data = feature_data.flatten()
            
            # 检查数据有效性
            if np.any(np.isnan(feature_data)) or np.any(np.isinf(feature_data)):
                logger.warning(f"⚠️ 特征 {feature_name} 包含无效值，进行清理")
                feature_data = np.nan_to_num(feature_data, nan=0.0, posinf=0.0, neginf=0.0)
            
            # 标准化特征
            feature_mean = feature_data.mean()
            feature_std = feature_data.std()
            
            if feature_std > 1e-10:
                normalized_feature = (feature_data - feature_mean) / feature_std
            else:
                logger.warning(f"⚠️ 特征 {feature_name} 标准差过小，只进行中心化")
                normalized_feature = feature_data - feature_mean
            
            feature_arrays.append(normalized_feature.reshape(-1, 1))
            feature_names.append(feature_name)
        
        # 合并所有特征
        if feature_arrays:
            feature_matrix = np.concatenate(feature_arrays, axis=1)
            logger.info(f"✅ 特征矩阵构建完成: {feature_matrix.shape}")
            logger.info(f"📋 特征列表: {feature_names[:10]}{'...' if len(feature_names) > 10 else ''}")
            return feature_matrix
        else:
            logger.error("❌ 没有有效特征可用")
            raise ValueError("没有有效特征可用于模型训练")
    
    def get_recommended_features(self) -> List[str]:
        """
        获取推荐的特征列表（核心特征集 - 15个）
        平衡效果和效率的最佳选择，大幅缩减特征数量

        Returns:
            List[str]: 推荐特征列表
        """
        return [
            # 布林带核心特征 (3个)
            'bb_percent_b',              # 价格在布林带中的位置
            'bb_band_width',             # 布林带宽度（波动性）
            'bb_squeeze',                # 布林带收缩（突破前兆）

            # ATR波动率核心特征 (2个)
            'atr_atr',                   # 平均真实波幅
            'atr_ratio',                 # ATR比率

            # 随机指标核心特征 (3个)
            'stoch_stoch_k',             # 随机指标K值
            'stoch_overbought',          # 超买信号
            'stoch_oversold',            # 超卖信号

            # 组合信号核心特征 (3个)
            'combined_squeeze_low_vol',   # 低波动挤压信号
            'combined_breakout_confirmed', # 突破确认信号
            'combined_bullish_confluence', # 多头汇合信号

            # 市场状态核心特征 (2个)
            'market_trend_strength',      # 趋势强度
            'market_trending_market',     # 是否趋势市场

            # 价格动量核心特征 (2个)
            'close_returns',             # 收益率
            'momentum_20'                # 20期动量
        ]

    def get_minimal_features(self) -> List[str]:
        """
        获取最小特征集（10个）
        最核心的技术指标，适合快速训练

        Returns:
            List[str]: 最小特征列表
        """
        return [
            # 布林带 (2个)
            'bb_percent_b',
            'bb_band_width',

            # ATR (1个)
            'atr_atr',

            # 随机指标 (2个)
            'stoch_stoch_k',
            'stoch_overbought',

            # 组合信号 (2个)
            'combined_squeeze_low_vol',
            'combined_breakout_confirmed',

            # 市场状态 (1个)
            'market_trend_strength',

            # 动量 (2个)
            'close_returns',
            'momentum_20'
        ]

    def get_extended_features(self) -> List[str]:
        """
        获取扩展特征集（25个）
        更全面的技术指标，适合高精度需求

        Returns:
            List[str]: 扩展特征列表
        """
        return self.get_recommended_features() + [
            # 额外布林带特征 (2个)
            'bb_breakout',               # 布林带突破
            'bb_distance_from_middle',   # 距离中轨距离

            # 额外ATR特征 (2个)
            'atr_low_volatility',        # 低波动状态
            'atr_high_volatility',       # 高波动状态

            # 额外随机指标 (2个)
            'stoch_stoch_d',            # 随机指标D值
            'stoch_k_d_diff',           # K-D差值

            # 额外组合信号 (2个)
            'combined_bearish_confluence', # 空头汇合信号
            'combined_oversold_confirmed', # 超卖确认信号

            # 额外市场状态 (1个)
            'market_trend_direction',    # 趋势方向

            # 额外动量特征 (1个)
            'trend_strength'             # 趋势强度指标
        ]

    def get_feature_set_info(self) -> Dict[str, Dict]:
        """
        获取所有特征集的信息

        Returns:
            Dict: 特征集信息
        """
        return {
            'minimal': {
                'name': '最小特征集',
                'count': len(self.get_minimal_features()),
                'description': '10个最核心的技术指标，快速训练',
                'features': self.get_minimal_features(),
                'use_case': '快速训练、资源受限环境'
            },
            'recommended': {
                'name': '推荐特征集',
                'count': len(self.get_recommended_features()),
                'description': '15个核心技术指标，平衡效果和效率',
                'features': self.get_recommended_features(),
                'use_case': '默认选择、平衡性能'
            },
            'extended': {
                'name': '扩展特征集',
                'count': len(self.get_extended_features()),
                'description': '25个精选技术指标，高精度需求',
                'features': self.get_extended_features(),
                'use_case': '高精度需求、充足资源'
            }
        }

    def create_feature_importance_analysis(self, features_dict: Dict[str, np.ndarray],
                                         target: np.ndarray) -> Dict[str, float]:
        """
        分析特征重要性 (简单的相关性分析)
        
        Args:
            features_dict: 特征字典
            target: 目标变量
            
        Returns:
            Dict[str, float]: 特征重要性分数
        """
        logger.info("📊 分析特征重要性...")
        
        importance_scores = {}
        
        for feature_name, feature_data in features_dict.items():
            try:
                # 计算与目标变量的相关性
                correlation = np.corrcoef(feature_data, target)[0, 1]
                if np.isnan(correlation):
                    correlation = 0.0
                
                importance_scores[feature_name] = abs(correlation)
                
            except Exception as e:
                logger.warning(f"⚠️ 计算特征 {feature_name} 重要性失败: {e}")
                importance_scores[feature_name] = 0.0
        
        # 按重要性排序
        sorted_features = sorted(importance_scores.items(), key=lambda x: x[1], reverse=True)
        
        logger.info("🏆 特征重要性排名 (前10):")
        for i, (feature, score) in enumerate(sorted_features[:10]):
            logger.info(f"   {i+1}. {feature}: {score:.4f}")
        
        return importance_scores
