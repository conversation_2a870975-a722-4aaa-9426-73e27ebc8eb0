#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恢复高标准训练配置
"""

import requests
import sqlite3
import time
import json

def login_session():
    """登录并返回会话"""
    session = requests.Session()
    
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def stop_current_training():
    """停止当前的简化训练"""
    print('🛑 停止当前的简化训练')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 获取当前运行的任务
        cursor.execute("""
            SELECT id, model_id FROM training_tasks 
            WHERE status IN ('running', 'data_ready')
            ORDER BY created_at DESC
        """)
        
        running_tasks = cursor.fetchall()
        
        print(f'📋 找到 {len(running_tasks)} 个需要停止的任务')
        
        # 停止所有运行中的任务
        for task in running_tasks:
            task_id, model_id = task
            print(f'🛑 停止任务: {task_id}')
            
            cursor.execute("""
                UPDATE training_tasks 
                SET status = 'stopped', 
                    updated_at = datetime('now'),
                    logs = '{"stage": "stopped", "message": "用户要求停止简化训练，恢复高标准配置"}'
                WHERE id = ?
            """, (task_id,))
            
            print(f'✅ 任务已停止: {task_id}')
        
        conn.commit()
        conn.close()
        
        print(f'🎯 已停止 {len(running_tasks)} 个简化训练任务')
        return len(running_tasks)
        
    except Exception as e:
        print(f'❌ 停止训练失败: {e}')
        return 0

def clear_resources():
    """清理系统资源"""
    print('\n🔧 清理系统资源')
    print('=' * 50)
    
    try:
        import torch
        import psutil
        
        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print('✅ GPU内存已清理')
        
        # 终止训练进程
        killed = 0
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'train' in cmdline.lower() or 'deep_learning' in cmdline.lower():
                        print(f'🔹 终止进程 PID: {proc.info["pid"]}')
                        proc.kill()
                        killed += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if killed > 0:
            print(f'✅ 已终止 {killed} 个训练进程')
            time.sleep(3)
        else:
            print('ℹ️ 没有找到需要终止的训练进程')
            
    except Exception as e:
        print(f'❌ 清理资源失败: {e}')

def start_high_standard_training(session):
    """启动高标准训练配置"""
    print('\n🚀 启动高标准训练配置')
    print('=' * 50)
    
    # 恢复到原始的高标准配置
    high_standard_configs = [
        {
            'model_name': f'高标准基础模型_{int(time.time())}',
            'model_type': 'lstm',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'mode': 'days',
                'training_days': 365  # 恢复到1年数据
            },
            'sequence_length': 60,      # 恢复到更长序列
            'hidden_size': 128,         # 恢复到更大隐藏层
            'num_layers': 3,            # 恢复到多层LSTM
            'dropout': 0.3,             # 恢复到标准dropout
            'batch_size': 32,           # 恢复到标准批次大小
            'learning_rate': 0.001,
            'epochs': 200,              # 恢复到充分训练轮数
            'patience': 15,             # 恢复到更大耐心值
            'early_stopping': True,
            'min_epochs': 20,           # 恢复到更多最小轮数
            'use_gpu': True,
            'save_checkpoints': True,
            # 使用基础特征
            'use_enhanced_features': False,
            'features': {
                'price': True,
                'volume': True,
                'technical': True,
                'time': True
            }
        },
        {
            'model_name': f'高标准推荐增强特征模型_{int(time.time())}',
            'model_type': 'lstm',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'mode': 'days',
                'training_days': 365
            },
            'sequence_length': 60,
            'hidden_size': 128,
            'num_layers': 3,
            'dropout': 0.3,
            'batch_size': 32,
            'learning_rate': 0.001,
            'epochs': 200,
            'patience': 15,
            'early_stopping': True,
            'min_epochs': 20,
            'use_gpu': True,
            'save_checkpoints': True,
            # 使用推荐增强特征（31个特征）
            'use_enhanced_features': True,
            'feature_selection_strategy': 'recommended',
            'analyze_feature_importance': True,
            'selected_features': None
        },
        {
            'model_name': f'高标准全部增强特征模型_{int(time.time())}',
            'model_type': 'lstm',
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'data_config': {
                'mode': 'days',
                'training_days': 365
            },
            'sequence_length': 60,
            'hidden_size': 128,
            'num_layers': 3,
            'dropout': 0.3,
            'batch_size': 32,
            'learning_rate': 0.001,
            'epochs': 200,
            'patience': 15,
            'early_stopping': True,
            'min_epochs': 20,
            'use_gpu': True,
            'save_checkpoints': True,
            # 使用全部增强特征（52个特征）
            'use_enhanced_features': True,
            'feature_selection_strategy': 'all',
            'analyze_feature_importance': True,
            'selected_features': None
        }
    ]
    
    started_tasks = []
    
    for i, config in enumerate(high_standard_configs):
        print(f"\n📊 启动配置 {i+1}: {config['model_name']}")
        print(f"   特征类型: {'增强特征' if config['use_enhanced_features'] else '基础特征'}")
        if config['use_enhanced_features']:
            strategy = config['feature_selection_strategy']
            feature_count = '31个' if strategy == 'recommended' else '52个' if strategy == 'all' else '自定义'
            print(f"   特征策略: {strategy} ({feature_count}特征)")
        print(f"   模型复杂度: 高标准 (hidden_size={config['hidden_size']}, layers={config['num_layers']})")
        print(f"   训练数据: {config['data_config']['training_days']}天")
        print(f"   训练轮数: {config['epochs']}")
        print(f"   序列长度: {config['sequence_length']}")
        print(f"   批次大小: {config['batch_size']}")
        
        try:
            response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                                   json=config,
                                   headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    task_id = result.get('task_id')
                    print(f"   ✅ 启动成功: {task_id}")
                    started_tasks.append({
                        'task_id': task_id,
                        'name': config['model_name'],
                        'type': '增强特征' if config['use_enhanced_features'] else '基础特征',
                        'strategy': config.get('feature_selection_strategy', 'basic')
                    })
                else:
                    print(f"   ❌ 启动失败: {result.get('error')}")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 启动异常: {e}")
        
        # 等待一下再启动下一个
        time.sleep(3)
    
    # 总结
    print(f"\n🎯 高标准训练启动总结:")
    print(f"   成功启动: {len(started_tasks)} 个任务")
    
    for task in started_tasks:
        print(f"   - {task['name']} ({task['type']}): {task['task_id']}")
    
    if started_tasks:
        print(f"\n💡 高标准配置说明:")
        print(f"   - 训练数据: 365天（1年完整数据）")
        print(f"   - 模型复杂度: hidden_size=128, layers=3")
        print(f"   - 序列长度: 60（更长的时间序列）")
        print(f"   - 批次大小: 32（标准大小）")
        print(f"   - 训练轮数: 200（充分训练）")
        print(f"   - 早停耐心: 15（避免过早停止）")
        print(f"   - 增强特征: 推荐31个 + 全部52个特征")
        
        print(f"\n📊 预期效果:")
        print(f"   - 更高的预测准确性")
        print(f"   - 更好的泛化能力")
        print(f"   - 充分利用增强特征优势")
        print(f"   - 更长的训练时间但更好的效果")
        
        print(f"\n⚠️ 注意事项:")
        print(f"   - 训练时间会比较长（可能数小时）")
        print(f"   - 需要充足的GPU内存和计算资源")
        print(f"   - 建议监控训练进度和系统资源")
    else:
        print(f"\n❌ 没有成功启动任何高标准训练任务")
    
    return started_tasks

def main():
    """主函数"""
    print('🔧 恢复高标准训练配置')
    print('=' * 80)
    
    # 登录
    session = login_session()
    if not session:
        return
    
    # 停止当前的简化训练
    stopped_count = stop_current_training()
    
    # 清理系统资源
    clear_resources()
    
    # 启动高标准训练
    started_tasks = start_high_standard_training(session)
    
    print(f"\n✅ 恢复高标准训练完成！")
    print(f"📋 总结:")
    print(f"   - 已停止简化训练: {stopped_count} 个任务")
    print(f"   - 已启动高标准训练: {len(started_tasks)} 个任务")
    print(f"   - 系统资源已清理")
    
    if started_tasks:
        print(f"\n🚀 高标准训练已启动，包括:")
        print(f"   1. 基础特征模型（8个特征）")
        print(f"   2. 推荐增强特征模型（31个特征）")
        print(f"   3. 全部增强特征模型（52个特征）")
        
        print(f"\n💡 后续操作:")
        print(f"   1. 等待数据准备完成")
        print(f"   2. 启动模型训练: python start_pending_training.py")
        print(f"   3. 监控训练进度: python check_training_status.py")

if __name__ == "__main__":
    main()
