#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导航结构更改
验证AI推理交易是否正确移动到交易管理模块下，以及交易查询模块是否被删除
"""

import requests
from bs4 import BeautifulSoup
import re

def test_navigation_structure():
    """测试导航结构"""
    print("🧪 测试导航结构更改")
    print("=" * 60)
    
    # 创建会话
    session = requests.Session()
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post('http://127.0.0.1:5000/login', data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 2. 访问首页获取导航结构
        print("\n📋 分析导航结构...")
        response = session.get('http://127.0.0.1:5000/dashboard')
        
        if response.status_code != 200:
            print(f"❌ 访问首页失败: {response.status_code}")
            return False
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找导航栏
        sidebar = soup.find('nav', class_='sidebar')
        if not sidebar:
            print("❌ 未找到导航栏")
            return False
        
        # 提取所有导航链接
        nav_links = sidebar.find_all('a', class_='nav-link')
        
        # 分析导航结构
        navigation_structure = {}
        current_section = None
        
        for link in nav_links:
            # 检查是否是section标题
            section_heading = link.find_previous('h6', class_='sidebar-heading')
            if section_heading:
                section_text = section_heading.get_text(strip=True)
                # 移除badge文本
                section_text = re.sub(r'\s*(VIP|管理员|GPU)\s*$', '', section_text)
                if section_text != current_section:
                    current_section = section_text
                    navigation_structure[current_section] = []
            
            # 获取链接文本和href
            link_text = link.get_text(strip=True)
            # 移除badge文本
            link_text = re.sub(r'\s*(SAFE|AI|验证|VIP|管理员|GPU)\s*$', '', link_text)
            href = link.get('href', '')
            
            if current_section and link_text and href != '#':
                navigation_structure[current_section].append({
                    'text': link_text,
                    'href': href
                })
        
        # 3. 验证更改
        print("\n🔍 验证导航结构更改...")
        
        success = True
        
        # 检查交易管理模块是否包含AI推理交易
        if '交易管理' in navigation_structure:
            trading_links = [link['text'] for link in navigation_structure['交易管理']]
            
            if 'AI推理交易' in trading_links:
                print("✅ AI推理交易已成功移动到交易管理模块")
            else:
                print("❌ AI推理交易未在交易管理模块中找到")
                print(f"   交易管理模块包含: {trading_links}")
                success = False
            
            # 检查交易查询是否已删除
            if '交易查询' in trading_links:
                print("❌ 交易查询模块仍然存在，应该被删除")
                success = False
            else:
                print("✅ 交易查询模块已成功删除")
        else:
            print("❌ 未找到交易管理模块")
            success = False
        
        # 检查深度学习模块是否不再包含模型推理
        if '深度学习' in navigation_structure:
            dl_links = [link['text'] for link in navigation_structure['深度学习']]
            
            if '模型推理' in dl_links:
                print("❌ 模型推理仍在深度学习模块中")
                success = False
            else:
                print("✅ 模型推理已从深度学习模块中移除")
        
        # 4. 显示完整的导航结构
        print(f"\n📊 当前导航结构:")
        for section, links in navigation_structure.items():
            print(f"\n🔹 {section}:")
            for link in links:
                print(f"   • {link['text']}")
        
        # 5. 测试AI推理交易页面访问
        print(f"\n🌐 测试AI推理交易页面访问...")
        response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if response.status_code == 200:
            print("✅ AI推理交易页面访问正常")
            
            # 检查页面标题是否已更新
            soup = BeautifulSoup(response.text, 'html.parser')
            page_title = soup.find('title')
            if page_title and 'AI推理交易' in page_title.get_text():
                print("✅ 页面标题已更新为'AI推理交易'")
            else:
                print("⚠️ 页面标题可能未完全更新")
            
            # 检查页面主标题
            main_title = soup.find('h1')
            if main_title and 'AI推理交易' in main_title.get_text():
                print("✅ 页面主标题已更新")
            else:
                print("⚠️ 页面主标题可能未完全更新")
                
        else:
            print(f"❌ AI推理交易页面访问失败: {response.status_code}")
            success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🔧 导航结构更改验证工具")
    print("=" * 80)
    
    print("📋 验证内容:")
    print("• AI推理交易是否移动到交易管理模块")
    print("• 交易查询模块是否已删除")
    print("• 模型推理是否从深度学习模块移除")
    print("• AI推理交易页面是否正常访问")
    print("• 页面标题是否已更新")
    print()
    
    success = test_navigation_structure()
    
    if success:
        print("\n🎉 导航结构更改验证成功！")
        print("✅ 所有更改都已正确实施:")
        print("   • AI推理交易已移动到交易管理模块下")
        print("   • 交易查询模块已成功删除")
        print("   • 模型推理已从深度学习模块移除")
        print("   • 页面标题和内容已相应更新")
        print()
        print("💡 用户现在可以在交易管理模块下找到AI推理交易功能")
    else:
        print("\n❌ 导航结构更改验证失败")
        print("🔧 请检查相关配置和实施情况")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
