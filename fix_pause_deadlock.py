#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复暂停死锁问题
清理训练控制状态并重启应用
"""

import requests
import time

def clear_training_control_states():
    """清理训练控制状态"""
    print('🔧 清理训练控制状态')
    print('=' * 60)
    
    # 登录并清理状态
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
            
            # 尝试获取当前任务列表
            tasks_response = session.get('http://127.0.0.1:5000/api/deep-learning/training-tasks')
            
            if tasks_response.status_code == 200:
                tasks = tasks_response.json()
                print(f"📊 找到 {len(tasks)} 个任务")
                
                # 查找running状态的任务
                running_tasks = [task for task in tasks if task.get('status') == 'running']
                
                if running_tasks:
                    print(f"🔍 找到 {len(running_tasks)} 个running任务")
                    
                    for task in running_tasks:
                        task_id = task.get('id')
                        print(f"   任务: {task_id}")
                        
                        # 尝试清除暂停状态
                        clear_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/training/{task_id}/resume')
                        
                        if clear_response.status_code == 200:
                            print(f"   ✅ 已清除暂停状态")
                        else:
                            print(f"   ⚠️ 清除暂停状态失败: {clear_response.status_code}")
                else:
                    print("✅ 没有running状态的任务")
            else:
                print(f"❌ 获取任务列表失败: {tasks_response.status_code}")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 清理状态失败: {e}")

def test_immediate_training():
    """立即测试训练"""
    print('\n🧪 立即测试训练')
    print('=' * 60)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 创建超简单测试任务
    ultra_simple_config = {
        'model_name': f'暂停修复测试_{int(time.time())}',
        'model_type': 'lstm',
        'symbol': 'XAUUSD',
        'timeframe': 'H1',
        'data_config': {'mode': 'days', 'training_days': 20},  # 更少数据
        'sequence_length': 5,   # 更短序列
        'hidden_size': 8,       # 更小模型
        'num_layers': 1,        # 单层
        'dropout': 0.0,         # 无dropout
        'batch_size': 2,        # 最小batch
        'learning_rate': 0.01,  # 较大学习率快速收敛
        'epochs': 3,            # 只训练3轮
        'patience': 1,          # 最小耐心
        'early_stopping': False, # 禁用早停
        'min_epochs': 1,        # 最少1轮
        'use_gpu': False,       # CPU训练
        'save_checkpoints': False,  # 不保存检查点
        'use_enhanced_features': False,  # 基础特征
        'include_basic_features': True,
        'analyze_feature_importance': False,
        'auto_start_training': False,  # 手动启动
        'validation_split': 0.2,
        # 数据加载器设置
        'num_workers': 0,
        'pin_memory': False,
        'drop_last': True,
        'persistent_workers': False
    }
    
    try:
        response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                               json=ultra_simple_config,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                task_id = result.get('task_id')
                print(f"✅ 超简单测试任务创建成功: {task_id}")
                
                # 快速等待数据准备
                print("⏳ 等待数据准备...")
                
                for i in range(30):  # 最多等待30秒
                    progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                    
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        if progress_data.get('success'):
                            progress = progress_data.get('progress', {})
                            status = progress.get('status', 'unknown')
                            
                            print(f"   [{i+1:2d}s] 状态: {status}")
                            
                            if status == 'data_ready':
                                print(f"   ✅ 数据准备完成！立即启动训练...")
                                
                                # 立即启动训练
                                train_response = session.post(f'http://127.0.0.1:5000/api/deep-learning/start-model-training/{task_id}')
                                
                                if train_response.status_code == 200:
                                    train_result = train_response.json()
                                    if train_result.get('success'):
                                        print(f"   ✅ 训练启动成功！")
                                        
                                        # 监控前20秒
                                        return monitor_ultra_simple_training(session, task_id)
                                    else:
                                        print(f"   ❌ 训练启动失败: {train_result.get('error')}")
                                        return False
                                else:
                                    print(f"   ❌ 训练启动请求失败: {train_response.status_code}")
                                    return False
                                    
                            elif status == 'failed':
                                print(f"   ❌ 数据准备失败")
                                return False
                            
                            time.sleep(1)
                        else:
                            print(f"   ❌ 获取进度失败")
                            return False
                    else:
                        print(f"   ❌ 进度请求失败")
                        return False
                
                print(f"⏰ 数据准备超时")
                return False
                
            else:
                print(f"❌ 任务创建失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_ultra_simple_training(session, task_id):
    """监控超简单训练"""
    print('\n🔍 监控超简单训练（前20秒）')
    print('=' * 60)
    
    start_time = time.time()
    last_progress = 0
    progress_updates = 0
    
    for i in range(20):  # 监控20秒
        try:
            progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
            
            if progress_response.status_code == 200:
                progress_data = progress_response.json()
                if progress_data.get('success'):
                    progress = progress_data.get('progress', {})
                    status = progress.get('status', 'unknown')
                    progress_percent = progress.get('progress', 0)
                    current_epoch = progress.get('epoch', 0)
                    
                    print(f"[{i+1:2d}s] 进度: {progress_percent:5.1f}%, 轮次: {current_epoch}, 状态: {status}")
                    
                    # 检查进度更新
                    if progress_percent != last_progress:
                        progress_updates += 1
                        print(f"      ✅ 进度更新 #{progress_updates}")
                        
                        # 如果突破25%，说明修复成功
                        if progress_percent > 25:
                            print(f"      🎉 成功突破25%！当前进度: {progress_percent}%")
                            return True
                    
                    # 检查是否仍卡在25%
                    if progress_percent == 25.0 and i > 10:
                        print(f"      ❌ 仍然卡在25%")
                        return False
                    
                    # 检查训练完成
                    if status == 'completed':
                        print(f"      🎉 训练完成！")
                        return True
                    elif status == 'failed':
                        print(f"      ❌ 训练失败")
                        return False
                    
                    last_progress = progress_percent
                    time.sleep(1)
                else:
                    print(f"   ❌ 获取进度失败")
                    return False
            else:
                print(f"   ❌ 进度请求失败")
                return False
                
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            return False
    
    print(f"📊 监控完成，总进度更新: {progress_updates} 次")
    return progress_updates > 0

def main():
    """主函数"""
    print('🔧 修复暂停死锁问题')
    print('=' * 80)
    
    print("🎯 发现问题: 训练控制中的while循环可能导致死锁")
    print("🔧 已修复: 添加了超时保护机制")
    print("🧪 现在测试修复效果...")
    
    # 清理训练控制状态
    clear_training_control_states()
    
    # 等待几秒
    print("\n⏳ 等待3秒...")
    time.sleep(3)
    
    # 测试训练
    success = test_immediate_training()
    
    print(f"\n🎯 修复结果:")
    if success:
        print(f"✅ 暂停死锁修复成功！")
        print(f"💡 关键改进:")
        print(f"   - 添加了暂停检查超时保护")
        print(f"   - 防止while循环死锁")
        print(f"   - 自动清除异常暂停状态")
        print(f"   - 训练可以正常进行")
    else:
        print(f"❌ 仍有问题，需要重启Flask应用")
        print(f"🚨 紧急措施:")
        print(f"   1. Ctrl+C 停止Flask应用")
        print(f"   2. python app.py 重新启动")
        print(f"   3. 使用超简单配置重新测试")

if __name__ == "__main__":
    main()
