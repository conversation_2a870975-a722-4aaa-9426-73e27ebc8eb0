#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最新训练的增强特征模型
"""

import torch
import os
import sqlite3
import json

def test_latest_model():
    """测试最新的增强特征模型"""
    
    print("🧪 测试最新训练的增强特征模型")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找最新的模型
        cursor.execute('''
            SELECT id, name, model_path, config, status, created_at
            FROM deep_learning_models
            WHERE status = 'completed'
            ORDER BY created_at DESC
            LIMIT 3
        ''')
        
        results = cursor.fetchall()
        if not results:
            print("❌ 没有找到已完成的模型")
            return False
        
        print(f"✅ 找到 {len(results)} 个已完成的模型:")
        
        for i, (model_id, name, model_path, config_str, status, created_at) in enumerate(results):
            print(f"\n📊 模型 {i+1}:")
            print(f"   ID: {model_id}")
            print(f"   名称: {name}")
            print(f"   路径: {model_path}")
            print(f"   状态: {status}")
            print(f"   创建时间: {created_at}")
            
            # 解析配置
            if config_str:
                try:
                    config = json.loads(config_str)
                    print(f"   模型类型: {config.get('model_type', 'unknown')}")
                    print(f"   使用增强特征: {config.get('use_enhanced_features', False)}")
                    print(f"   隐藏层大小: {config.get('hidden_size', 'unknown')}")
                    print(f"   层数: {config.get('num_layers', 'unknown')}")
                except:
                    print(f"   配置解析失败")
            
            # 分析模型文件
            if model_path and os.path.exists(model_path):
                analyze_result = analyze_model_file(model_path)
                if analyze_result:
                    print(f"   实际模型类型: {analyze_result['model_type']}")
                    print(f"   实际特征数量: {analyze_result['feature_count']}")
                    
                    # 测试配置推断
                    from services.deep_learning_service import DeepLearningService
                    dl_service = DeepLearningService()
                    inferred_config = dl_service._infer_model_config_from_weights(analyze_result['state_dict'])
                    
                    print(f"   推断配置: {inferred_config}")
                    
                    # 检查是否是增强特征模型
                    if analyze_result['feature_count'] > 8:
                        print(f"   ✅ 这是增强特征模型")
                    else:
                        print(f"   📊 这是基础特征模型")
            else:
                print(f"   ❌ 模型文件不存在")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def analyze_model_file(model_path):
    """分析模型文件"""
    try:
        # 加载状态字典
        state_dict = torch.load(model_path, map_location='cpu')
        
        # 如果是包含状态字典的字典
        if isinstance(state_dict, dict) and 'state_dict' in state_dict:
            actual_state_dict = state_dict['state_dict']
        else:
            actual_state_dict = state_dict
        
        # 分析模型类型和特征数量
        model_type = None
        feature_count = None
        
        if 'conv1.weight' in actual_state_dict:
            model_type = 'CNN-LSTM'
            conv_shape = actual_state_dict['conv1.weight'].shape
            feature_count = conv_shape[1]  # 输入通道数
            
        elif 'lstm.weight_ih_l0' in actual_state_dict:
            model_type = 'LSTM'
            lstm_shape = actual_state_dict['lstm.weight_ih_l0'].shape
            feature_count = lstm_shape[1]
            
        elif 'gru.weight_ih_l0' in actual_state_dict:
            model_type = 'GRU'
            gru_shape = actual_state_dict['gru.weight_ih_l0'].shape
            feature_count = gru_shape[1]
        
        return {
            'model_type': model_type,
            'feature_count': feature_count,
            'state_dict': actual_state_dict,
            'all_keys': list(actual_state_dict.keys())
        }
        
    except Exception as e:
        print(f"❌ 分析模型文件失败: {e}")
        return None

def main():
    """主函数"""
    
    print("🚀 测试最新训练的模型")
    print("=" * 80)
    
    if test_latest_model():
        print(f"\n✅ 测试完成!")
        print(f"💡 现在可以选择合适的模型进行回测")
    else:
        print(f"\n❌ 测试失败")

if __name__ == "__main__":
    main()
