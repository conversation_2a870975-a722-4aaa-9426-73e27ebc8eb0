#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启用手动交易按钮的解决方案
"""

print("🔧 手动交易按钮不能点击的解决方案")
print("=" * 80)

print("\n📋 问题诊断结果:")
print("✅ MT5连接: 正常")
print("✅ 市场数据: 正常")
print("❌ 交易模型: 未选择 ← 这是问题所在")

print("\n🎯 解决方案:")

print("\n方案1: 选择交易模型并启动AI交易（推荐）")
print("=" * 50)
print("1. 在AI推理交易页面找到'交易模型选择'区域")
print("2. 从下拉菜单中选择一个训练完成的模型")
print("3. 点击'启动AI交易'按钮")
print("4. 等待状态显示为'AI交易活跃'")
print("5. 手动交易按钮会自动启用")

print("\n方案2: 临时强制启用按钮（不推荐）")
print("=" * 50)
print("如果您只想测试手动交易功能，可以临时强制启用按钮：")
print("\n1. 按F12打开浏览器开发者工具")
print("2. 切换到'控制台(Console)'标签")
print("3. 粘贴以下代码并按回车：")

print("\n" + "="*40)
print("// 临时强制启用手动交易按钮")
force_enable_code = '''
console.log('🔧 强制启用手动交易按钮...');

const buyBtn = document.getElementById('manualBuyBtn');
const sellBtn = document.getElementById('manualSellBtn');

if (buyBtn && sellBtn) {
    // 强制启用按钮
    buyBtn.disabled = false;
    buyBtn.innerHTML = '<i class="fas fa-arrow-up me-1"></i>买入 (BUY)';
    buyBtn.className = 'btn btn-success w-100';
    
    sellBtn.disabled = false;
    sellBtn.innerHTML = '<i class="fas fa-arrow-down me-1"></i>卖出 (SELL)';
    sellBtn.className = 'btn btn-danger w-100';
    
    console.log('✅ 手动交易按钮已强制启用');
    console.log('⚠️ 注意: 这是临时启用，建议选择交易模型以获得完整功能');
    
    // 临时设置必要的变量
    window.selectedTradingModel = {
        symbol: 'XAUUSD',
        name: '临时模型',
        id: 'temp'
    };
    
    console.log('✅ 已设置临时交易模型');
    
} else {
    console.error('❌ 找不到手动交易按钮');
}
'''

print(force_enable_code)
print("="*40)

print("\n⚠️ 重要提醒:")
print("• 方案2只是临时解决方案")
print("• 建议使用方案1选择真实的交易模型")
print("• 这样可以获得完整的AI交易功能")

print("\n🚀 推荐步骤:")
print("1. 选择一个训练完成的AI模型")
print("2. 启动AI交易")
print("3. 手动交易按钮会自动启用")
print("4. 享受完整的AI交易功能")

print("\n💡 如果没有可用的交易模型:")
print("1. 前往'模型训练'页面")
print("2. 训练一个新的AI模型")
print("3. 等待训练完成")
print("4. 返回AI推理交易页面使用")

print("\n📊 按钮启用条件总结:")
print("✅ MT5连接正常")
print("✅ 市场数据正常")
print("❌ 需要选择交易模型 ← 请完成这一步")

print("\n🎉 完成上述步骤后，手动交易按钮就可以正常点击了！")
