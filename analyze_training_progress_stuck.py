#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析模型训练进度不动的原因
深度诊断训练过程中的潜在问题
"""

import sqlite3
import json
import time
import requests
from datetime import datetime

def check_current_training_status():
    """检查当前训练状态"""
    print('🔍 检查当前训练状态')
    print('=' * 60)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查找当前running状态的任务
        cursor.execute('''
            SELECT id, model_name, status, progress, current_epoch, total_epochs, 
                   created_at, updated_at, logs
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        running_tasks = cursor.fetchall()
        
        if running_tasks:
            print(f"📊 找到 {len(running_tasks)} 个运行中的任务:")
            
            for task in running_tasks:
                task_id, model_name, status, progress, current_epoch, total_epochs, created_at, updated_at, logs = task
                
                print(f"\n🎯 任务: {model_name} (ID: {task_id[:8]}...)")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   轮次: {current_epoch}/{total_epochs}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                
                # 计算停滞时间
                if updated_at:
                    try:
                        update_time = datetime.fromisoformat(updated_at)
                        now = datetime.now()
                        stuck_duration = (now - update_time).total_seconds()
                        print(f"   停滞时间: {stuck_duration:.1f} 秒")
                        
                        if stuck_duration > 300:  # 5分钟
                            print(f"   ⚠️ 可能卡住: 超过5分钟无更新")
                        elif stuck_duration > 60:  # 1分钟
                            print(f"   ⚠️ 进度缓慢: 超过1分钟无更新")
                        else:
                            print(f"   ✅ 正常运行")
                    except:
                        print(f"   ❌ 时间解析失败")
                
                # 分析日志
                if logs:
                    try:
                        log_data = json.loads(logs) if isinstance(logs, str) else logs
                        stage = log_data.get('stage', 'unknown')
                        message = log_data.get('message', 'N/A')
                        print(f"   日志阶段: {stage}")
                        print(f"   日志消息: {message}")
                    except:
                        print(f"   ❌ 日志解析失败")
                
                return task_id, progress, current_epoch, total_epochs, stuck_duration if 'stuck_duration' in locals() else 0
        else:
            print("✅ 没有运行中的任务")
            return None, None, None, None, 0
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查训练状态失败: {e}")
        return None, None, None, None, 0

def analyze_potential_causes():
    """分析潜在原因"""
    print('\n🔍 分析训练进度不动的潜在原因')
    print('=' * 60)
    
    potential_causes = [
        {
            'category': '数据加载问题',
            'causes': [
                'DataLoader死锁（多线程问题）',
                '数据预处理耗时过长',
                '内存不足导致数据加载缓慢',
                '磁盘I/O瓶颈',
                '数据格式错误导致无限循环'
            ],
            'solutions': [
                '设置num_workers=0强制单线程',
                '优化数据预处理逻辑',
                '监控内存使用情况',
                '检查磁盘空间和性能',
                '验证数据格式和完整性'
            ]
        },
        {
            'category': '模型计算问题',
            'causes': [
                'GPU内存不足导致计算卡住',
                '模型结构过于复杂',
                '梯度计算异常（NaN/Inf）',
                '优化器参数不当',
                '损失函数计算错误'
            ],
            'solutions': [
                '减小batch_size或使用CPU',
                '简化模型结构',
                '添加梯度裁剪和NaN检查',
                '调整学习率和优化器参数',
                '验证损失函数实现'
            ]
        },
        {
            'category': '训练循环问题',
            'causes': [
                '无限循环或死循环',
                '异常处理不当导致卡住',
                '进度更新逻辑错误',
                '验证阶段卡住',
                '早停逻辑异常'
            ],
            'solutions': [
                '添加循环超时保护',
                '完善异常处理机制',
                '修复进度更新逻辑',
                '优化验证阶段代码',
                '检查早停条件'
            ]
        },
        {
            'category': '系统资源问题',
            'causes': [
                'CPU使用率过高',
                'GPU资源竞争',
                '内存泄漏',
                '磁盘空间不足',
                '网络连接问题'
            ],
            'solutions': [
                '监控和优化CPU使用',
                '确保GPU独占使用',
                '检查内存泄漏',
                '清理磁盘空间',
                '检查网络连接稳定性'
            ]
        },
        {
            'category': '代码逻辑问题',
            'causes': [
                '特征计算耗时过长',
                '数据库操作阻塞',
                '日志写入频繁',
                '同步操作等待',
                '资源锁竞争'
            ],
            'solutions': [
                '优化特征计算算法',
                '异步数据库操作',
                '减少日志频率',
                '改为异步操作',
                '优化资源锁使用'
            ]
        }
    ]
    
    print("📋 潜在原因分析:")
    for i, category in enumerate(potential_causes, 1):
        print(f"\n{i}. {category['category']}:")
        print("   可能原因:")
        for cause in category['causes']:
            print(f"     - {cause}")
        print("   解决方案:")
        for solution in category['solutions']:
            print(f"     - {solution}")

def create_diagnostic_script():
    """创建诊断脚本"""
    print('\n🔧 创建训练诊断脚本')
    print('=' * 60)
    
    diagnostic_code = '''
import psutil
import torch
import time
import threading
from datetime import datetime

def monitor_training_resources():
    """监控训练资源使用"""
    print("🔍 开始监控训练资源...")
    
    while True:
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available / (1024**3)  # GB
            
            # GPU使用（如果可用）
            gpu_info = "N/A"
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.memory_allocated() / (1024**3)  # GB
                gpu_memory_max = torch.cuda.max_memory_allocated() / (1024**3)  # GB
                gpu_info = f"{gpu_memory:.2f}GB / {gpu_memory_max:.2f}GB"
            
            # 磁盘使用
            disk = psutil.disk_usage('.')
            disk_free = disk.free / (1024**3)  # GB
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] "
                  f"CPU: {cpu_percent:5.1f}% | "
                  f"内存: {memory_percent:5.1f}% ({memory_available:.1f}GB可用) | "
                  f"GPU: {gpu_info} | "
                  f"磁盘: {disk_free:.1f}GB可用")
            
            # 检查异常情况
            if cpu_percent > 95:
                print("⚠️ CPU使用率过高!")
            if memory_percent > 90:
                print("⚠️ 内存使用率过高!")
            if disk_free < 1:
                print("⚠️ 磁盘空间不足!")
            
            time.sleep(5)  # 每5秒监控一次
            
        except KeyboardInterrupt:
            print("监控停止")
            break
        except Exception as e:
            print(f"监控异常: {e}")
            time.sleep(5)

if __name__ == "__main__":
    monitor_training_resources()
'''
    
    # 保存诊断脚本
    with open('monitor_training_resources.py', 'w', encoding='utf-8') as f:
        f.write(diagnostic_code)
    
    print("✅ 已创建训练资源监控脚本: monitor_training_resources.py")
    print("💡 使用方法: python monitor_training_resources.py")

def provide_immediate_solutions():
    """提供立即解决方案"""
    print('\n🚀 立即解决方案')
    print('=' * 60)
    
    solutions = [
        {
            'priority': 'critical',
            'title': '强制重启训练（保持高要求）',
            'steps': [
                '1. 停止当前卡住的训练任务',
                '2. 检查并清理系统资源',
                '3. 使用相同配置重新启动训练',
                '4. 添加详细监控和日志'
            ],
            'code': '''
# 停止卡住的任务
UPDATE training_tasks SET status = 'stopped' WHERE status = 'running';

# 重新启动训练（保持原配置）
- 使用相同的模型配置
- 保持相同的数据集大小
- 维持相同的训练参数
- 添加进度监控增强
'''
        },
        {
            'priority': 'high',
            'title': '优化训练稳定性（不降低要求）',
            'steps': [
                '1. 添加训练循环超时保护',
                '2. 强化进度更新机制',
                '3. 优化数据加载策略',
                '4. 增强异常处理'
            ],
            'code': '''
# 训练循环超时保护
max_batch_time = 300  # 5分钟超时
batch_start_time = time.time()

for batch_idx, (batch_X, batch_y) in enumerate(train_loader):
    if time.time() - batch_start_time > max_batch_time:
        logger.warning("批次处理超时，跳过")
        break
    
    # 训练逻辑...
    batch_start_time = time.time()
'''
        },
        {
            'priority': 'medium',
            'title': '增强监控和诊断',
            'steps': [
                '1. 启动资源监控脚本',
                '2. 添加详细的训练日志',
                '3. 实时监控进度更新',
                '4. 设置异常告警'
            ],
            'code': '''
# 启动监控
python monitor_training_resources.py &

# 检查训练进度
python check_training_progress.py

# 实时日志监控
tail -f training.log
'''
        }
    ]
    
    print("🎯 按优先级排序的解决方案:")
    for solution in solutions:
        priority_icon = '🔴' if solution['priority'] == 'critical' else '🟡' if solution['priority'] == 'high' else '🟢'
        print(f"\n{priority_icon} {solution['title']} ({solution['priority'].upper()})")
        print("   步骤:")
        for step in solution['steps']:
            print(f"     {step}")
        print("   代码示例:")
        print(f"   {solution['code']}")

def create_training_recovery_plan():
    """创建训练恢复计划"""
    print('\n📋 训练恢复计划（保持高要求）')
    print('=' * 60)
    
    recovery_plan = """
🎯 训练恢复计划 - 不降低要求版本

阶段1: 立即诊断 (5分钟)
├── 检查当前训练状态和卡住位置
├── 监控系统资源使用情况
├── 分析训练日志和错误信息
└── 确定具体卡住原因

阶段2: 紧急修复 (10分钟)
├── 停止卡住的训练任务
├── 清理系统资源和缓存
├── 修复发现的具体问题
└── 优化训练稳定性代码

阶段3: 重启训练 (立即)
├── 使用原始高要求配置
├── 添加增强监控和保护
├── 启动新的训练任务
└── 实时监控训练进度

阶段4: 持续监控 (训练期间)
├── 资源使用监控
├── 进度更新监控
├── 异常检测和告警
└── 性能优化调整

🔧 关键原则:
- ✅ 保持原始训练要求不变
- ✅ 只优化稳定性和监控
- ✅ 不降低模型复杂度
- ✅ 不减少训练数据
- ✅ 不降低训练轮次
"""
    
    print(recovery_plan)

def main():
    """主函数"""
    print('🔧 模型训练进度不动问题分析')
    print('=' * 80)
    
    print("🎯 分析目标:")
    print("   找出训练进度不动的具体原因")
    print("   提供不降低要求的解决方案")
    print("   确保训练质量和稳定性")
    
    # 检查当前训练状态
    task_id, progress, current_epoch, total_epochs, stuck_duration = check_current_training_status()
    
    # 分析潜在原因
    analyze_potential_causes()
    
    # 创建诊断脚本
    create_diagnostic_script()
    
    # 提供解决方案
    provide_immediate_solutions()
    
    # 创建恢复计划
    create_training_recovery_plan()
    
    print(f"\n🎯 分析结果")
    print('=' * 80)
    
    if task_id:
        print(f"📊 发现卡住的训练任务:")
        print(f"   任务ID: {task_id[:8]}...")
        print(f"   当前进度: {progress}%")
        print(f"   当前轮次: {current_epoch}/{total_epochs}")
        print(f"   卡住时间: {stuck_duration:.1f} 秒")
        
        if stuck_duration > 300:
            print(f"🔴 严重卡住: 建议立即重启训练")
        elif stuck_duration > 60:
            print(f"🟡 轻微卡住: 建议监控或重启")
        else:
            print(f"🟢 可能正常: 继续观察")
    else:
        print(f"✅ 没有发现卡住的训练任务")
    
    print(f"\n🚀 立即行动建议:")
    print(f"1. 🔍 运行资源监控: python monitor_training_resources.py")
    print(f"2. 📊 检查训练状态: 观察进度是否有更新")
    print(f"3. 🔧 如果确认卡住: 停止并重启训练（保持原配置）")
    print(f"4. 📈 持续监控: 确保训练稳定进行")

if __name__ == "__main__":
    main()
