#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试增强特征配置传递
"""

import requests
import time
import json

def test_enhanced_features_simple():
    """简单测试增强特征配置"""
    print("🧪 简单测试增强特征配置传递")
    print("=" * 50)
    
    # 等待应用启动
    time.sleep(5)
    
    try:
        # 创建会话并登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 获取可用模型
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        models_data = models_response.json()
        
        test_model = None
        for model in models_data.get('models', []):
            if model.get('status') == 'completed':
                test_model = model
                break
        
        if not test_model:
            print(f"❌ 没有可用模型")
            return False
        
        print(f"✅ 选择测试模型: {test_model['name']}")
        
        # 测试增强特征回测请求
        print(f"\n🚀 发送增强特征回测请求...")
        
        backtest_data = {
            'model_id': test_model['id'],
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'start_date': '2025-08-01',
            'end_date': '2025-08-02',
            'initial_balance': 10000,
            'lot_size': 0.01,
            'stop_loss_pips': 1000,
            'take_profit_pips': 2000,
            'min_confidence': 0.3,
            'cliff_brake_enabled': False,
            'trailing_stop_enabled': False,
            # 增强特征配置
            'use_enhanced_features': True,
            'feature_selection_strategy': 'recommended',
            'analyze_feature_importance': True,
            'selected_features': ['bb_percent_b', 'atr_atr', 'stoch_stoch_k']
        }
        
        print(f"📋 请求数据:")
        print(f"   模型ID: {backtest_data['model_id'][:8]}...")
        print(f"   使用增强特征: {backtest_data['use_enhanced_features']}")
        print(f"   特征策略: {backtest_data['feature_selection_strategy']}")
        print(f"   分析重要性: {backtest_data['analyze_feature_importance']}")
        print(f"   自定义特征: {backtest_data['selected_features']}")
        
        response = session.post('http://127.0.0.1:5000/api/deep-learning/inference-backtest', 
                               json=backtest_data)
        
        print(f"\n📡 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应数据:")
            print(f"   成功: {result.get('success')}")
            
            if result.get('success'):
                trades = result.get('trades', [])
                stats = result.get('statistics', {})
                
                print(f"   交易数量: {len(trades)}")
                print(f"   统计数据: {len(stats)} 个指标")
                
                # 检查第一笔交易是否有增强特征标记
                if trades:
                    first_trade = trades[0]
                    print(f"\n📊 第一笔交易数据:")
                    for key, value in first_trade.items():
                        print(f"      {key}: {value}")
                    
                    # 检查增强特征标记
                    if first_trade.get('enhanced_features_used'):
                        print(f"   ✅ 发现增强特征标记!")
                    else:
                        print(f"   ❌ 未发现增强特征标记")
                
                return True
            else:
                print(f"   错误: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误详情: {error_data}")
            except:
                print(f"   响应内容: {response.text[:200]}...")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_enhanced_features_simple()
    
    if success:
        print("\n🎉 增强特征配置传递测试成功！")
    else:
        print("\n❌ 增强特征配置传递测试失败")
