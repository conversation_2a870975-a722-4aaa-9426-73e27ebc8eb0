#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查现有模型的特征配置
"""

import sqlite3
import json
import os
import torch
import numpy as np

def check_existing_models():
    """检查现有模型的特征配置"""
    print("🔍 检查现有模型的特征配置")
    print("=" * 50)
    
    try:
        # 连接数据库
        db_path = 'trading_system.db'
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询所有模型
        cursor.execute("""
            SELECT id, name, model_type, symbol, timeframe, config, status, model_path
            FROM deep_learning_models 
            ORDER BY created_at DESC
        """)
        
        models = cursor.fetchall()
        
        if not models:
            print("❌ 数据库中没有找到任何模型")
            return
        
        print(f"✅ 找到 {len(models)} 个模型")
        
        for i, model in enumerate(models):
            model_id, name, model_type, symbol, timeframe, config_str, status, model_path = model
            
            print(f"\n📊 模型 {i+1}: {name}")
            print(f"   ID: {model_id}")
            print(f"   类型: {model_type}")
            print(f"   品种: {symbol}")
            print(f"   时间框架: {timeframe}")
            print(f"   状态: {status}")
            print(f"   模型路径: {model_path}")
            
            # 解析配置
            if config_str:
                try:
                    config = json.loads(config_str)
                    print(f"   📋 配置信息:")
                    print(f"      序列长度: {config.get('sequence_length', 'N/A')}")
                    print(f"      隐藏层大小: {config.get('hidden_size', 'N/A')}")
                    print(f"      特征配置: {config.get('features', 'N/A')}")
                    print(f"      使用增强特征: {config.get('use_enhanced_features', False)}")
                    
                    # 检查模型文件
                    if model_path and os.path.exists(model_path):
                        print(f"   🔍 分析模型文件...")
                        analyze_model_file(model_path, config)
                    else:
                        print(f"   ❌ 模型文件不存在: {model_path}")
                        
                except Exception as e:
                    print(f"   ❌ 配置解析失败: {e}")
            else:
                print(f"   ⚠️ 没有配置信息")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def analyze_model_file(model_path, config):
    """分析模型文件的特征配置"""
    try:
        # 加载模型状态字典
        state_dict = torch.load(model_path, map_location='cpu')
        
        # 如果是包含状态字典的字典
        if isinstance(state_dict, dict) and 'state_dict' in state_dict:
            actual_state_dict = state_dict['state_dict']
        else:
            actual_state_dict = state_dict
        
        print(f"      模型权重键: {list(actual_state_dict.keys())[:5]}...")
        
        # 推断特征数量
        model_type = config.get('model_type', 'lstm').lower()
        feature_count = None
        
        if model_type == 'cnn_lstm' and 'conv1.weight' in actual_state_dict:
            # CNN-LSTM模型
            conv_weight_shape = actual_state_dict['conv1.weight'].shape
            feature_count = conv_weight_shape[1]
            print(f"      模型类型: CNN-LSTM")
            print(f"      conv1.weight形状: {conv_weight_shape}")
            print(f"      推断特征数量: {feature_count}")
            
        elif 'lstm.weight_ih_l0' in actual_state_dict:
            # LSTM模型
            lstm_weight_shape = actual_state_dict['lstm.weight_ih_l0'].shape
            feature_count = lstm_weight_shape[1]
            print(f"      模型类型: LSTM")
            print(f"      lstm.weight_ih_l0形状: {lstm_weight_shape}")
            print(f"      推断特征数量: {feature_count}")
            
        elif 'gru.weight_ih_l0' in actual_state_dict:
            # GRU模型
            gru_weight_shape = actual_state_dict['gru.weight_ih_l0'].shape
            feature_count = gru_weight_shape[1]
            print(f"      模型类型: GRU")
            print(f"      gru.weight_ih_l0形状: {gru_weight_shape}")
            print(f"      推断特征数量: {feature_count}")
        
        # 分析特征数量与配置的关系
        if feature_count:
            print(f"      🔍 特征数量分析:")
            
            # 基础特征数量（OHLCV）
            basic_features = 5
            
            # 增强特征数量（推荐特征集）
            enhanced_features_count = 26  # 根据推荐特征集
            
            if feature_count == basic_features:
                print(f"         ✅ 使用基础特征 (OHLCV): {feature_count} 个")
                print(f"         📋 训练时未使用增强特征")
            elif feature_count == enhanced_features_count:
                print(f"         ✅ 使用推荐增强特征: {feature_count} 个")
                print(f"         📋 训练时使用了增强特征")
            elif feature_count > enhanced_features_count:
                print(f"         ✅ 使用全部增强特征: {feature_count} 个")
                print(f"         📋 训练时使用了完整的增强特征集")
            else:
                print(f"         ⚠️ 自定义特征数量: {feature_count} 个")
                print(f"         📋 训练时使用了自定义特征配置")
            
            # 检查配置一致性
            config_enhanced = config.get('use_enhanced_features', False)
            if config_enhanced and feature_count > basic_features:
                print(f"         ✅ 配置与模型一致: 都使用增强特征")
            elif not config_enhanced and feature_count == basic_features:
                print(f"         ✅ 配置与模型一致: 都使用基础特征")
            else:
                print(f"         ⚠️ 配置与模型不一致:")
                print(f"            配置增强特征: {config_enhanced}")
                print(f"            模型特征数量: {feature_count}")
        
    except Exception as e:
        print(f"      ❌ 模型文件分析失败: {e}")

def test_enhanced_features_compatibility():
    """测试增强特征兼容性"""
    print(f"\n🧪 测试增强特征兼容性")
    print("=" * 50)
    
    try:
        # 导入增强特征工程
        from services.enhanced_feature_engineering import EnhancedFeatureEngineering
        
        # 创建测试数据
        test_data = np.random.rand(100, 5)  # 100个时间点，5个特征(OHLCV)
        
        # 创建特征工程实例
        feature_engineer = EnhancedFeatureEngineering()
        
        # 计算增强特征
        features_dict = feature_engineer.calculate_all_enhanced_features(test_data)
        
        print(f"✅ 增强特征计算成功")
        print(f"   生成特征数量: {len(features_dict)}")
        print(f"   特征名称: {list(features_dict.keys())[:10]}...")
        
        # 获取推荐特征
        recommended_features = feature_engineer.get_recommended_features()
        print(f"   推荐特征数量: {len(recommended_features)}")
        print(f"   推荐特征: {recommended_features[:5]}...")
        
        # 构建特征矩阵
        feature_matrix = feature_engineer.build_feature_matrix(features_dict, recommended_features)
        print(f"   特征矩阵形状: {feature_matrix.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强特征兼容性测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🔍 模型特征配置检查工具")
    print("=" * 80)
    
    # 1. 检查现有模型
    check_existing_models()
    
    # 2. 测试增强特征兼容性
    test_enhanced_features_compatibility()
    
    print(f"\n📋 总结:")
    print(f"   1. 检查了数据库中的所有模型")
    print(f"   2. 分析了模型文件的特征配置")
    print(f"   3. 验证了增强特征的兼容性")
    print(f"   4. 确认了训练时的特征使用情况")

if __name__ == "__main__":
    main()
