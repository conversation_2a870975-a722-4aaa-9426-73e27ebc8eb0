#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试模型训练状态问题
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_training_tasks():
    """检查训练任务状态"""
    print("🔍 检查训练任务状态")
    print("=" * 50)
    
    db_path = 'trading_system.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='training_tasks'
        """)
        
        if not cursor.fetchone():
            print("❌ training_tasks表不存在")
            conn.close()
            return False
        
        print("✅ training_tasks表存在")
        
        # 检查所有任务
        cursor.execute("""
            SELECT id, model_id, status, progress, current_epoch, total_epochs, 
                   logs, created_at, updated_at
            FROM training_tasks 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        tasks = cursor.fetchall()
        
        print(f"📊 总任务数: {len(tasks)}")
        
        if tasks:
            print("\n📋 最近的训练任务:")
            for task in tasks:
                task_id, model_id, status, progress, current_epoch, total_epochs, logs, created_at, updated_at = task
                
                print(f"\n🔹 任务ID: {task_id[:8]}...")
                print(f"   模型ID: {model_id[:8]}..." if model_id else "   模型ID: None")
                print(f"   状态: {status}")
                print(f"   进度: {progress}%")
                print(f"   轮次: {current_epoch}/{total_epochs}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                
                if logs:
                    try:
                        log_data = json.loads(logs)
                        print(f"   日志阶段: {log_data.get('stage', 'unknown')}")
                        print(f"   日志消息: {log_data.get('message', 'no message')}")
                    except:
                        print(f"   日志: {logs[:100]}...")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查任务失败: {e}")
        return False

def check_models_status():
    """检查模型状态"""
    print("\n🔍 检查模型状态")
    print("=" * 50)
    
    db_path = 'trading_system.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查模型表
        cursor.execute("""
            SELECT id, name, status, created_at
            FROM deep_learning_models 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        models = cursor.fetchall()
        
        print(f"📊 模型数量: {len(models)}")
        
        if models:
            print("\n📋 最近的模型:")
            for model in models:
                model_id, name, status, created_at = model
                print(f"🔹 {name} ({model_id[:8]}...) - {status} - {created_at}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查模型失败: {e}")
        return False

def fix_stuck_tasks():
    """修复卡住的任务"""
    print("\n🔧 修复卡住的任务")
    print("=" * 50)
    
    db_path = 'trading_system.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查找可能卡住的任务
        cursor.execute("""
            SELECT id, model_id, status, progress, logs
            FROM training_tasks 
            WHERE status IN ('running', 'data_preparation')
            ORDER BY created_at DESC
        """)
        
        stuck_tasks = cursor.fetchall()
        
        if not stuck_tasks:
            print("✅ 没有发现卡住的任务")
            conn.close()
            return True
        
        print(f"⚠️ 发现 {len(stuck_tasks)} 个可能卡住的任务")
        
        fixed_count = 0
        for task in stuck_tasks:
            task_id, model_id, status, progress, logs = task
            
            print(f"\n🔹 处理任务: {task_id[:8]}...")
            print(f"   当前状态: {status}")
            print(f"   当前进度: {progress}%")
            
            # 分析日志确定应该设置的状态
            should_fix = False
            new_status = 'failed'
            new_logs = json.dumps({
                'stage': 'auto_fixed',
                'message': '自动修复卡住的任务',
                'original_status': status,
                'fixed_at': datetime.now().isoformat()
            })
            
            if logs:
                try:
                    log_data = json.loads(logs)
                    stage = log_data.get('stage', '')
                    
                    # 如果日志显示数据准备完成，设置为data_ready
                    if stage == 'data_ready' or 'data_ready' in log_data.get('message', ''):
                        new_status = 'data_ready'
                        new_logs = json.dumps({
                            'stage': 'data_ready',
                            'message': '数据准备完成，可以开始模型训练（自动修复）',
                            'fixed_at': datetime.now().isoformat()
                        })
                        should_fix = True
                        print(f"   → 修复为: data_ready")
                    elif progress >= 25:
                        # 如果进度>=25%，可能数据准备已完成
                        new_status = 'data_ready'
                        new_logs = json.dumps({
                            'stage': 'data_ready',
                            'message': '数据准备完成，可以开始模型训练（基于进度修复）',
                            'fixed_at': datetime.now().isoformat()
                        })
                        should_fix = True
                        print(f"   → 修复为: data_ready (基于进度)")
                    else:
                        should_fix = True
                        print(f"   → 标记为: failed")
                except:
                    should_fix = True
                    print(f"   → 标记为: failed (日志解析失败)")
            else:
                should_fix = True
                print(f"   → 标记为: failed (无日志)")
            
            if should_fix:
                # 更新任务状态
                cursor.execute('''
                    UPDATE training_tasks
                    SET status = ?, logs = ?, updated_at = ?
                    WHERE id = ?
                ''', (new_status, new_logs, datetime.now().isoformat(), task_id))
                
                # 如果设置为data_ready，也更新模型状态
                if new_status == 'data_ready' and model_id:
                    cursor.execute('''
                        UPDATE deep_learning_models
                        SET status = 'data_ready'
                        WHERE id = ?
                    ''', (model_id,))
                
                fixed_count += 1
                print(f"   ✅ 已修复")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ 修复完成，共修复 {fixed_count} 个任务")
        return True
        
    except Exception as e:
        print(f"❌ 修复任务失败: {e}")
        return False

def test_training_service():
    """测试训练服务"""
    print("\n🧪 测试训练服务")
    print("=" * 50)
    
    try:
        from services.deep_learning_service import DeepLearningService
        
        service = DeepLearningService()
        
        # 查找一个data_ready状态的任务
        db_path = 'trading_system.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, model_id FROM training_tasks 
            WHERE status = 'data_ready'
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        task = cursor.fetchone()
        conn.close()
        
        if task:
            task_id, model_id = task
            print(f"📋 找到data_ready任务: {task_id[:8]}...")
            
            # 测试获取训练进度
            progress = service.get_training_progress(task_id)
            if progress.get('success'):
                print("✅ 获取训练进度成功")
                print(f"   状态: {progress['progress']['status']}")
                print(f"   进度: {progress['progress']['progress']}%")
            else:
                print(f"❌ 获取训练进度失败: {progress.get('error')}")
                
            # 测试启动模型训练
            print(f"\n📋 测试启动模型训练...")
            result = service.start_model_training(task_id)
            if result.get('success'):
                print("✅ 启动模型训练成功")
                print(f"   消息: {result.get('message')}")
            else:
                print(f"❌ 启动模型训练失败: {result.get('error')}")
                
        else:
            print("ℹ️ 没有找到data_ready状态的任务进行测试")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试训练服务失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🔧 模型训练状态问题调试工具")
    print("=" * 80)
    
    print("📋 问题描述:")
    print("• 显示数据准备完成了，但是模型训练报错")
    print("• 错误：任务状态不正确: running，需要先完成数据准备")
    print()
    
    # 检查训练任务
    tasks_ok = check_training_tasks()
    
    # 检查模型状态
    models_ok = check_models_status()
    
    # 修复卡住的任务
    fix_ok = fix_stuck_tasks() if tasks_ok else False
    
    # 测试训练服务
    test_ok = test_training_service() if tasks_ok else False
    
    print("\n📊 调试总结")
    print("=" * 80)
    
    if tasks_ok:
        print("✅ 训练任务检查完成")
    else:
        print("❌ 训练任务检查失败")
    
    if models_ok:
        print("✅ 模型状态检查完成")
    else:
        print("❌ 模型状态检查失败")
    
    if fix_ok:
        print("✅ 任务修复完成")
    else:
        print("❌ 任务修复失败")
    
    if test_ok:
        print("✅ 训练服务测试完成")
    else:
        print("❌ 训练服务测试失败")
    
    print("\n💡 修复建议:")
    print("1. 重启应用程序以应用代码修复")
    print("2. 重新执行数据准备")
    print("3. 等待数据准备完成后再点击开始训练")
    print("4. 如果仍有问题，检查日志详细信息")

if __name__ == "__main__":
    main()
