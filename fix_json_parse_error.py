#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复JSON解析错误
检查和修复"[object Object]"问题
"""

import sqlite3
import json
from datetime import datetime

def analyze_json_parse_error():
    """分析JSON解析错误"""
    print('🔍 分析JSON解析错误')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询最近的任务日志
        cursor.execute('''
            SELECT id, model_id, status, logs
            FROM training_tasks 
            ORDER BY updated_at DESC
            LIMIT 10
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("❌ 没有找到任务")
            return
        
        print(f"📊 检查最近 {len(tasks)} 个任务的日志:")
        
        problematic_tasks = []
        
        for i, task in enumerate(tasks, 1):
            task_id, model_id, status, logs = task
            
            print(f"\n任务 {i}: {task_id[:8]}...")
            print(f"   状态: {status}")
            
            if logs:
                print(f"   日志类型: {type(logs)}")
                print(f"   日志长度: {len(str(logs))}")
                print(f"   日志前100字符: {str(logs)[:100]}...")
                
                # 检查是否是有效的JSON
                if isinstance(logs, str):
                    if logs == "[object Object]":
                        print(f"   ❌ 发现问题：日志是 '[object Object]'")
                        problematic_tasks.append(task_id)
                    elif logs.startswith("[object"):
                        print(f"   ❌ 发现问题：日志包含 '[object' 字符串")
                        problematic_tasks.append(task_id)
                    else:
                        try:
                            parsed = json.loads(logs)
                            print(f"   ✅ 日志JSON格式正确")
                            print(f"   日志内容: {list(parsed.keys()) if isinstance(parsed, dict) else 'not dict'}")
                        except json.JSONDecodeError as e:
                            print(f"   ❌ JSON解析失败: {e}")
                            problematic_tasks.append(task_id)
                else:
                    print(f"   ⚠️ 日志不是字符串类型: {type(logs)}")
                    if hasattr(logs, '__dict__'):
                        print(f"   对象属性: {logs.__dict__}")
            else:
                print(f"   ⚠️ 没有日志")
        
        if problematic_tasks:
            print(f"\n🚨 发现 {len(problematic_tasks)} 个有问题的任务:")
            for task_id in problematic_tasks:
                print(f"   - {task_id}")
        else:
            print(f"\n✅ 所有任务的日志格式都正确")
        
        return problematic_tasks
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return []

def check_backend_log_generation():
    """检查后端日志生成逻辑"""
    print('\n🔍 检查后端日志生成逻辑')
    print('=' * 50)
    
    try:
        # 检查深度学习服务中的日志生成
        with open('services/deep_learning_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("📋 检查日志生成相关代码:")
        
        # 查找可能导致[object Object]的代码
        issues = []
        
        if 'json.dumps(' in content:
            print("   ✅ 找到 json.dumps() 调用")
        else:
            issues.append("缺少 json.dumps() 调用")
        
        if 'logs=' in content:
            print("   ✅ 找到日志赋值")
        
        if '"logs":' in content:
            print("   ✅ 找到日志字段")
        
        # 查找可能的问题模式
        if 'str(' in content and 'logs' in content:
            issues.append("可能使用了 str() 转换对象")
        
        if issues:
            print(f"\n⚠️ 发现潜在问题:")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print(f"\n✅ 后端日志生成逻辑看起来正常")
            
    except Exception as e:
        print(f"❌ 检查后端代码失败: {e}")

def fix_problematic_logs(task_ids):
    """修复有问题的日志"""
    print(f'\n🔧 修复有问题的日志')
    print('=' * 50)
    
    if not task_ids:
        print("✅ 没有需要修复的日志")
        return True
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        for task_id in task_ids:
            print(f"🔧 修复任务: {task_id}")
            
            # 创建一个有效的默认日志
            default_log = {
                "stage": "unknown",
                "message": "日志已修复",
                "timestamp": datetime.now().isoformat(),
                "error": "原日志格式错误，已重置"
            }
            
            # 更新日志
            cursor.execute('''
                UPDATE training_tasks 
                SET logs = ?, updated_at = ?
                WHERE id = ?
            ''', (json.dumps(default_log), datetime.now().isoformat(), task_id))
            
            print(f"   ✅ 任务 {task_id[:8]}... 日志已修复")
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"\n✅ 成功修复 {len(task_ids)} 个任务的日志")
        return True
        
    except Exception as e:
        print(f"❌ 修复日志失败: {e}")
        return False

def add_frontend_error_handling():
    """添加前端错误处理建议"""
    print(f'\n🔧 前端错误处理建议')
    print('=' * 50)
    
    print("📋 建议的前端修复代码:")
    
    frontend_fix = '''
// 修复JSON解析错误的代码
function safeJsonParse(jsonString) {
    try {
        // 检查是否是 [object Object] 字符串
        if (jsonString === "[object Object]" || jsonString.startsWith("[object")) {
            console.warn("检测到无效的对象字符串，返回默认值");
            return {
                stage: "unknown",
                message: "日志格式错误",
                error: "原日志包含无效的对象字符串"
            };
        }
        
        // 检查是否已经是对象
        if (typeof jsonString === 'object') {
            return jsonString;
        }
        
        // 尝试解析JSON
        return JSON.parse(jsonString);
    } catch (error) {
        console.error("JSON解析失败:", error, "原始数据:", jsonString);
        return {
            stage: "error",
            message: "日志解析失败",
            error: error.message,
            originalData: jsonString
        };
    }
}

// 在updateTrainingProgress函数中使用
if (progress.logs) {
    try {
        const logs = safeJsonParse(progress.logs);  // 使用安全解析
        // 继续处理日志...
    } catch (error) {
        console.error("日志处理失败:", error);
        // 显示错误信息而不是崩溃
    }
}
'''
    
    print(frontend_fix)

def check_current_training_logs():
    """检查当前训练任务的日志"""
    print(f'\n🔍 检查当前训练任务日志')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('trading_system.db')
        cursor = conn.cursor()
        
        # 查询running状态的任务
        cursor.execute('''
            SELECT id, model_id, status, progress, logs
            FROM training_tasks 
            WHERE status = 'running'
            ORDER BY updated_at DESC
        ''')
        
        tasks = cursor.fetchall()
        conn.close()
        
        if not tasks:
            print("✅ 没有正在运行的任务")
            return True
        
        print(f"📊 检查 {len(tasks)} 个正在运行的任务:")
        
        all_good = True
        for task in tasks:
            task_id, model_id, status, progress, logs = task
            
            print(f"\n任务: {task_id[:8]}...")
            print(f"   状态: {status}")
            print(f"   进度: {progress}%")
            
            if logs:
                if isinstance(logs, str) and (logs == "[object Object]" or logs.startswith("[object")):
                    print(f"   ❌ 日志有问题: {logs}")
                    all_good = False
                else:
                    try:
                        if isinstance(logs, str):
                            parsed = json.loads(logs)
                        else:
                            parsed = logs
                        print(f"   ✅ 日志正常: {parsed.get('stage', 'unknown')}")
                    except:
                        print(f"   ❌ 日志解析失败")
                        all_good = False
            else:
                print(f"   ⚠️ 没有日志")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print('🔧 JSON解析错误修复')
    print('=' * 80)
    
    # 分析JSON解析错误
    problematic_tasks = analyze_json_parse_error()
    
    # 检查后端日志生成
    check_backend_log_generation()
    
    # 修复有问题的日志
    if problematic_tasks:
        fix_success = fix_problematic_logs(problematic_tasks)
    else:
        fix_success = True
    
    # 检查当前训练任务
    current_logs_ok = check_current_training_logs()
    
    # 提供前端错误处理建议
    add_frontend_error_handling()
    
    print(f"\n🎯 修复结果:")
    print(f"   问题任务修复: {'✅ 成功' if fix_success else '❌ 失败'}")
    print(f"   当前任务日志: {'✅ 正常' if current_logs_ok else '❌ 有问题'}")
    
    if fix_success and current_logs_ok:
        print(f"\n🎉 JSON解析错误修复完成！")
        print(f"💡 建议:")
        print(f"   1. 刷新浏览器页面")
        print(f"   2. 重新开始训练任务")
        print(f"   3. 观察是否还有JSON解析错误")
    else:
        print(f"\n❌ 仍有问题需要进一步处理")
        print(f"🔧 建议:")
        print(f"   1. 检查后端日志生成逻辑")
        print(f"   2. 添加前端错误处理")
        print(f"   3. 重启Flask应用")

if __name__ == "__main__":
    main()
