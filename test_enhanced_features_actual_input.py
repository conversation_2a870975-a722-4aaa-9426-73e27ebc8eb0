#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际测试增强特征是否输入到模型
通过API调用验证增强特征配置的完整数据流
"""

import requests
import json
import time

def test_enhanced_features_api_call():
    """测试增强特征API调用"""
    print('🧪 测试增强特征API调用')
    print('=' * 60)
    
    # 登录
    session = requests.Session()
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 获取可用模型
    try:
        models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
        
        if models_response.status_code == 200:
            models = models_response.json()
            if models:
                model = models[0]  # 使用第一个模型
                model_id = model['id']
                print(f"✅ 找到模型: {model['name']} (ID: {model_id[:8]}...)")
            else:
                print("❌ 没有可用模型")
                return False
        else:
            print(f"❌ 获取模型失败: {models_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取模型异常: {e}")
        return False
    
    # 测试不同的增强特征配置
    test_configs = [
        {
            'name': '基础配置（无增强特征）',
            'config': {
                'use_enhanced_features': False
            }
        },
        {
            'name': '推荐特征集',
            'config': {
                'use_enhanced_features': True,
                'feature_selection_strategy': 'recommended',
                'analyze_feature_importance': True
            }
        },
        {
            'name': '最小特征集',
            'config': {
                'use_enhanced_features': True,
                'feature_selection_strategy': 'minimal',
                'analyze_feature_importance': False
            }
        },
        {
            'name': '增强特征集',
            'config': {
                'use_enhanced_features': True,
                'feature_selection_strategy': 'enhanced',
                'analyze_feature_importance': True
            }
        },
        {
            'name': '自定义特征',
            'config': {
                'use_enhanced_features': True,
                'feature_selection_strategy': 'custom',
                'analyze_feature_importance': True,
                'selected_features': ['bb_percent_b', 'atr_atr', 'stoch_stoch_k']
            }
        }
    ]
    
    results = []
    
    for test_config in test_configs:
        print(f"\n🔧 测试配置: {test_config['name']}")
        
        # 构建API请求数据
        api_data = {
            'model_id': model_id,
            'symbol': 'XAUUSD',
            'timeframe': 'H1',
            'inference_mode': 'realtime',
            'data_points': 50,
            'use_gpu': True,
            'show_confidence': True,
            **test_config['config']  # 合并增强特征配置
        }
        
        print(f"   📊 请求配置: {json.dumps(test_config['config'], indent=2)}")
        
        try:
            # 发送推理请求
            response = session.post(
                'http://127.0.0.1:5000/api/deep-learning/inference',
                json=api_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    print(f"   ✅ 推理成功")
                    
                    # 检查返回结果中的增强特征信息
                    predictions = result.get('predictions', [])
                    if predictions:
                        first_prediction = predictions[0]
                        
                        # 检查增强特征标记
                        enhanced_used = first_prediction.get('enhanced_features_used', False)
                        feature_strategy = first_prediction.get('feature_strategy', 'unknown')
                        feature_type = first_prediction.get('feature_type', 'unknown')
                        feature_count = first_prediction.get('feature_count', 'unknown')
                        
                        print(f"   📊 增强特征使用: {enhanced_used}")
                        print(f"   📊 特征策略: {feature_strategy}")
                        print(f"   📊 特征类型: {feature_type}")
                        print(f"   📊 特征数量: {feature_count}")
                        
                        # 检查特征重要性分析
                        if result.get('feature_importance'):
                            print(f"   📊 特征重要性分析: 已提供")
                        else:
                            print(f"   📊 特征重要性分析: 未提供")
                        
                        results.append({
                            'config_name': test_config['name'],
                            'success': True,
                            'enhanced_used': enhanced_used,
                            'feature_strategy': feature_strategy,
                            'feature_type': feature_type,
                            'feature_count': feature_count,
                            'has_importance': bool(result.get('feature_importance'))
                        })
                    else:
                        print(f"   ⚠️ 推理成功但无预测结果")
                        results.append({
                            'config_name': test_config['name'],
                            'success': True,
                            'enhanced_used': False,
                            'note': 'no_predictions'
                        })
                else:
                    print(f"   ❌ 推理失败: {result.get('error', 'unknown')}")
                    results.append({
                        'config_name': test_config['name'],
                        'success': False,
                        'error': result.get('error', 'unknown')
                    })
            else:
                print(f"   ❌ API调用失败: {response.status_code}")
                results.append({
                    'config_name': test_config['name'],
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                })
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            results.append({
                'config_name': test_config['name'],
                'success': False,
                'error': str(e)
            })
        
        # 等待一下避免请求过快
        time.sleep(2)
    
    return results

def analyze_test_results(results):
    """分析测试结果"""
    print('\n🔍 分析测试结果')
    print('=' * 60)
    
    if not results:
        print("❌ 没有测试结果")
        return
    
    print("📊 测试结果汇总:")
    
    success_count = 0
    enhanced_working_count = 0
    
    for result in results:
        config_name = result['config_name']
        success = result['success']
        
        if success:
            success_count += 1
            enhanced_used = result.get('enhanced_used', False)
            feature_strategy = result.get('feature_strategy', 'unknown')
            feature_type = result.get('feature_type', 'unknown')
            feature_count = result.get('feature_count', 'unknown')
            
            print(f"\n✅ {config_name}:")
            print(f"   推理状态: 成功")
            print(f"   增强特征: {enhanced_used}")
            print(f"   特征策略: {feature_strategy}")
            print(f"   特征类型: {feature_type}")
            print(f"   特征数量: {feature_count}")
            
            if enhanced_used:
                enhanced_working_count += 1
        else:
            error = result.get('error', 'unknown')
            print(f"\n❌ {config_name}:")
            print(f"   推理状态: 失败")
            print(f"   错误信息: {error}")
    
    print(f"\n📈 统计结果:")
    print(f"   总测试数: {len(results)}")
    print(f"   成功推理: {success_count}/{len(results)}")
    print(f"   增强特征工作: {enhanced_working_count}/{len(results)}")
    
    # 分析结果
    if success_count == 0:
        print(f"\n❌ 所有测试都失败，可能是系统问题")
    elif enhanced_working_count == 0:
        print(f"\n⚠️ 推理成功但增强特征未生效")
    elif enhanced_working_count > 0:
        print(f"\n✅ 增强特征部分或完全工作")
    
    return {
        'total': len(results),
        'success': success_count,
        'enhanced_working': enhanced_working_count
    }

def provide_diagnosis_and_recommendations(stats):
    """提供诊断和建议"""
    print('\n💡 诊断和建议')
    print('=' * 60)
    
    if not stats:
        print("❌ 无法提供诊断，缺少统计数据")
        return
    
    total = stats['total']
    success = stats['success']
    enhanced_working = stats['enhanced_working']
    
    print("🔍 诊断结果:")
    
    if success == 0:
        print("❌ 系统级问题:")
        print("   - Flask应用可能未运行")
        print("   - 模型文件可能损坏或缺失")
        print("   - 数据库连接问题")
        print("   - 深度学习服务异常")
        
        print("\n🔧 建议解决方案:")
        print("   1. 检查Flask应用是否正常运行")
        print("   2. 验证模型文件是否存在且完整")
        print("   3. 检查数据库连接")
        print("   4. 查看服务器日志获取详细错误信息")
    
    elif enhanced_working == 0:
        print("⚠️ 增强特征功能问题:")
        print("   - 增强特征配置未正确传递")
        print("   - 特征计算逻辑有问题")
        print("   - 模型兼容性问题")
        print("   - 特征标记逻辑错误")
        
        print("\n🔧 建议解决方案:")
        print("   1. 检查前端配置收集是否正确")
        print("   2. 验证路由层参数传递")
        print("   3. 检查服务层特征计算逻辑")
        print("   4. 确认模型兼容性处理")
        print("   5. 添加详细日志跟踪数据流")
    
    elif enhanced_working < total:
        print("⚠️ 部分增强特征功能问题:")
        print("   - 某些特征策略可能有问题")
        print("   - 特定配置组合可能不兼容")
        print("   - 模型对某些特征集不支持")
        
        print("\n🔧 建议解决方案:")
        print("   1. 分析哪些配置失败，哪些成功")
        print("   2. 检查失败配置的特定问题")
        print("   3. 验证模型对不同特征集的支持")
        print("   4. 优化特征策略实现")
    
    else:
        print("✅ 增强特征功能正常:")
        print("   - 所有测试配置都成功")
        print("   - 增强特征正确输入到模型")
        print("   - 特征策略切换正常")
        print("   - 配置传递完整")
        
        print("\n🎉 功能验证成功:")
        print("   1. 增强特征配置正确收集")
        print("   2. API参数正确传递")
        print("   3. 服务层正确处理")
        print("   4. 特征计算正常工作")
        print("   5. 模型输入维度匹配")

def main():
    """主函数"""
    print('🔧 增强特征实际输入测试')
    print('=' * 80)
    
    print("🎯 测试目标:")
    print("   验证增强特征配置是否真正输入到模型")
    print("   检查不同特征策略的工作状态")
    print("   确认整个数据流的完整性")
    
    # 执行API测试
    results = test_enhanced_features_api_call()
    
    if results:
        # 分析结果
        stats = analyze_test_results(results)
        
        # 提供诊断和建议
        provide_diagnosis_and_recommendations(stats)
    else:
        print("\n❌ 测试执行失败")
    
    print(f"\n🎯 总结")
    print('=' * 80)
    print("✅ 增强特征输入测试完成")
    print("\n💡 关键检查点:")
    print("   1. API调用是否成功")
    print("   2. 增强特征标记是否正确")
    print("   3. 特征策略是否生效")
    print("   4. 特征数量是否匹配")
    print("   5. 特征重要性分析是否提供")
    
    print(f"\n🚀 下一步:")
    print("   根据测试结果进行相应的修复或优化")
    print("   如果测试成功，说明增强特征功能正常工作")
    print("   如果测试失败，需要根据诊断结果进行修复")

if __name__ == "__main__":
    main()
