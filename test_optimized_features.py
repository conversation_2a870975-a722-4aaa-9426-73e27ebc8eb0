#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的特征处理功能
验证基础特征和增强特征的正确组合
"""

import requests
import time
import json

def login_session():
    """登录并返回会话"""
    session = requests.Session()
    
    try:
        response = session.post('http://127.0.0.1:5000/login', 
                               data={'username': 'admin', 'password': 'admin123'})
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_optimized_feature_combinations():
    """测试优化后的特征组合"""
    print('🧪 测试优化后的特征组合')
    print('=' * 50)
    
    session = login_session()
    if not session:
        return False
    
    # 测试配置
    test_configs = [
        {
            'name': '仅基础特征模式（兼容性测试）',
            'config': {
                'model_name': f'基础特征兼容性测试_{int(time.time())}',
                'model_type': 'lstm',
                'symbol': 'XAUUSD',
                'timeframe': 'H1',
                'data_config': {
                    'mode': 'days',
                    'training_days': 30
                },
                'sequence_length': 20,
                'hidden_size': 64,
                'num_layers': 2,
                'dropout': 0.2,
                'batch_size': 16,
                'learning_rate': 0.001,
                'epochs': 5,
                'patience': 3,
                'early_stopping': True,
                'min_epochs': 2,
                'use_gpu': True,
                'save_checkpoints': True,
                # 仅基础特征
                'use_enhanced_features': False,
                'features': {
                    'price': True,
                    'volume': True,
                    'technical': True,
                    'time': True
                }
            },
            'expected_features': 8,
            'description': '8个基础特征（OHLCV + SMA_5 + SMA_20 + RSI）'
        },
        {
            'name': '基础+推荐增强特征模式',
            'config': {
                'model_name': f'基础+推荐增强特征测试_{int(time.time())}',
                'model_type': 'lstm',
                'symbol': 'XAUUSD',
                'timeframe': 'H1',
                'data_config': {
                    'mode': 'days',
                    'training_days': 30
                },
                'sequence_length': 20,
                'hidden_size': 64,
                'num_layers': 2,
                'dropout': 0.2,
                'batch_size': 16,
                'learning_rate': 0.001,
                'epochs': 5,
                'patience': 3,
                'early_stopping': True,
                'min_epochs': 2,
                'use_gpu': True,
                'save_checkpoints': True,
                # 基础特征 + 推荐增强特征
                'use_enhanced_features': True,
                'feature_selection_strategy': 'recommended',
                'include_basic_features': True,
                'analyze_feature_importance': True
            },
            'expected_features': 39,  # 8 + 31
            'description': '8个基础特征 + 31个互补增强特征 = 39个特征'
        },
        {
            'name': '基础+自定义增强特征模式',
            'config': {
                'model_name': f'基础+自定义增强特征测试_{int(time.time())}',
                'model_type': 'lstm',
                'symbol': 'XAUUSD',
                'timeframe': 'H1',
                'data_config': {
                    'mode': 'days',
                    'training_days': 30
                },
                'sequence_length': 20,
                'hidden_size': 64,
                'num_layers': 2,
                'dropout': 0.2,
                'batch_size': 16,
                'learning_rate': 0.001,
                'epochs': 5,
                'patience': 3,
                'early_stopping': True,
                'min_epochs': 2,
                'use_gpu': True,
                'save_checkpoints': True,
                # 基础特征 + 自定义增强特征
                'use_enhanced_features': True,
                'feature_selection_strategy': 'custom',
                'include_basic_features': True,
                'analyze_feature_importance': True,
                'selected_features': [
                    'bb_percent_b', 'bb_squeeze', 'bb_breakout',
                    'atr_atr', 'atr_ratio', 'atr_low_volatility',
                    'stoch_stoch_k', 'stoch_overbought', 'stoch_oversold'
                ]
            },
            'expected_features': 17,  # 8 + 9
            'description': '8个基础特征 + 9个自定义增强特征 = 17个特征'
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_configs, 1):
        print(f"\n📊 测试 {i}: {test_case['name']}")
        print(f"   预期特征数: {test_case['expected_features']}")
        print(f"   描述: {test_case['description']}")
        
        try:
            response = session.post('http://127.0.0.1:5000/api/deep-learning/start-data-preparation',
                                   json=test_case['config'],
                                   headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    task_id = result.get('task_id')
                    print(f"   ✅ 启动成功: {task_id}")
                    
                    # 等待数据准备完成
                    print(f"   ⏳ 等待数据准备完成...")
                    time.sleep(10)
                    
                    # 检查数据准备结果
                    progress_response = session.get(f'http://127.0.0.1:5000/api/deep-learning/training-progress/{task_id}')
                    
                    if progress_response.status_code == 200:
                        progress_data = progress_response.json()
                        if progress_data.get('success'):
                            progress = progress_data.get('progress', {})
                            logs = progress.get('logs')
                            
                            if logs:
                                try:
                                    log_data = json.loads(logs)
                                    data_info = log_data.get('data_info', {})
                                    
                                    if 'X_train_shape' in data_info:
                                        actual_features = data_info['X_train_shape'][2]  # [samples, sequence, features]
                                        print(f"   📊 实际特征数: {actual_features}")
                                        print(f"   📋 数据形状: {data_info['X_train_shape']}")
                                        
                                        # 验证特征数量
                                        if actual_features == test_case['expected_features']:
                                            print(f"   ✅ 特征数量验证通过")
                                            results.append({
                                                'name': test_case['name'],
                                                'status': 'success',
                                                'expected': test_case['expected_features'],
                                                'actual': actual_features,
                                                'task_id': task_id
                                            })
                                        else:
                                            print(f"   ❌ 特征数量不匹配: 预期{test_case['expected_features']}, 实际{actual_features}")
                                            results.append({
                                                'name': test_case['name'],
                                                'status': 'mismatch',
                                                'expected': test_case['expected_features'],
                                                'actual': actual_features,
                                                'task_id': task_id
                                            })
                                    else:
                                        print(f"   ⚠️ 数据准备尚未完成")
                                        results.append({
                                            'name': test_case['name'],
                                            'status': 'pending',
                                            'task_id': task_id
                                        })
                                except Exception as e:
                                    print(f"   ❌ 日志解析失败: {e}")
                                    results.append({
                                        'name': test_case['name'],
                                        'status': 'error',
                                        'error': str(e),
                                        'task_id': task_id
                                    })
                            else:
                                print(f"   ⚠️ 暂无日志信息")
                                results.append({
                                    'name': test_case['name'],
                                    'status': 'no_logs',
                                    'task_id': task_id
                                })
                        else:
                            print(f"   ❌ 获取进度失败: {progress_data.get('error')}")
                    else:
                        print(f"   ❌ 进度请求失败: {progress_response.status_code}")
                else:
                    print(f"   ❌ 启动失败: {result.get('error')}")
                    results.append({
                        'name': test_case['name'],
                        'status': 'failed',
                        'error': result.get('error')
                    })
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                results.append({
                    'name': test_case['name'],
                    'status': 'request_failed',
                    'status_code': response.status_code
                })
                
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results.append({
                'name': test_case['name'],
                'status': 'exception',
                'error': str(e)
            })
        
        # 等待一下再进行下一个测试
        time.sleep(2)
    
    # 总结测试结果
    print(f"\n🎯 测试结果总结")
    print('=' * 50)
    
    success_count = 0
    for result in results:
        status_icon = '✅' if result['status'] == 'success' else '❌'
        print(f"{status_icon} {result['name']}: {result['status']}")
        
        if result['status'] == 'success':
            print(f"   特征数量: {result['actual']} (预期: {result['expected']})")
            success_count += 1
        elif result['status'] == 'mismatch':
            print(f"   特征数量不匹配: {result['actual']} (预期: {result['expected']})")
        elif 'error' in result:
            print(f"   错误: {result['error']}")
    
    print(f"\n📊 测试统计:")
    print(f"   总测试数: {len(results)}")
    print(f"   成功数: {success_count}")
    print(f"   失败数: {len(results) - success_count}")
    print(f"   成功率: {success_count/len(results)*100:.1f}%")
    
    return success_count == len(results)

def main():
    """主函数"""
    print('🔧 优化特征处理功能测试')
    print('=' * 80)
    
    success = test_optimized_feature_combinations()
    
    if success:
        print(f"\n🎉 优化特征处理功能测试成功！")
        print(f"💡 验证结果:")
        print(f"   ✅ 基础特征兼容性正常")
        print(f"   ✅ 基础+增强特征组合正确")
        print(f"   ✅ 特征数量计算准确")
        print(f"   ✅ 避免了重复特征问题")
    else:
        print(f"\n❌ 优化特征处理功能测试失败")
        print(f"🔧 需要进一步检查实现")

if __name__ == "__main__":
    main()
