#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试修复结果
"""

import requests
import time
import subprocess
import os

def start_app():
    """启动应用"""
    print("🚀 启动应用...")
    
    try:
        # 杀死现有进程
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                     capture_output=True, text=True)
        time.sleep(2)
    except:
        pass
    
    # 启动新进程
    process = subprocess.Popen(['python', 'app.py'], 
                             cwd=os.getcwd(),
                             stdout=subprocess.PIPE, 
                             stderr=subprocess.PIPE)
    
    # 等待应用启动
    print("⏳ 等待应用启动...")
    time.sleep(8)
    
    return process

def test_fixes():
    """测试修复结果"""
    print("🧪 测试修复结果")
    print("=" * 40)
    
    try:
        # 测试应用响应
        response = requests.get('http://127.0.0.1:5000', timeout=10)
        print(f"应用状态: {response.status_code}")
        
        if response.status_code != 200:
            print("❌ 应用未正常运行")
            return False
        
        # 登录
        session = requests.Session()
        login_response = session.post('http://127.0.0.1:5000/login', 
                                    data={'username': 'admin', 'password': 'admin123'})
        
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code}")
            return False
        
        print("✅ 登录成功")
        
        # 测试AI推理交易页面
        print(f"\n🔍 测试AI推理交易页面...")
        inference_response = session.get('http://127.0.0.1:5000/deep-learning/inference')
        
        if inference_response.status_code != 200:
            print(f"❌ AI推理交易页面访问失败: {inference_response.status_code}")
            return False
        
        content = inference_response.text
        print(f"✅ AI推理交易页面访问成功，大小: {len(content):,} 字符")
        
        # 检查修复1: JavaScript错误修复
        print(f"\n🔍 检查JavaScript错误修复...")
        
        if 'null.addEventListener' in content:
            print("❌ null.addEventListener错误仍然存在")
            return False
        else:
            print("✅ null.addEventListener错误已修复")
        
        # 检查修复2: 交易模型选择
        print(f"\n🔍 检查交易模型选择...")
        
        if 'id="tradingModelSelect"' in content:
            print("✅ 交易模型选择存在")
        else:
            print("❌ 交易模型选择缺失")
            return False
        
        if 'loadTradingModels()' in content:
            print("✅ 交易模型加载函数调用存在")
        else:
            print("❌ 交易模型加载函数调用缺失")
        
        # 检查修复3: MT5连接功能
        print(f"\n🔍 检查MT5连接功能...")
        
        if 'checkMT5Connection()' in content:
            print("✅ MT5连接检查函数调用存在")
        else:
            print("❌ MT5连接检查函数调用缺失")
            return False
        
        if 'onclick="checkMT5Connection()"' in content:
            print("✅ MT5连接检查按钮存在")
        else:
            print("❌ MT5连接检查按钮缺失")
        
        if 'onclick="autoConnectMT5()"' in content:
            print("✅ MT5自动连接按钮存在")
        else:
            print("❌ MT5自动连接按钮缺失")
        
        # 检查修复4: 增强特征选项
        print(f"\n🔍 检查增强特征选项...")
        
        if 'id="enableEnhancedFeatures"' in content:
            print("✅ 增强特征选项存在")
        else:
            print("❌ 增强特征选项缺失")
        
        # 测试API端点
        print(f"\n🔍 测试API端点...")
        
        # 测试模型列表API
        try:
            models_response = session.get('http://127.0.0.1:5000/api/deep-learning/models')
            if models_response.status_code == 200:
                models_data = models_response.json()
                if models_data.get('success'):
                    models = models_data.get('models', [])
                    completed_models = [m for m in models if m.get('status') == 'completed']
                    print(f"✅ 模型列表API正常，找到 {len(completed_models)} 个完成的模型")
                else:
                    print(f"⚠️ 模型列表API返回错误: {models_data.get('error')}")
            else:
                print(f"❌ 模型列表API访问失败: {models_response.status_code}")
        except Exception as e:
            print(f"❌ 模型列表API异常: {e}")
        
        # 测试MT5连接状态API
        try:
            mt5_response = session.get('http://127.0.0.1:5000/api/mt5/connection-status')
            if mt5_response.status_code == 200:
                mt5_data = mt5_response.json()
                print(f"✅ MT5连接状态API正常，连接状态: {mt5_data.get('connected', False)}")
            else:
                print(f"❌ MT5连接状态API访问失败: {mt5_response.status_code}")
        except Exception as e:
            print(f"❌ MT5连接状态API异常: {e}")
        
        # 保存页面内容用于调试
        with open('final_test_page.html', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"💾 页面内容已保存到: final_test_page.html")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 AI推理交易MT5连接和模型选择问题修复验证")
    print("=" * 60)
    
    # 启动应用
    process = start_app()
    
    try:
        # 测试修复结果
        success = test_fixes()
        
        if success:
            print("\n🎉 所有修复验证通过！")
            print("📋 修复总结:")
            print("   1. ✅ JavaScript null.addEventListener错误已修复")
            print("   2. ✅ 交易模型选择功能完整")
            print("   3. ✅ MT5连接功能正常")
            print("   4. ✅ 增强特征选项已添加")
            print("   5. ✅ API端点正常工作")
            print("\n🔄 请刷新浏览器页面测试功能")
            print("🌐 访问: http://127.0.0.1:5000/deep-learning/inference")
        else:
            print("\n⚠️ 部分修复验证失败")
            print("🔧 请检查上述错误信息")
        
        return success
        
    finally:
        # 保持应用运行
        print(f"\n📝 应用继续运行中...")
    
if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
