#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标在深度学习模型训练中的应用演示
展示布林带等技术指标如何计算并输入到模型中
"""

import numpy as np
import pandas as pd
from services.technical_indicators import TechnicalIndicators
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

class TechnicalIndicatorsForML:
    """为机器学习模型准备技术指标的类"""
    
    def __init__(self):
        self.ti = TechnicalIndicators()
    
    def prepare_price_data(self, symbol='XAUUSD', days=100):
        """
        准备价格数据（模拟MT5数据格式）
        实际应用中这里会从MT5获取真实数据
        """
        print("📊 准备价格数据...")
        
        # 模拟价格数据 [open, high, low, close, volume]
        np.random.seed(42)  # 确保可重复性
        
        # 生成基础价格走势
        base_price = 2000.0  # 黄金基础价格
        price_changes = np.random.normal(0, 10, days)  # 价格变化
        prices = [base_price]
        
        for change in price_changes:
            new_price = prices[-1] + change
            prices.append(max(new_price, 1800))  # 设置最低价格
        
        prices = np.array(prices[1:])  # 去掉初始价格
        
        # 生成OHLC数据
        data = []
        for i, close in enumerate(prices):
            # 生成合理的OHLC数据
            volatility = np.random.uniform(5, 20)
            high = close + np.random.uniform(0, volatility)
            low = close - np.random.uniform(0, volatility)
            open_price = low + np.random.uniform(0, high - low)
            volume = np.random.uniform(1000, 10000)
            
            data.append([open_price, high, low, close, volume])
        
        price_data = np.array(data)
        
        print(f"✅ 生成了 {len(price_data)} 条价格数据")
        print(f"   价格范围: {price_data[:, 3].min():.2f} - {price_data[:, 3].max():.2f}")
        
        return price_data
    
    def calculate_all_technical_indicators(self, price_data):
        """
        计算所有技术指标
        这是深度学习模型训练时的核心特征工程步骤
        """
        print("\n🔧 计算技术指标...")
        
        # 转换为pandas Series以便计算
        df = pd.DataFrame(price_data, columns=['open', 'high', 'low', 'close', 'volume'])
        
        indicators = {}
        
        # 1. 布林带 (Bollinger Bands) - 重点展示
        print("📈 计算布林带...")
        bb = self.ti.bollinger_bands(df['close'], period=20, std_dev=2)
        indicators['bb_upper'] = bb['upper']
        indicators['bb_middle'] = bb['middle']
        indicators['bb_lower'] = bb['lower']
        
        # 布林带相关特征
        indicators['bb_width'] = bb['upper'] - bb['lower']  # 带宽
        indicators['bb_position'] = (df['close'] - bb['lower']) / (bb['upper'] - bb['lower'])  # 价格在带中的位置
        
        print(f"   布林带上轨范围: {bb['upper'].min():.2f} - {bb['upper'].max():.2f}")
        print(f"   布林带下轨范围: {bb['lower'].min():.2f} - {bb['lower'].max():.2f}")
        
        # 2. MACD
        print("📊 计算MACD...")
        macd = self.ti.macd(df['close'])
        indicators['macd_line'] = macd['macd']
        indicators['macd_signal'] = macd['signal']
        indicators['macd_histogram'] = macd['histogram']
        
        # 3. RSI
        print("📉 计算RSI...")
        indicators['rsi'] = self.ti.rsi(df['close'])
        
        # 4. 移动平均线
        print("📈 计算移动平均线...")
        indicators['sma_5'] = self.ti.sma(df['close'], 5)
        indicators['sma_20'] = self.ti.sma(df['close'], 20)
        indicators['sma_50'] = self.ti.sma(df['close'], 50)
        indicators['ema_12'] = self.ti.ema(df['close'], 12)
        indicators['ema_26'] = self.ti.ema(df['close'], 26)
        
        # 5. 随机指标
        print("🎯 计算随机指标...")
        stoch = self.ti.stochastic(df['high'], df['low'], df['close'])
        indicators['stoch_k'] = stoch['%K']
        indicators['stoch_d'] = stoch['%D']
        
        # 6. ATR (平均真实波幅)
        print("📏 计算ATR...")
        indicators['atr'] = self.ti.atr(df['high'], df['low'], df['close'])
        
        # 7. ADX (平均趋向指数)
        print("🧭 计算ADX...")
        adx = self.ti.adx(df['high'], df['low'], df['close'])
        indicators['adx'] = adx['ADX']
        indicators['di_plus'] = adx['DI+']
        indicators['di_minus'] = adx['DI-']
        
        # 8. 威廉指标
        print("⚡ 计算威廉指标...")
        indicators['williams_r'] = self.ti.williams_r(df['high'], df['low'], df['close'])
        
        # 9. CCI (商品通道指数)
        print("🌊 计算CCI...")
        indicators['cci'] = self.ti.cci(df['high'], df['low'], df['close'])
        
        # 10. 成交量指标
        if 'volume' in df.columns:
            print("📦 计算成交量指标...")
            indicators['obv'] = self.ti.obv(df['close'], df['volume'])
        
        print(f"✅ 计算完成，共生成 {len(indicators)} 个技术指标")
        
        return indicators
    
    def prepare_features_for_model(self, price_data, indicators):
        """
        为深度学习模型准备特征矩阵
        这是模型训练时的关键步骤
        """
        print("\n🤖 为模型准备特征...")
        
        # 基础价格特征
        price_features = []
        
        # 1. 原始价格特征 (标准化)
        for col_name in ['open', 'high', 'low', 'close']:
            col_idx = ['open', 'high', 'low', 'close', 'volume'].index(col_name)
            feature = price_data[:, col_idx]
            # 标准化
            normalized = (feature - feature.mean()) / (feature.std() + 1e-8)
            price_features.append(normalized.reshape(-1, 1))
        
        # 2. 价格变化特征
        close_prices = price_data[:, 3]
        price_change = np.diff(close_prices, prepend=close_prices[0])
        price_change_pct = price_change / (close_prices + 1e-8)
        price_features.append(price_change_pct.reshape(-1, 1))
        
        # 3. 技术指标特征
        technical_features = []
        
        # 布林带特征 (重点)
        bb_features = [
            'bb_upper', 'bb_middle', 'bb_lower', 'bb_width', 'bb_position'
        ]
        
        print("📈 处理布林带特征...")
        for feature_name in bb_features:
            if feature_name in indicators:
                feature_data = indicators[feature_name].fillna(0).values
                # 标准化
                normalized = (feature_data - feature_data.mean()) / (feature_data.std() + 1e-8)
                technical_features.append(normalized.reshape(-1, 1))
                print(f"   {feature_name}: 范围 {normalized.min():.3f} - {normalized.max():.3f}")
        
        # 其他技术指标
        other_indicators = [
            'macd_line', 'macd_signal', 'macd_histogram',
            'rsi', 'sma_5', 'sma_20', 'ema_12', 'ema_26',
            'stoch_k', 'stoch_d', 'atr', 'adx', 'williams_r', 'cci'
        ]
        
        print("📊 处理其他技术指标...")
        for feature_name in other_indicators:
            if feature_name in indicators:
                feature_data = indicators[feature_name].fillna(0).values
                # 标准化
                normalized = (feature_data - feature_data.mean()) / (feature_data.std() + 1e-8)
                technical_features.append(normalized.reshape(-1, 1))
        
        # 4. 合并所有特征
        all_features = price_features + technical_features
        feature_matrix = np.concatenate(all_features, axis=1)
        
        print(f"✅ 特征矩阵准备完成:")
        print(f"   形状: {feature_matrix.shape}")
        print(f"   价格特征: {len(price_features)} 个")
        print(f"   技术指标特征: {len(technical_features)} 个")
        print(f"   总特征数: {feature_matrix.shape[1]} 个")
        
        return feature_matrix
    
    def create_sequences_for_lstm(self, feature_matrix, sequence_length=60):
        """
        为LSTM模型创建序列数据
        这是时间序列深度学习的标准做法
        """
        print(f"\n🔄 创建LSTM序列 (序列长度: {sequence_length})...")
        
        sequences = []
        targets = []
        
        for i in range(sequence_length, len(feature_matrix)):
            # 输入序列：过去sequence_length个时间步的所有特征
            sequence = feature_matrix[i-sequence_length:i]
            sequences.append(sequence)
            
            # 目标：下一个时间步的收盘价变化方向 (简化的分类任务)
            current_close = feature_matrix[i-1, 3]  # 当前收盘价特征
            next_close = feature_matrix[i, 3]       # 下一个收盘价特征
            
            # 1: 上涨, 0: 下跌
            target = 1 if next_close > current_close else 0
            targets.append(target)
        
        X = np.array(sequences)
        y = np.array(targets)
        
        print(f"✅ 序列创建完成:")
        print(f"   输入形状: {X.shape} (样本数, 序列长度, 特征数)")
        print(f"   目标形状: {y.shape}")
        print(f"   上涨样本: {np.sum(y)} ({np.mean(y)*100:.1f}%)")
        print(f"   下跌样本: {len(y) - np.sum(y)} ({(1-np.mean(y))*100:.1f}%)")
        
        return X, y
    
    def demonstrate_complete_pipeline(self):
        """演示完整的技术指标到模型输入的流程"""
        print("🚀 技术指标深度学习应用完整演示")
        print("=" * 60)
        
        # 1. 准备价格数据
        price_data = self.prepare_price_data(days=200)
        
        # 2. 计算技术指标
        indicators = self.calculate_all_technical_indicators(price_data)
        
        # 3. 准备模型特征
        feature_matrix = self.prepare_features_for_model(price_data, indicators)
        
        # 4. 创建LSTM序列
        X, y = self.create_sequences_for_lstm(feature_matrix)
        
        # 5. 展示布林带的具体应用
        self.demonstrate_bollinger_bands_usage(price_data, indicators)
        
        print("\n🎉 演示完成！")
        print("=" * 60)
        print("📋 总结:")
        print(f"   • 原始价格数据: {price_data.shape}")
        print(f"   • 技术指标数量: {len(indicators)}")
        print(f"   • 模型特征矩阵: {feature_matrix.shape}")
        print(f"   • LSTM输入序列: {X.shape}")
        print(f"   • 预测目标: {y.shape}")
        
        return X, y, feature_matrix, indicators
    
    def demonstrate_bollinger_bands_usage(self, price_data, indicators):
        """详细演示布林带在模型中的应用"""
        print("\n🎯 布林带详细应用演示")
        print("-" * 40)
        
        close_prices = price_data[:, 3]
        bb_upper = indicators['bb_upper'].values
        bb_middle = indicators['bb_middle'].values
        bb_lower = indicators['bb_lower'].values
        bb_position = indicators['bb_position'].values
        
        # 分析布林带信号
        print("📊 布林带信号分析:")
        
        # 突破上轨信号
        upper_breakout = close_prices > bb_upper
        print(f"   突破上轨次数: {np.sum(upper_breakout)} ({np.mean(upper_breakout)*100:.1f}%)")
        
        # 跌破下轨信号
        lower_breakout = close_prices < bb_lower
        print(f"   跌破下轨次数: {np.sum(lower_breakout)} ({np.mean(lower_breakout)*100:.1f}%)")
        
        # 中轨附近
        near_middle = np.abs(close_prices - bb_middle) < (bb_upper - bb_lower) * 0.1
        print(f"   中轨附近次数: {np.sum(near_middle)} ({np.mean(near_middle)*100:.1f}%)")
        
        # 布林带宽度分析
        bb_width = bb_upper - bb_lower
        print(f"   平均带宽: {np.mean(bb_width):.2f}")
        print(f"   带宽标准差: {np.std(bb_width):.2f}")
        
        # 价格在布林带中的位置分布
        print(f"   价格位置分布:")
        print(f"     下轨附近 (0-0.2): {np.mean(bb_position < 0.2)*100:.1f}%")
        print(f"     中轨附近 (0.4-0.6): {np.mean((bb_position >= 0.4) & (bb_position <= 0.6))*100:.1f}%")
        print(f"     上轨附近 (0.8-1.0): {np.mean(bb_position > 0.8)*100:.1f}%")

    def show_actual_deep_learning_integration(self):
        """展示实际深度学习服务中的集成方式"""
        print("\n🔗 实际深度学习服务集成展示")
        print("=" * 50)

        print("📝 在deep_learning_service.py中的实际实现:")

        # 展示实际代码结构
        code_example = '''
# 1. 特征计算函数 (在_calculate_features_from_dict中)
def calculate_technical_indicators(price_data):
    """计算技术指标特征"""

    # 布林带计算
    close_prices = price_data[:, 3]  # 收盘价
    sma_20 = np.convolve(close_prices, np.ones(20)/20, mode='same')
    std_20 = pd.Series(close_prices).rolling(20).std().fillna(0).values

    bb_upper = sma_20 + (2 * std_20)
    bb_lower = sma_20 - (2 * std_20)
    bb_position = (close_prices - bb_lower) / (bb_upper - bb_lower + 1e-10)

    # MACD计算
    ema_12 = pd.Series(close_prices).ewm(span=12).mean().values
    ema_26 = pd.Series(close_prices).ewm(span=26).mean().values
    macd_line = ema_12 - ema_26
    macd_signal = pd.Series(macd_line).ewm(span=9).mean().values

    # RSI计算
    price_changes = np.diff(close_prices, prepend=close_prices[0])
    gains = np.where(price_changes > 0, price_changes, 0)
    losses = np.where(price_changes < 0, -price_changes, 0)

    avg_gains = pd.Series(gains).rolling(14).mean().fillna(0).values
    avg_losses = pd.Series(losses).rolling(14).mean().fillna(0).values

    rs = avg_gains / (avg_losses + 1e-10)
    rsi = 100 - (100 / (1 + rs))

    # 组合技术指标特征
    technical_features = np.column_stack([
        bb_upper, bb_lower, bb_position,  # 布林带特征
        macd_line, macd_signal,           # MACD特征
        rsi,                              # RSI特征
        sma_20                            # 移动平均
    ])

    # 标准化
    normalized = (technical_features - technical_features.mean(axis=0)) / (technical_features.std(axis=0) + 1e-10)

    return normalized

# 2. 在模型训练中的使用 (在_prepare_training_data中)
def prepare_model_features(price_data, config):
    """准备模型特征"""

    features = []

    # 基础价格特征
    if config.get('use_price_features', True):
        price_features = price_data[:, [0, 1, 2, 3]]  # OHLC
        normalized_prices = (price_features - price_features.mean(axis=0)) / (price_features.std(axis=0) + 1e-10)
        features.append(normalized_prices)

    # 技术指标特征
    if config.get('use_technical_indicators', True):
        technical_features = calculate_technical_indicators(price_data)
        features.append(technical_features)

    # 合并所有特征
    if features:
        return np.concatenate(features, axis=1)
    else:
        return price_data[:, 3:4]  # 默认只用收盘价

# 3. 创建LSTM序列 (在_create_sequences中)
def create_lstm_sequences(features, sequence_length=60):
    """为LSTM创建序列"""

    X, y = [], []

    for i in range(sequence_length, len(features)):
        # 输入：过去sequence_length个时间步的所有特征
        X.append(features[i-sequence_length:i])

        # 目标：下一个时间步的价格变化方向
        current_price = features[i-1, 3]  # 当前收盘价特征
        next_price = features[i, 3]       # 下一个收盘价特征
        y.append(1 if next_price > current_price else 0)

    return np.array(X), np.array(y)
        '''

        print(code_example)

        print("\n🎯 关键技术要点:")
        print("1. 布林带位置 (bb_position) 是最重要的特征之一")
        print("   - 值接近0：价格在下轨附近，可能超卖")
        print("   - 值接近1：价格在上轨附近，可能超买")
        print("   - 值接近0.5：价格在中轨附近，趋势不明")

        print("\n2. MACD组合特征提供趋势信息")
        print("   - MACD线：快慢EMA差值，反映趋势强度")
        print("   - 信号线：MACD的EMA，提供买卖信号")

        print("\n3. RSI提供超买超卖信号")
        print("   - RSI > 70：可能超买，考虑卖出")
        print("   - RSI < 30：可能超卖，考虑买入")

        print("\n4. 特征标准化确保模型训练稳定")
        print("   - 所有特征都标准化到相似的数值范围")
        print("   - 避免某些特征因数值过大而主导训练")

def main():
    """主演示函数"""
    demo = TechnicalIndicatorsForML()
    X, y, features, indicators = demo.demonstrate_complete_pipeline()

    # 展示实际集成方式
    demo.show_actual_deep_learning_integration()

    print("\n💡 实际应用建议:")
    print("1. 布林带位置特征是强有力的趋势反转信号")
    print("2. 结合多个技术指标可以提高模型预测准确性")
    print("3. 特征标准化对深度学习模型训练至关重要")
    print("4. 序列长度的选择需要平衡信息量和计算效率")
    print("5. 在实际应用中，建议使用更多技术指标组合")

if __name__ == "__main__":
    main()
